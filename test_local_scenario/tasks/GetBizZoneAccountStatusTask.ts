import { Network } from '../helpers/constants'
import { GetBizZoneAccountStatusArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class GetBizZoneAccountStatus extends BaseTask<GetBizZoneAccountStatusArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'getBizZoneAccountStatus')
  }

  protected getDefaultArguments(): Partial<GetBizZoneAccountStatusArguments> {
    return {
      zoneId: '3001',
    }
  }
}
