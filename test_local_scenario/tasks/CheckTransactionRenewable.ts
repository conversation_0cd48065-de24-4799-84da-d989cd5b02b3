import { Network } from '../helpers/constants'
import { CheckTransactionRenewableArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class CheckTransactionRenewableTask extends BaseTask<CheckTransactionRenewableArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'checkTransactionRenewable')
  }

  protected getDefaultArguments(): Partial<CheckTransactionRenewableArguments> {
    return {}
  }
}
