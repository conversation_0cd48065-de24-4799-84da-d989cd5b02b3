import { Network } from '../helpers/constants'
import { RegisterAccArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class RegisterAccTask extends BaseTask<RegisterAccArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'registerAcc')
  }

  protected getDefaultArguments(): Partial<RegisterAccArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      accountKey: commonConfig.KEY_ACCOUNT,
      validId: networkConfig.VALID_ID,
      issuerId: networkConfig.ISSUER_ID,
      issuerKey: commonConfig.KEY_ISSUER,
      flag: '11',
    }
  }
}
