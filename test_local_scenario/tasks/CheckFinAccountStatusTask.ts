import { Network } from '../helpers/constants'
import { CheckFinAccountStatusArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class CheckFinAccountStatusTask extends BaseTask<CheckFinAccountStatusArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'checkFinAccountStatus')
  }

  protected getDefaultArguments(): Partial<CheckFinAccountStatusArguments> {
    return {}
  }
}
