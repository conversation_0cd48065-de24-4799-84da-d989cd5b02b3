import { Network } from '../helpers/constants'
import { CheckExchangeArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class CheckExchangeTask extends BaseTask<CheckExchangeArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'checkExchange')
  }

  protected getDefaultArguments(): Partial<CheckExchangeArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      fromZoneId: networkConfig.ZONE_ID,
      amount: commonConfig.CHARGE_AMOUNT,
    }
  }
}
