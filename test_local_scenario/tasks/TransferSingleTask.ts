import { Network } from '../helpers/constants'
import { TransferSingleArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class TransferSingleTask extends BaseTask<TransferSingleArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'transferSingle')
  }

  protected getDefaultArguments(): Partial<TransferSingleArguments> {
    const commonConfig = this.getCommonConfig()
    return {
      amount: commonConfig.TRANSFER_AMOUNT,
      miscValue1: commonConfig.MISC_VALUE_1,
      miscValue2: commonConfig.MISC_VALUE_2,
      memo: commonConfig.MEMO,
      traceId: commonConfig.TRACE_ID,
    }
  }
}
