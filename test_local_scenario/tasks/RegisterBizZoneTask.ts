import { Network } from '../helpers/constants'
import { RegisterBizZoneArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class RegisterBizZoneTask extends BaseTask<RegisterBizZoneArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'registerBizZone')
  }

  protected getDefaultArguments(): Partial<RegisterBizZoneArguments> {
    return {}
  }
}
