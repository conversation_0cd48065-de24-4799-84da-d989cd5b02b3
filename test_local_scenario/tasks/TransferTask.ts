import { Network } from '../helpers/constants'
import { TransferArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class TransferTask extends BaseTask<TransferArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'transfer')
  }

  protected getDefaultArguments(): Partial<TransferArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      fromZoneId: networkConfig.ZONE_ID,
      amount: commonConfig.CHARGE_AMOUNT,
      timeoutHeight: '1000000',
    }
  }
}
