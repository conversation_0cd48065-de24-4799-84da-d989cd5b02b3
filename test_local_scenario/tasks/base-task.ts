import * as path from 'path'
import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { runTaskAndCaptureOutput } from '../helpers/local_task_runner'
import { BaseTaskArguments } from '../helpers/task-arguments'

abstract class BaseTask<T extends BaseTaskArguments> {
  protected network: Network
  protected taskName: string
  abstract filePath: string

  constructor(network: Network, taskName: string) {
    this.network = network
    this.taskName = taskName
  }

  protected abstract getDefaultArguments(): Partial<T>

  protected getNetworkConfig() {
    return networkConfig[this.network]
  }

  protected getCommonConfig() {
    return commonConfig
  }

  async execute(additionalArguments?: Partial<T>): Promise<string> {
    console.log('Running file:', path.relative(process.cwd(), this.filePath))

    const taskArguments = {
      ...this.getDefaultArguments(),
      ...additionalArguments,
    }

    return await runTaskAndCaptureOutput(this.network, this.taskName, taskArguments)
  }
}

export { BaseTask }
