import { Network } from '../helpers/constants'
import { CheckTransactionArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class CheckTransactionTask extends BaseTask<CheckTransactionArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'checkTransaction')
  }

  protected getDefaultArguments(): Partial<CheckTransactionArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      zoneId: networkConfig.ZONE_ID,
      validId: networkConfig.VALID_ID,
      amount: commonConfig.CHARGE_AMOUNT,
    }
  }
}
