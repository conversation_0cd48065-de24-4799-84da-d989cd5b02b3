import { Network } from '../helpers/constants'
import { RegisterTokenArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class RegisterTokenTask extends BaseTask<RegisterTokenArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'registerToken')
  }

  protected getDefaultArguments(): Partial<RegisterTokenArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      provKey: commonConfig.KEY_PROV,
      tokenId: networkConfig.TOKEN_ID,
      provId: networkConfig.PROV_ID,
      tokenName: networkConfig.TOKEN_NAME,
      symbol: networkConfig.TOKEN_SYMBOL,
    }
  }
}
