import { Network } from '../helpers/constants'
import { RegisterEscrowAccArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class RegisterEscrowAccTask extends BaseTask<RegisterEscrowAccArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'registerEscrowAcc')
  }

  protected getDefaultArguments(): Partial<RegisterEscrowAccArguments> {
    const networkConfig = this.getNetworkConfig()
    return {
      srcZoneId: networkConfig.ZONE_ID,
    }
  }
}
