import { Network } from '../helpers/constants'
import { ForceBurnTokenArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class ForceBurnTokenTask extends BaseTask<ForceBurnTokenArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'forceBurnToken')
  }

  protected getDefaultArguments(): Partial<ForceBurnTokenArguments> {
    const commonConfig = this.getCommonConfig()
    return {
      issuerKey: commonConfig.KEY_ISSUER,
    }
  }
}
