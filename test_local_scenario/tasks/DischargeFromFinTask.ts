import { Network } from '../helpers/constants'
import { DischargeFromFinArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class DischargeFromFinTask extends BaseTask<DischargeFromFinArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'dischargeFromFin')
  }

  protected getDefaultArguments(): Partial<DischargeFromFinArguments> {
    return {}
  }
}
