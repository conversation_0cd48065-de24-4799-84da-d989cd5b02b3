import { Network } from '../helpers/constants'
import { BurnTokenArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class CheckBurnTask extends BaseTask<BurnTokenArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'checkBurn')
  }

  protected getDefaultArguments(): BurnTokenArguments {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      issuerId: networkConfig.ISSUER_ID,
      accountId: networkConfig.ACCOUNT_ID_1,
      amount: commonConfig.BURN_AMOUNT,
      issuerKey: commonConfig.KEY_ISSUER,
    }
  }
}
