import { Network } from '../helpers/constants'
import { RegisterProvArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class RegisterProvTask extends BaseTask<RegisterProvArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'registerProvider')
  }

  protected getDefaultArguments(): Partial<RegisterProvArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      provId: networkConfig.PROV_ID,
      zoneId: networkConfig.ZONE_ID,
      zoneName: networkConfig.ZONE_NAME,
      provKey: commonConfig.KEY_PROV,
      flag: '11',
    }
  }
}
