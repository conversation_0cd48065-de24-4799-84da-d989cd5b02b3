import { Network } from '../helpers/constants'
import { CheckApproveArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class CheckApproveTask extends BaseTask<CheckApproveArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'checkApprove')
  }

  protected getDefaultArguments(): Partial<CheckApproveArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      validId: networkConfig.VALID_ID,
      ownerId: networkConfig.ACCOUNT_ID_1,
      spenderId: networkConfig.ACCOUNT_ID_2,
      amount: '999999',
      validKey: commonConfig.KEY_VALID,
    }
  }
}
