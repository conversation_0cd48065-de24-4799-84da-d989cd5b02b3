import { BaseTask } from './base-task'
import { MintTokenArguments } from '../helpers/task-arguments'
import { Network } from '../helpers/constants'

export class CheckMintTask extends BaseTask<MintTokenArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'checkMint')
  }

  protected getDefaultArguments(): MintTokenArguments {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      issuerId: networkConfig.ISSUER_ID,
      accountId: networkConfig.ACCOUNT_ID_1,
      amount: commonConfig.MINT_AMOUNT,
      issuerKey: commonConfig.KEY_ISSUER,
    }
  }
}
