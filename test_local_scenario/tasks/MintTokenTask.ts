import { Network } from '../helpers/constants'
import { MintTokenArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class MintTokenTask extends BaseTask<MintTokenArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'mintToken')
  }

  protected getDefaultArguments(): Partial<MintTokenArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      issuerId: networkConfig.ISSUER_ID,
      amount: commonConfig.MINT_AMOUNT,
      issuerKey: commonConfig.KEY_ISSUER,
    }
  }
}
