import { BaseTask } from './base-task'
import { Network } from '../helpers/constants'
import { SyncAccountArguments } from '../helpers/task-arguments'
export class SyncAccountTask extends BaseTask<SyncAccountArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'syncAccount')
  }

  protected getDefaultArguments(): SyncAccountArguments {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      validatorId: networkConfig.VALID_ID,
      accountId: networkConfig.ACCOUNT_ID_1,
      accountName: networkConfig.ACCOUNT_NAME_1,
      fromZoneId: networkConfig.ZONE_ID,
      zoneName: networkConfig.ZONE_NAME,
      accountStatus: commonConfig.STATUS_APPLYING,
      reasonCode: commonConfig.REASON_CODE,
      approvalAmount: commonConfig.ZERO_AMOUNT,
      traceId: commonConfig.TRACE_ID,
      timeoutHeight: '1000000',
    }
  }
}
