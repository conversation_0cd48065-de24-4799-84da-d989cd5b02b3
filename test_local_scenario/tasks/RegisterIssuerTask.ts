import { Network } from '../helpers/constants'
import { RegisterIssuerArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class RegisterIssuerTask extends BaseTask<RegisterIssuerArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'registerIssuer')
  }

  protected getDefaultArguments(): Partial<RegisterIssuerArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      issuerId: networkConfig.ISSUER_ID,
      bankCode: networkConfig.BANK_CODE,
      issuerName: networkConfig.ISSUER_NAME,
      issuerKey: commonConfig.KEY_ISSUER,
      flag: '11',
    }
  }
}
