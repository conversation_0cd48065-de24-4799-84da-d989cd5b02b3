import { Network } from '../helpers/constants'
import { ApproveArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class ApproveTask extends BaseTask<ApproveArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'approve')
  }

  protected getDefaultArguments(): Partial<ApproveArguments> {
    const networkConfig = this.getNetworkConfig()
    return {
      validId: networkConfig.VALID_ID,
      ownerId: networkConfig.ACCOUNT_ID_1,
      spenderId: networkConfig.ACCOUNT_ID_2,
      amount: '999999',
    }
  }
}
