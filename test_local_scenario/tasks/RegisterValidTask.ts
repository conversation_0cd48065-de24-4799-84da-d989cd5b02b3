import { Network } from '../helpers/constants'
import { RegisterValidArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class RegisterValidTask extends BaseTask<RegisterValidArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'registerValidator')
  }

  protected getDefaultArguments(): Partial<RegisterValidArguments> {
    const networkConfig = this.getNetworkConfig()
    const commonConfig = this.getCommonConfig()
    return {
      validId: networkConfig.VALID_ID,
      issuerId: networkConfig.ISSUER_ID,
      validName: networkConfig.VALID_NAME,
      validKey: commonConfig.KEY_VALID,
      flag: '11',
    }
  }
}
