import { Network } from '../helpers/constants'
import { RetrieveDischargeEventArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class RetrieveDischargeEventTask extends BaseTask<RetrieveDischargeEventArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'retrieveDischargeEvent')
  }

  protected getDefaultArguments(): Partial<RetrieveDischargeEventArguments> {
    return {}
  }
}
