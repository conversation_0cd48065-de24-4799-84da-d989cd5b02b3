import { BaseTask } from './base-task'
import { Network } from '../helpers/constants'
import { GetTokenArguments } from '../helpers/task-arguments'

export class GetTokenTask extends BaseTask<GetTokenArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'getToken')
  }

  protected getDefaultArguments(): GetTokenArguments {
    const networkConfig = this.getNetworkConfig()
    if (this.network === Network.LocalBiz) {
      return {
        tokenId: (networkConfig as any).RENEWABLE_ID_1,
      }
    }
    return {
      tokenId: networkConfig.TOKEN_ID,
    }
  }
}
