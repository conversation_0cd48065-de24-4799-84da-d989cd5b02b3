import { BaseTask } from './base-task'
import { IndustryAccountArguments } from '../helpers/task-arguments'
import { Network } from '../helpers/constants'

export class SetActiveBusinessAccountWithZoneTask extends BaseTask<IndustryAccountArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'setActiveBusinessAccountWithZone')
  }

  protected getDefaultArguments(): Partial<IndustryAccountArguments> {
    const networkConfig = this.getNetworkConfig()
    return {
      validatorId: networkConfig.VALID_ID,
      accountId: networkConfig.ACCOUNT_ID_1,
      zoneId: (networkConfig as any).BIZ_ZONE_ID,
    }
  }
}
