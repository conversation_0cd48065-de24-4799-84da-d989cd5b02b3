import { Network, networkConfig } from '../helpers/constants'
import { CheckMintTask } from '../tasks/CheckMintTask'
import { MintTokenTask } from '../tasks/MintTokenTask'

async function mintToken(network: Network) {
  const checkMintTask = new CheckMintTask(network)
  const checkResult = await checkMintTask.execute()

  if (checkResult && checkResult.includes('result | ok')) {
    const mintTokenTask = new MintTokenTask(network)
    return await mintTokenTask.execute({
      accountId: networkConfig[network].ACCOUNT_ID_1,
    })
  } else {
    throw new Error(`failed mintToken for account ${networkConfig[network].ACCOUNT_ID_1}`)
  }
}

export { mintToken }
