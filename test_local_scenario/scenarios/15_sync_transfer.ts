import { Network, networkConfig } from '../helpers/constants'
import { CheckTransactionTask } from '../tasks/CheckTransactionTask'
import { TransferSingleTask } from '../tasks/TransferSingleTask'
import { showAccountsStatus } from './utils'

export async function syncTransfer(network: Network, fromAccountId: string, toAccountId: string) {
  const checkOutput = await new CheckTransactionTask(Network.LocalFin).execute({
    sendAccountId: fromAccountId,
    fromAccountId,
    toAccountId,
    zoneId: networkConfig[network].ZONE_ID,
    validId: networkConfig[network].VALID_ID,
  })
  if (!checkOutput.includes('result | ok')) {
    throw new Error('failed checkTransaction.')
  }
  const transferOutput = await new TransferSingleTask(network).execute({
    sendAccountId: fromAccountId,
    fromAccountId,
    toAccountId,
  })
  await new Promise((resolve) => setTimeout(resolve, 5000))
  await showAccountsStatus(fromAccountId)
  await showAccountsStatus(toAccountId)
  return transferOutput
}
