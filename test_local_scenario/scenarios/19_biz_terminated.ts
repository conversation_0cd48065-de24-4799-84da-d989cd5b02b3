import { Network, networkConfig } from '../helpers/constants'
import { GetAccountAllTask } from '../tasks/GetAccountAllTask'
import { SetBizZoneTerminatedTask } from '../tasks/SetBizZoneTerminatedTask'
import { showAccountsStatus } from './utils'

async function processBizTerminated(network: Network, accountId: string) {
  const getAccountAllTask = new GetAccountAllTask(network)
  const checkResult = await getAccountAllTask.execute({
    accountId,
    validId: networkConfig[network].VALID_ID,
  })

  if (checkResult && !checkResult?.includes('Not linked from Biz Zone.')) {
    const setBizZoneTerminatedTask = new SetBizZoneTerminatedTask(network)
    return await setBizZoneTerminatedTask.execute({
      accountId,
      zoneId: (networkConfig[network] as any).BIZ_ZONE_ID,
    })
  } else {
    throw new Error('BZ account is not linked.')
  }
}

async function bizTerminated(network: Network) {
  const config = networkConfig[network]

  const processResult1 = await processBizTerminated(network, config.ACCOUNT_ID_3)
  const processResult2 = await processBizTerminated(network, config.ACCOUNT_ID_4)

  const showStatus1 = await showAccountsStatus(config.ACCOUNT_ID_3)
  const showStatus2 = await showAccountsStatus(config.ACCOUNT_ID_4)
  return processResult1 + processResult2 + showStatus1 + showStatus2
}

export { bizTerminated }
