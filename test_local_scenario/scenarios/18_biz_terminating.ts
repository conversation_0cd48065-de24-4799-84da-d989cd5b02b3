import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { BizTerminatingTask } from '../tasks/BizTerminatingTask'
import { CheckSyncAccountTask } from '../tasks/CheckSyncAccountTask'
import { showAccountsStatus } from './utils'

async function processBizTerminating(network: Network, accountId: string) {
  const checkSyncAccountTask = new CheckSyncAccountTask(Network.LocalFin)
  const checkResult = await checkSyncAccountTask.execute({
    accountId,
    validId: networkConfig[network].VALID_ID,
    zoneId: networkConfig[network].ZONE_ID,
    accountStatus: commonConfig.STATUS_TERMINATING,
  })

  if (checkResult && checkResult.includes('result | ok')) {
    const bizTerminatingTask = new BizTerminatingTask(network)
    return await bizTerminatingTask.execute({
      accountId,
    })
  } else {
    console.error('failed checkSyncAccount.')
    throw new Error(`failed checkSyncAccount for account ${accountId}.`)
  }
}

async function bizTerminating(network: Network) {
  const config = networkConfig[network]

  const processAccount3Result = await processBizTerminating(network, config.ACCOUNT_ID_3)
  const processAccount4Result = await processBizTerminating(network, config.ACCOUNT_ID_4)

  await new Promise((resolve) => setTimeout(resolve, 10000))

  const account3Status = await showAccountsStatus(config.ACCOUNT_ID_3)
  const account4Status = await showAccountsStatus(config.ACCOUNT_ID_4)
  return processAccount3Result + processAccount4Result + account3Status + account4Status
}

export { bizTerminating }
