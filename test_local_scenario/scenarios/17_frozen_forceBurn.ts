import { Network, networkConfig } from '../helpers/constants'
import { ForceBurnTokenTask } from '../tasks/ForceBurnTokenTask'
import { GetBizZoneAccountStatus } from '../tasks/GetBizZoneAccountStatusTask'
import { SetAccountStatusTask } from '../tasks/SetAccountStatusTask'
import { showAccountsStatus } from './utils'

async function getBizZoneAccountStatus(accountId: string) {
  return await new GetBizZoneAccountStatus(Network.LocalFin).execute({
    accountId,
  })
}

export async function frozenForceBurn(accountId: string) {
  const setFrozenOutput = await new SetAccountStatusTask(Network.LocalFin).execute({
    accountStatus: 'frozen',
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
    accountId,
  })
  const frozenAccStatus = await showAccountsStatus(accountId)
  const frozenBizZoneAccStatus = await getBizZoneAccountStatus(accountId)

  const forceBurnTokenOutput = await new ForceBurnTokenTask(Network.LocalFin).execute({
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
    accountId,
  })
  const forceBurnAccStatus = await showAccountsStatus(accountId)
  const forceBizZoneAccStatus = await getBizZoneAccountStatus(accountId)

  const setAcctiveOutput = await new SetAccountStatusTask(Network.LocalFin).execute({
    issuerId: networkConfig[Network.LocalFin].ISSUER_ID,
    accountId,
    accountStatus: 'active',
  })
  const activeAccStatus = await showAccountsStatus(accountId)
  const ActiveBizZoneAccStatus = await getBizZoneAccountStatus(accountId)
  return (
    setFrozenOutput +
    frozenAccStatus +
    frozenBizZoneAccStatus +
    forceBurnTokenOutput +
    forceBurnAccStatus +
    forceBizZoneAccStatus +
    setAcctiveOutput +
    activeAccStatus +
    ActiveBizZoneAccStatus
  )
}
