import { Network, networkConfig } from '../helpers/constants'
import { CheckFinAccountStatusTask } from '../tasks/CheckFinAccountStatusTask'
import { MintRenewableArgumentsTask } from '../tasks/MintRenewableArgumentsTask'

export async function mintRenewable() {
  const accountId = networkConfig[Network.LocalBiz].ACCOUNT_ID_2
  const statusOutput = await new CheckFinAccountStatusTask(Network.LocalBiz).execute({
    accountId,
  })
  if (!statusOutput.includes('accountStatus | active')) {
    throw new Error(`failed checkFinAccountStatus for account ${accountId}`)
  }
  const mintRenewableTask = new MintRenewableArgumentsTask(Network.LocalBiz)
  const output1 = await mintRenewableTask.execute({
    tokenId: networkConfig[Network.LocalBiz].RENEWABLE_ID_1,
    metadataId: networkConfig[Network.LocalBiz].METADATA_ID,
    metadataHash: networkConfig[Network.LocalBiz].METADATA_HASH,
  })
  const output2 = await mintRenewableTask.execute({
    tokenId: networkConfig[Network.LocalBiz].RENEWABLE_ID_2,
    metadataId: networkConfig[Network.LocalBiz].METADATA_ID,
    metadataHash: networkConfig[Network.LocalBiz].METADATA_HASH,
  })
  const output3 = await mintRenewableTask.execute({
    tokenId: networkConfig[Network.LocalBiz].RENEWABLE_ID_3,
    metadataId: networkConfig[Network.LocalBiz].METADATA_ID,
    metadataHash: networkConfig[Network.LocalBiz].METADATA_HASH,
  })
  return output1 + output2 + output3
}
