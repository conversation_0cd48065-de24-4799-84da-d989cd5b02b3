import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { CheckExchangeTask } from '../tasks/CheckExchangeTask'
import { DischargeFromFinTask } from '../tasks/DischargeFromFinTask'
import { RetrieveDischargeEventTask } from '../tasks/RetrieveDischargeEventTask'

async function dischargeFromFin(accountId: string) {
  const checkExchangeTask = new CheckExchangeTask(Network.LocalFin)
  const checkResult = await checkExchangeTask.execute({
    accountId,
    toZoneId: networkConfig[Network.LocalBiz].FIN_ZONE_ID,
    fromZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
    amount: commonConfig.DISCHARGE_AMOUNT,
  })

  if (checkResult && checkResult.includes('result | ok')) {
    const dischargeFromFinTask = new DischargeFromFinTask(Network.LocalFin)
    return await dischargeFromFinTask.execute({
      accountId,
      fromZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
      toZoneId: networkConfig[Network.LocalBiz].FIN_ZONE_ID,
      amount: commonConfig.DISCHARGE_AMOUNT,
      timeoutHeight: '1000000',
    })
  } else {
    throw new Error(`failed exchange.`)
  }
}

async function retrieveDischargeEvent() {
  const retrieveDischargeEventTask = new RetrieveDischargeEventTask(Network.LocalBiz)
  return await retrieveDischargeEventTask.execute({})
}

export async function dischargeFromFinScenario() {
  const dischargeFromFinOutput = await dischargeFromFin(networkConfig[Network.LocalBiz].ACCOUNT_ID_4)
  await new Promise((resolve) => setTimeout(resolve, 5000))
  const retrieveDischargeEventOutput = await retrieveDischargeEvent()
  return dischargeFromFinOutput + retrieveDischargeEventOutput
}
