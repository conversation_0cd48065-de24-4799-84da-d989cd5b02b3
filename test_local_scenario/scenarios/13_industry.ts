import { Network, networkConfig } from '../helpers/constants'
import { GetAccountAllTask } from '../tasks/GetAccountAllTask'
import { SetActiveBusinessAccountWithZoneTask } from '../tasks/SetActiveBusinessAccountWithZoneTask'
import { showAccountsStatus } from './utils'

async function processIndustryAccount(network: Network, accountId: string, networkConfig: any) {
  const getAccountAllTask = new GetAccountAllTask(network)
  const output = await getAccountAllTask.execute({
    accountId,
  })

  if (!output.includes('Not linked from Biz Zone.')) {
    console.info(`start to make account ${accountId} active.`)

    const setActiveTask = new SetActiveBusinessAccountWithZoneTask(network)
    return await setActiveTask.execute({
      accountId,
    })
  } else {
    throw new Error(`BZ account ${accountId} is not linked.`)
  }
}

async function industry(network: Network) {
  const config = networkConfig[network]

  const processAccount3Result = await processIndustryAccount(network, config.ACCOUNT_ID_3, config)
  const processAccount4Result = await processIndustryAccount(network, config.ACCOUNT_ID_4, config)

  const account3Status = await showAccountsStatus(config.ACCOUNT_ID_3)
  const account4Status = await showAccountsStatus(config.ACCOUNT_ID_4)
  return processAccount3Result + processAccount4Result + account3Status + account4Status
}

export { industry }
