import { Network, networkConfig } from '../helpers/constants'
import { ApproveTask } from '../tasks/ApproveTask'
import { CheckApproveTask } from '../tasks/CheckApproveTask'

export async function approve(network: Network) {
  const checkApproveOutputawait = await new CheckApproveTask(network).execute()
  if (!checkApproveOutputawait.includes('result | ok')) {
    throw new Error(
      `failed approve for owner id ${networkConfig[network].ACCOUNT_ID_1} spender id ${networkConfig[network].ACCOUNT_ID_2}.`,
    )
  }
  return await new ApproveTask(network).execute()
}
