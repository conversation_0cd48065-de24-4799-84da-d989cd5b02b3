import { Network, networkConfig } from '../helpers/constants'
import { BurnTokenTask } from '../tasks/BurnTokenTask'
import { CheckBurnTask } from '../tasks/CheckBurnTask'

async function burnToken(network: Network) {
  const checkBurnTask = new CheckBurnTask(network)
  const checkResult = await checkBurnTask.execute()

  if (checkResult && checkResult.includes('result | ok')) {
    const burnTokenTask = new BurnTokenTask(network)
    return await burnTokenTask.execute()
  } else {
    throw new Error(`failed burnToken for account ${networkConfig[network].ACCOUNT_ID_1}`)
  }
}

export { burnToken }
