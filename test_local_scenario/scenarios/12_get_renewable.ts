import { Network, networkConfig } from '../helpers/constants'
import { GetTokenTask } from '../tasks/GetTokenTask'

async function getRenewable(network: Network) {
  const config = networkConfig[network] as any
  const getTokenTask = new GetTokenTask(network)

  await getTokenTask.execute({
    tokenId: config.RENEWABLE_ID_1,
  })

  await getTokenTask.execute({
    tokenId: config.RENEWABLE_ID_2,
  })

  return await getTokenTask.execute({
    tokenId: config.RENEWABLE_ID_3,
  })
}

export { getRenewable }
