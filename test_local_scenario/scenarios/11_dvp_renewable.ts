import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { CheckTransactionRenewableTask } from '../tasks/CheckTransactionRenewable'
import { DvpMultiTask } from '../tasks/DvpMultiTask'
import { DvpTask } from '../tasks/DvpTask'

export async function dvpRenewable() {
  const checkTask = new CheckTransactionRenewableTask(Network.LocalBiz)
  const checkOutput = await checkTask.execute({
    sendAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_2,
    fromAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_2,
    toAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_1,
    misc2: networkConfig[Network.LocalBiz].RENEWABLE_ID_1,
  })
  if (!checkOutput.includes('result | true')) {
    throw new Error('failed checkTransactionRenewable.')
  }
  const dvpOutput = await new DvpTask(Network.LocalBiz).execute({
    sendAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_1,
    fromAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_1,
    toAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_2,
    amount: commonConfig.TRANSFER_AMOUNT,
    misc2: networkConfig[Network.LocalBiz].RENEWABLE_ID_1,
    memo: '',
  })
  const checkMultiOutput = await checkTask.execute({
    sendAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_2,
    fromAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_2,
    toAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_1,
    misc2: `${networkConfig[Network.LocalBiz].RENEWABLE_ID_2},${networkConfig[Network.LocalBiz].RENEWABLE_ID_3}`,
  })
  if (!checkMultiOutput.includes('result | true')) {
    throw new Error('failed checkTransactionRenewable.')
  }
  const dvpMultiOutput = await new DvpMultiTask(Network.LocalBiz).execute({
    sendAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_1,
    fromAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_1,
    toAccountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_2,
    amount: commonConfig.TRANSFER_AMOUNT,
    tokenId1: networkConfig[Network.LocalBiz].RENEWABLE_ID_2,
    tokenId2: networkConfig[Network.LocalBiz].RENEWABLE_ID_3,
    memo: '',
  })
  return dvpOutput + dvpMultiOutput
}
