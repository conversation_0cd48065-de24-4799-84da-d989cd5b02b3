import { Network, networkConfig } from '../helpers/constants'
import { CheckSyncAccountTask } from '../tasks/CheckSyncAccountTask'
import { SyncAccountTask } from '../tasks/SyncAccountTask'
import { showAccountsStatus } from './utils'

async function syncAccount(network: Network, accountId: string, accountName: string) {
  const checkSyncAccountTask = new CheckSyncAccountTask(Network.LocalFin)
  const checkResult = await checkSyncAccountTask.execute({
    accountId,
    validId: networkConfig[network].VALID_ID,
    zoneId: networkConfig[network].ZONE_ID,
  })

  if (checkResult && checkResult.includes('result | ok')) {
    console.info(`start syncAccount for account ${accountId}.`)

    const syncAccountTask = new SyncAccountTask(network)
    const result = await syncAccountTask.execute({
      accountId,
      accountName,
    })

    console.info(`syncAccount for account ${accountId} is successful.`)
    return result
  } else {
    throw new Error(`failed checkSyncAccount for account ${accountId}.`)
  }
}

async function synchronous(network: Network) {
  const configData = networkConfig[network]

  const resultSyncAccount3 = await syncAccount(network, configData.ACCOUNT_ID_3, configData.ACCOUNT_NAME_3)
  const resultSyncAccount4 = await syncAccount(network, configData.ACCOUNT_ID_4, configData.ACCOUNT_NAME_4)

  await new Promise((resolve) => setTimeout(resolve, 10000))

  const account3Status = await showAccountsStatus(configData.ACCOUNT_ID_3)
  const account4Status = await showAccountsStatus(configData.ACCOUNT_ID_4)
  return resultSyncAccount3 + resultSyncAccount4 + account3Status + account4Status
}

export { synchronous }
