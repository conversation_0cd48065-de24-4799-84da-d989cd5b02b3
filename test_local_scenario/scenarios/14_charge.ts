import { Network, networkConfig } from '../helpers/constants'
import { CheckExchangeTask } from '../tasks/CheckExchangeTask'
import { MintTokenTask } from '../tasks/MintTokenTask'
import { RegisterEscrowAccTask } from '../tasks/RegisterEscrowAccTask'
import { TransferTask } from '../tasks/TransferTask'
import { showAccountsStatus } from './utils'

export async function charge(network: Network, accountId: string) {
  await new MintTokenTask(network).execute({
    accountId,
  })
  const checkExOutput = await new CheckExchangeTask(network).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
  })
  if (!checkExOutput.includes('result | ok')) {
    throw new Error('failed checkExchange.')
  }
  console.info('start charge to account3')
  const registerEscrowAccOutput = await new RegisterEscrowAccTask(network).execute({
    dstZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
    escrowAccount: networkConfig[Network.LocalFin].ACCOUNT_ID_1,
  })
  const transferOutput = await new TransferTask(network).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
  })
  await new Promise((resolve) => setTimeout(resolve, 5000))
  return (await showAccountsStatus(accountId)) + registerEscrowAccOutput + transferOutput
}
