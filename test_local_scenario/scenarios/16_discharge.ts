import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { CheckExchangeTask } from '../tasks/CheckExchangeTask'
import { TransferTask } from '../tasks/TransferTask'
import { showAccountsStatus } from './utils'

async function processDischarge(network: Network, accountId: string) {
  const checkExchangeTask = new CheckExchangeTask(Network.LocalFin)
  const checkResult = await checkExchangeTask.execute({
    accountId,
    toZoneId: networkConfig[Network.LocalBiz].FIN_ZONE_ID,
    fromZoneId: networkConfig[Network.LocalBiz].ZONE_ID,
  })

  if (checkResult && checkResult.includes('result | ok')) {
    const transferTask = new TransferTask(network)
    return await transferTask.execute({
      accountId,
      toZoneId: networkConfig[Network.LocalBiz].FIN_ZONE_ID,
      amount: commonConfig.DISCHARGE_AMOUNT,
    })
  } else {
    throw new Error(`failed exchange.`)
  }
}

async function discharge(network: Network) {
  const config = networkConfig[network]

  const result = await processDischarge(network, config.ACCOUNT_ID_4)

  await new Promise((resolve) => setTimeout(resolve, 5000))

  const account4Status = await showAccountsStatus(config.ACCOUNT_ID_4)

  return result + account4Status
}

export { discharge }
