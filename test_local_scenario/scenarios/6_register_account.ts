import { Network, networkConfig } from '../helpers/constants'
import { RegisterAccTask } from '../tasks/RegisterAccTask'

async function registerAcc(network: Network) {
  const registerAccTask = new RegisterAccTask(network)
  if (network === Network.LocalFin) {
    const output1 = await registerAccTask.execute({
      accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_1,
      accountName: networkConfig[Network.LocalFin].ACCOUNT_NAME_1,
    })
    const output2 = await registerAccTask.execute({
      accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_2,
      accountName: networkConfig[Network.LocalFin].ACCOUNT_NAME_2,
    })
    const output3 = await registerAccTask.execute({
      accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_3,
      accountName: networkConfig[Network.LocalFin].ACCOUNT_NAME_3,
    })
    const output4 = await registerAccTask.execute({
      accountId: networkConfig[Network.LocalFin].ACCOUNT_ID_4,
      accountName: networkConfig[Network.LocalFin].ACCOUNT_NAME_4,
    })
    return output1 + output2 + output3 + output4
  } else {
    const output1 = await registerAccTask.execute({
      accountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_1,
      accountName: networkConfig[Network.LocalBiz].ACCOUNT_NAME_1,
    })
    const output2 = await registerAccTask.execute({
      accountId: networkConfig[Network.LocalBiz].ACCOUNT_ID_2,
      accountName: networkConfig[Network.LocalBiz].ACCOUNT_NAME_2,
    })
    return output1 + output2
  }
}

export { registerAcc }
