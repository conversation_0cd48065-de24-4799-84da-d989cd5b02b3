import { expect } from 'chai'
import { Network } from './helpers/constants'
import { dvpRenewable } from './scenarios/11_dvp_renewable'
import { regBizzone } from './scenarios/11_reg_bizzone'
import { getRenewable } from './scenarios/12_get_renewable'
import { registerProv } from './scenarios/2_register_prov'
import { registerIssuer } from './scenarios/3_register_issuer'
import { registerValid } from './scenarios/4_register_valid'
import { registerToken } from './scenarios/5_register_token'
import { registerAcc } from './scenarios/6_register_account'
import { approve } from './scenarios/7_approve'
import { burnToken } from './scenarios/7_burn_token'
import { mintToken } from './scenarios/7_mint_token'
import { mintRenewable } from './scenarios/9_mint_renewable'

describe('Registering Data for localFin', function () {
  this.timeout(0)
  it('Registering Provier for localFin', async function () {
    const output = await registerProv(Network.LocalFin)
    expect(output).to.not.include('Error')
  })
  it('Registering Issuer for localFin', async function () {
    const output = await registerIssuer(Network.LocalFin)
    expect(output).to.not.include('Error')
  })
  it('Setting valid ID for localFin', async function () {
    const output = await registerValid(Network.LocalFin)
    expect(output).to.not.include('Error')
  })
  it('Registering Token for localFin', async function () {
    const output = await registerToken(Network.LocalFin)
    expect(output).to.not.include('Error')
  })
  it('Setting Account ID for localFin', async function () {
    const output = await registerAcc(Network.LocalFin)
    expect(output).to.not.include('Error')
  })
  it('Minting token for localFin', async function () {
    const output = await mintToken(Network.LocalFin)
    expect(output).to.not.include('Error')
  })
  it('Burning token for localFin', async function () {
    const output = await burnToken(Network.LocalFin)
    expect(output).to.not.include('Error')
  })
  it('Registering BizZone for localFin', async function () {
    const output = await regBizzone(Network.LocalFin)
    expect(output).to.not.include('Error')
  })
})

describe('Registering Data for localBiz', function () {
  this.timeout(0)
  it('Registering Provier for localBiz', async function () {
    const output = await registerProv(Network.LocalBiz)
    expect(output).to.not.include('Error')
  })
  it('Registering Issuer for localBiz', async function () {
    const output = await registerIssuer(Network.LocalBiz)
    expect(output).to.not.include('Error')
  })
  it('Setting valid ID for localBiz', async function () {
    const output = await registerValid(Network.LocalBiz)
    expect(output).to.not.include('Error')
  })
  it('Registering Token for localBiz', async function () {
    const output = await registerToken(Network.LocalBiz)
    expect(output).to.not.include('Error')
  })
  it('Setting Account ID for localBiz', async function () {
    const output = await registerAcc(Network.LocalBiz)
    expect(output).to.not.include('Error')
  })
  it('Minting token for localBiz', async function () {
    const output = await mintToken(Network.LocalBiz)
    expect(output).to.not.include('Error')
  })
  it('Burning token for localBiz', async function () {
    const output = await burnToken(Network.LocalBiz)
    expect(output).to.not.include('Error')
  })
  it('Approve for localBiz', async function () {
    const output = await approve(Network.LocalBiz)
    expect(output).to.not.include('Error')
  })
  it('Minting Renewable Tokens for localBiz', async function () {
    const output = await mintRenewable()
    expect(output).to.not.include('Error')
  })
  it('Getting renewable for localBiz', async function () {
    const output = await getRenewable(Network.LocalBiz)
    expect(output).to.not.include('Error')
  })
  it('DvP Renewable Tokens for localBiz', async function () {
    const output = await dvpRenewable()
    expect(output).to.not.include('Error')
  })
  it('Getting renewable for localBiz', async function () {
    const output = await getRenewable(Network.LocalBiz)
    expect(output).to.not.include('Error')
  })
})
