import debug from 'debug'
import { HardhatContext } from 'hardhat/internal/context'
import { loadConfigAndTasks } from 'hardhat/internal/core/config/config-loading'
import { getEnvHardhatArguments } from 'hardhat/internal/core/params/env-variables'
import { HARDHAT_PARAM_DEFINITIONS } from 'hardhat/internal/core/params/hardhat-params'
import { Environment } from 'hardhat/internal/core/runtime-environment'
import { HardhatRuntimeEnvironment } from 'hardhat/types'

let ctx: HardhatContext
const hres = new Map()

if (HardhatContext.isCreated()) {
  ctx = HardhatContext.getHardhatContext()
} else {
  ctx = HardhatContext.createHardhatContext()
}

function getHardhatRuntimeEnvironment(network: string): HardhatRuntimeEnvironment {
  if (hres.has(network)) {
    const hre = hres.get(network)
    ctx.environment = hre
    return hre
  } else {
    process.env.HARDHAT_NETWORK = network
    const hardhatArguments = getEnvHardhatArguments(HARDHAT_PARAM_DEFINITIONS, process.env)
    if (hardhatArguments.verbose) {
      debug.enable('hardhat*')
    }
    const { resolvedConfig, userConfig } = loadConfigAndTasks(hardhatArguments)

    const hre = new Environment(
      resolvedConfig,
      hardhatArguments,
      ctx.tasksDSL.getTaskDefinitions(),
      ctx.tasksDSL.getScopesDefinitions(),
      ctx.environmentExtenders,
      userConfig,
      ctx.providerExtenders,
    )
    hres.set(network, hre)
    ctx.environment = hre
    return hre
  }
}

export { getHardhatRuntimeEnvironment }
