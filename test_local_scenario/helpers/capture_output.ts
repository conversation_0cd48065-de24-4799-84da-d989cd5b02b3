import { stderr, stdout } from 'process'

/**
 * Async version of captureConsoleOutput
 * @template T The return type of the async function being captured
 * @param fn The async function to capture output from
 * @returns A promise with an object containing the function's result and captured output
 */
export async function captureConsoleOutputAsync<T>(fn: () => Promise<T>): Promise<{ result: T; output: string }> {
  let output = ''

  const originalStdoutWrite = stdout.write.bind(stdout)
  stdout.write = (chunk, encoding, callback) => {
    if (typeof chunk === 'string') {
      output += chunk
    }
    return originalStdoutWrite(chunk, encoding, callback)
  }

  const originalStderrWrite = stderr.write.bind(stderr)
  stderr.write = (chunk, encoding, callback) => {
    if (typeof chunk === 'string') {
      output += chunk
    }
    return originalStderrWrite(chunk, encoding, callback)
  }

  try {
    const result = await fn()
    return { result, output }
  } finally {
    stdout.write = originalStdoutWrite
  }
}
