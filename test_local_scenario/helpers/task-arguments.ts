interface BaseTaskArguments {}

interface RegisterIssuerArguments extends BaseTaskArguments {
  issuerId: string
  bankCode: string
  issuerName: string
  issuerKey: string
  flag: string
}

interface RegisterValidArguments extends BaseTaskArguments {
  validId: string
  issuerId: string
  validName: string
  validKey: string
  flag: string
}

interface GetAccountAllArguments extends BaseTaskArguments {
  accountId: string
  validId: string
}

interface RegisterProvArguments extends BaseTaskArguments {
  provId: string
  zoneId: string
  zoneName: string
  provKey: string
  flag: string
}

interface RegisterTokenArguments extends BaseTaskArguments {
  provKey: string
  tokenId: string
  provId: string
  tokenName: string
  symbol: string
}

interface RegisterAccArguments extends BaseTaskArguments {
  accountId: string
  accountName: string
  accountKey: string
  issuerKey: string
  issuerId: string
  validId: string
  flag: string
}

interface MintTokenArguments extends BaseTaskArguments {
  issuerId: string
  accountId: string
  amount: string
  issuerKey: string
}

interface BurnTokenArguments extends BaseTaskArguments {
  issuerId: string
  accountId: string
  amount: string
  issuerKey: string
}

interface ApproveArguments extends BaseTaskArguments {
  validId: string
  ownerId: string
  spenderId: string
  amount: string
}

interface CheckApproveArguments extends ApproveArguments {
  validKey: string
}

interface CheckFinAccountStatusArguments extends BaseTaskArguments {
  accountId: string
}

interface MintRenewableArguments extends BaseTaskArguments {
  tokenId: string
  metadataId: string
  metadataHash: string
  mintAccountId: string
  ownerAccountId: string
}

interface RegisterBizZoneArguments extends BaseTaskArguments {
  zoneId: string
  zoneName: string
}

interface GetTokenArguments extends BaseTaskArguments {
  tokenId: string
}

interface RenewableArguments extends BaseTaskArguments {
  sendAccountId: string
  fromAccountId: string
  toAccountId: string
}

interface CheckTransactionRenewableArguments extends RenewableArguments {
  misc2: string
}

interface DvpArguments extends RenewableArguments {
  amount: string
  misc2: string
  memo: string
}

interface DvpMultiArguments extends RenewableArguments {
  amount: string
  tokenId1: string
  tokenId2: string
  memo: string
}

interface IndustryAccountArguments extends BaseTaskArguments {
  validatorId: string
  accountId: string
  zoneId: string
}

interface CheckExchangeArguments extends BaseTaskArguments {
  accountId: string
  toZoneId: string
  fromZoneId: string
  amount: string
}

interface RegisterEscrowAccArguments extends BaseTaskArguments {
  srcZoneId: string
  dstZoneId: string
  escrowAccount: string
}

interface TransferArguments extends BaseTaskArguments {
  accountId: string
  fromZoneId: string
  toZoneId: string
  amount: string
  timeoutHeight: string
}

interface CheckTransactionArguments extends BaseTaskArguments {
  sendAccountId: string
  fromAccountId: string
  toAccountId: string
  zoneId: string
  validId: string
  amount: string
}

interface TransferSingleArguments extends BaseTaskArguments {
  sendAccountId: string
  fromAccountId: string
  toAccountId: string
  amount: string
  miscValue1: string
  miscValue2: string
  memo: string
  traceId: string
}

interface GetBizZoneAccountStatusArguments extends BaseTaskArguments {
  accountId: string
  zoneId: string
}

interface SetAccountStatusArguments extends BaseTaskArguments {
  issuerId: string
  accountId: string
  accountStatus: string
  reasonCode: string
  issuerKey: string
}

interface ForceBurnTokenArguments extends BaseTaskArguments {
  issuerId: string
  accountId: string
  issuerKey: string
}

interface CheckSyncAccountArguments extends BaseTaskArguments {
  accountId: string
  validId: string
  zoneId: string
  accountStatus: string
}

interface SyncAccountArguments extends BaseTaskArguments {
  validatorId: string
  accountId: string
  accountName: string
  fromZoneId: string
  zoneName: string
  accountStatus: string
  reasonCode: string
  approvalAmount: string
  traceId: string
  timeoutHeight: string
}

interface SetBizZoneTerminatedArguments extends BaseTaskArguments {
  accountId: string
  zoneId: string
}

interface DischargeFromFinArguments extends BaseTaskArguments {
  accountId: string
  fromZoneId: string
  toZoneId: string
  amount: string
  timeoutHeight: string
}

interface RetrieveDischargeEventArguments extends BaseTaskArguments {}

export type {
  ApproveArguments,
  BaseTaskArguments,
  BurnTokenArguments,
  CheckApproveArguments,
  CheckExchangeArguments,
  CheckFinAccountStatusArguments,
  CheckSyncAccountArguments,
  CheckTransactionArguments,
  CheckTransactionRenewableArguments,
  DischargeFromFinArguments,
  DvpArguments,
  DvpMultiArguments,
  ForceBurnTokenArguments,
  GetAccountAllArguments,
  GetBizZoneAccountStatusArguments,
  GetTokenArguments,
  IndustryAccountArguments,
  MintRenewableArguments,
  MintTokenArguments,
  RegisterAccArguments,
  RegisterBizZoneArguments,
  RegisterEscrowAccArguments,
  RegisterIssuerArguments,
  RegisterProvArguments,
  RegisterTokenArguments,
  RegisterValidArguments,
  RetrieveDischargeEventArguments,
  SetAccountStatusArguments,
  SetBizZoneTerminatedArguments,
  SyncAccountArguments,
  TransferArguments,
  TransferSingleArguments,
}
