import { SubtaskArguments, TaskArguments, TaskIdentifier } from 'hardhat/types'
import { envVers } from '../../envVers'
import { captureConsoleOutputAsync } from './capture_output'
import { Network, commonConfig, networkConfig } from './constants'
import { getHardhatRuntimeEnvironment } from './multi_hardhat'

// load common environemnt variables
Object.entries(commonConfig).forEach(([key, value]) => {
  process.env[key] = value
})

const networkEnvVers: Map<string, string> = new Map([
  ['provider.url', 'PROVIDER'],
  ['kms.endpoint', 'KMS_ENDPOINT_URL'],
])

async function runTask(
  network: Network,
  taskName: TaskIdentifier,
  taskArguments?: TaskArguments,
  subtaskArguments?: SubtaskArguments,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<any> {
  // load network specific environment variables
  networkConfig[network] &&
    Object.entries(networkConfig[network]).forEach(([key, value]) => {
      process.env[key] = value
    })

  networkEnvVers.forEach((value, path) => {
    const keys = path.split('.')
    keys.reduce((o, key, index) => {
      if (index === keys.length - 1) {
        o[key] = process.env[value]
      } else {
        if (!o[key]) {
          o[key] = {}
        }
      }
      return o[key]
    }, envVers)
  })

  const hre = getHardhatRuntimeEnvironment(network)
  return await hre.run(taskName, taskArguments, subtaskArguments)
}

async function runTaskAndCaptureOutput(
  network: Network,
  taskName: TaskIdentifier,
  taskArguments?: TaskArguments,
  subtaskArguments?: SubtaskArguments,
): Promise<string> {
  const result = await captureConsoleOutputAsync(async () => {
    return runTask(network, taskName, taskArguments, subtaskArguments)
  })
  return result.output
}

export { runTask, runTaskAndCaptureOutput }
