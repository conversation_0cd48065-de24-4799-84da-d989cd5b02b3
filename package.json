{"name": "dcbg-dcjpy-contract", "version": "2.0.0", "description": "", "main": "index.js", "scripts": {"preinstall": "npx only-allow npm", "generate-types": "typechain --target=ethers-v6 'build/contracts/**/*[^dbg].json'", "postinstall": "npx hardhat compile --force && npm run generate-types", "test": "npm run postinstall && npx hardhat test && npm run size", "test-all": "npm run postinstall && run-s test:* --is_executed_postinstall=true", "test:access-ctrl": "if ! ${npm_config_is_executed_postinstall} || [ -z ${npm_config_is_executed_postinstall} ]; then npm run postinstall; fi && npx hardhat test test/AccessCtrl/AccessCtrl.test.ts", "test:account": "if ! ${npm_config_is_executed_postinstall} || [ -z ${npm_config_is_executed_postinstall} ]; then npm run postinstall; fi && npx hardhat test test/Account/Account.test.ts", "test:contractManager": "if ! ${npm_config_is_executed_postinstall} || [ -z ${npm_config_is_executed_postinstall} ]; then npm run postinstall; fi && npx hardhat test test/ContractManager/ContractManager.test.ts", "test:issuer": "if ! ${npm_config_is_executed_postinstall} || [ -z ${npm_config_is_executed_postinstall} ]; then npm run postinstall; fi && npx hardhat test test/Issuer/Issuer.test.ts", "test:provider": "if ! ${npm_config_is_executed_postinstall} || [ -z ${npm_config_is_executed_postinstall} ]; then npm run postinstall; fi && npx hardhat test test/Provider/Provider.test.ts", "test:token": "if ! ${npm_config_is_executed_postinstall} || [ -z ${npm_config_is_executed_postinstall} ]; then npm run postinstall; fi && npx hardhat test test/Token/Token.test.ts", "test:transfer-proxy": "if ! ${npm_config_is_executed_postinstall} || [ -z ${npm_config_is_executed_postinstall} ]; then npm run postinstall; fi && npx hardhat test test/TransferProxy/TransferProxy.test.ts", "test:validator": "if ! ${npm_config_is_executed_postinstall} || [ -z ${npm_config_is_executed_postinstall} ]; then npm run postinstall; fi && npx hardhat test test/Validator/Validator.test.ts", "test:providerMigrate": "if ! ${npm_config_is_executed_postinstall} || [ -z ${npm_config_is_executed_postinstall} ]; then npm run postinstall; fi && npx hardhat test test/ProviderMigrate/Provider.test.ts --show-stack-traces", "test-local-scenario:all_scenario": "TS_NODE_TRANSPILE_ONLY=true npx mocha --require ts-node/register test_local_scenario/all_scenario.test.ts --reporter test_local_scenario/utils/reporter.ts", "coverage": "npm run postinstall && npx hardhat coverage && npm run size", "prettier": "npx prettier --write .", "solhint": "npx solhint \"contracts/**/*.sol\"", "lint-staged": "npm run prettier", "size": "npx hardhat size-contracts | tee ./docs/size-contracts.md", "prepare": "husky install"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.7", "@nomicfoundation/hardhat-ethers": "^3.0.6", "@nomicfoundation/hardhat-network-helpers": "^1.0.11", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomiclabs/hardhat-truffle5": "^2.0.7", "@nomiclabs/hardhat-web3": "^2.0.0", "@truffle/hdwallet-provider": "^2.1.15", "@typechain/ethers-v6": "^0.5.1", "@types/chai": "^4.3.7", "@types/lodash.omit": "^4.5.9", "@types/lodash.pick": "^4.4.9", "@types/mocha": "^10.0.7", "@types/mochawesome": "^6.2.4", "@types/node": "^22.1.0", "@types/web3": "^1.2.2", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "chalk": "^4.1.2", "dotenv": "^16.4.5", "eslint": "^8.45.0", "ethers": "^6.13.2", "hardhat": "^2.22.8", "hardhat-contract-sizer": "^2.10.0", "hardhat-deploy": "^0.12.4", "hardhat-deploy-ethers": "^0.4.2", "husky": "^9.1.4", "mochawesome": "^7.1.3", "npm-run-all": "^4.1.5", "only-allow": "^1.2.1", "prettier": "^3.3.3", "prettier-plugin-solidity": "1.3.1", "solhint": "^5.0.3", "solidity-coverage": "^0.8.12", "solidity-docgen": "0.6.0-beta.36", "surya": "^0.4.11", "truffle-assertions": "^0.9.2", "truffle-contract-size": "^2.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.4", "underscore": "1.13.7", "utility-types": "^3.11.0"}, "dependencies": {"@openzeppelin/contracts": "^4.8.3", "@openzeppelin/contracts-upgradeable": "^4.5.2", "@peculiar/asn1-ecc": "^2.3.14", "aws-kms-ethers-signer": "^0.1.3", "bignumber.js": "^2.3.0", "bn.js": "^5.2.1", "eth-sig-util": "^3.0.1", "lodash": "^4.17.21", "npm": "^10.8.2", "peculiar": "^0.14.1", "solhint-plugin-dcf": "file:solhint-plugin-dcf", "solidity-mpt": "^0.1.0", "solidity-rlp": "^2.0.8", "truffle-assertions": "^0.9.2"}, "engines": {"node": "v18.17.1"}}