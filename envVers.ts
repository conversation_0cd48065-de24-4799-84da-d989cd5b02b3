import dotenv from 'dotenv'
import * as PrivateKey from './privateKey'

dotenv.config()
dotenv.config({ path: '.kms' })

export const envVers = {
  aws: {
    profile: process.env.AWS_PROFILE,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || 'test',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'test',
      sessionToken: process.env.AWS_SESSION_TOKEN || 'test',
    },
    region: process.env.REGION || 'ap-northeast-1',
  },
  provider: {
    url: process.env.CODEBUILD_PROVIDER
      ? process.env.CODEBUILD_PROVIDER
      : process.env.PROVIDER || 'http://127.0.0.1:18451',
  },
  network: {
    id: process.env.NETWORK_ID || '5151',
    localFin: {
      port: process.env.LOCAL_FIN_PORT || '18451',
      networkId: process.env.LOCAL_FIN_CHAIN_ID || '5151',
    },
    localBiz: {
      port: process.env.LOCAL_BIZ_PORT || '28451',
      networkId: process.env.LOCAL_BIZ_CHAIN_ID || '5152',
    },
  },
  kms: {
    keys: {
      fin: process.env.KMS_KEY_ID_FIN || 'test',
      biz: process.env.KMS_KEY_ID_BIZ || 'test',
    },
    endpoint: process.env.KMS_ENDPOINT_URL || 'http://localhost:14566',
  },
  ibc: {
    fin: {
      ibcTokenAddress: process.env.FIN_IBC_TOKEN_ADDRESS,
      validatorAddress: process.env.FIN_VALIDATOR_ADDRESS,
      accountAddress: process.env.FIN_ACCOUNT_ADDRESS,
      accessCtrlAddress: process.env.FIN_ACCESS_CTRL_ADDRESS,
      businessZoneAccountAddress: process.env.FIN_BUSINESS_ZONE_ACCOUNT_ADDRESS,
    },
    biz: {
      ibcTokenAddress: process.env.BIZ_IBC_TOKEN_ADDRESS,
      validatorAddress: process.env.BIZ_VALIDATOR_ADDRESS,
      accountAddress: process.env.BIZ_ACCOUNT_ADDRESS,
      accessCtrlAddress: process.env.BIZ_ACCESS_CTRL_ADDRESS,
      businessZoneAccountAddress: process.env.BIZ_BUSINESS_ZONE_ACCOUNT_ADDRESS,
    },
    common: {
      ibcTokenAddress: process.env.IBC_TOKEN_ADDRESS,
      validatorAddress: process.env.VALIDATOR_ADDRESS,
      accountAddress: process.env.ACCOUNT_ADDRESS,
      accessCtrlAddress: process.env.ACCESS_CTRL_ADDRESS,
      businessZoneAccountAddress: process.env.BUSINESS_ZONE_ACCOUNT_ADDRESS,
    },
  },
  localTest: {
    accountId1: process.env.ACCOUNT_ID_1,
    accountId2: process.env.ACCOUNT_ID_2,
    renewableId1: process.env.RENEWABLE_ID_1,
    valId: process.env.VALID_ID,
    offset: process.env.OFFSET,
    limit: process.env.LIMIT,
    sortOrder: process.env.SORT_ORDER,
  },
  mode: {
    test: process.env.MODE_TEST,
  },
  keyAdmin: process.env.KEY_ADMIN || PrivateKey.key[0],
}
