import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('burnCancel', 'burn Cancel', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('amount', 'amount')
  .addParam('blockTimestamp', 'block Timestamp')
  .addParam('issuerKey', 'issuer Key')
  .setAction(async (taskArguments, hre) => {
    try {
      let { accountId = '', issuerId = '', amount = '', blockTimestamp = '', issuerKey = '' } = { ...taskArguments }

      accountId = convertToHex({ hre, value: accountId })
      issuerId = convertToHex({ hre, value: issuerId })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** burnCancel Parameters **\n`)
      const params = {
        issuerId,
        accountId,
        amount,
        blockTimestamp,
      }
      Tools.printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Token' })

      const deadline = await Tools.getTime()
      const sig = await PrivateKey.sig(
        issuerKey,
        ['bytes32', 'bytes32', 'uint256', 'uint256', 'uint256'],
        [issuerId, accountId, amount, blockTimestamp, deadline],
      )

      const receipt = await contract.burnCancel(issuerId, accountId, amount, blockTimestamp, traceId, deadline, sig[0])

      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error: any) {
      Tools.showErrorDetails({ error })
    }
  })
