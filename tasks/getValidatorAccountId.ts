import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getValidatorAccountId', 'get ValidatorAccountId', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .setAction(async (taskArguments, hre) => {
    try {
      let { validatorId = '' } = { ...taskArguments }
      validatorId = convertToHex({ hre, value: validatorId })

      console.log(`** getValidatorAccountId Parameters **\n`)
      const params = {
        validatorId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      const result = await contract.getValidatorAccountId(validatorId)

      const validatorInfo = {
        accountId: result.accountId,
        error: result.err,
      }

      console.log(`** Validator Account Information **\n`)
      printTable({ data: validatorInfo })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
