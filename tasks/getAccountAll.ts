import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable } from './common/tools'

wrappedTask('getAccountAll', 'Get all Account information associated with Validator.', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    let { validId = '', accountId = '' } = { ...taskArguments }
    const validatorId = convertToHex({ hre, value: validId })
    accountId = convertToHex({ hre, value: accountId })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

    const receipt = await contract.getAccountAll(validatorId, accountId)

    console.log(`*** Account Information (with Business Zone Information) ***`)

    const accountInfo = {
      accountId: ethers.toUtf8String(accountId),
      accountName: ethers.toUtf8String(receipt[0].accountName.toString()),
      accountStatus: ethers.toUtf8String(receipt[0].accountStatus.toString()),
      balance: receipt[0].balance,
      appliedAt: receipt[0].appliedAt.toString(),
      registeredAt: receipt[0].registeredAt.toString(),
      terminatingAt: receipt[0].terminatingAt.toString(),
      terminatedAt: receipt[0].terminatedAt.toString(),
    }

    printTable({ data: accountInfo })

    if (receipt[0].businessZoneAccounts.length > 0) {
      console.log('Business Zone Account Linkage Status: Linked')
      for (const businessZoneAccount of receipt[0].businessZoneAccounts) {
        const businessZoneInfo = {
          accountName: ethers.toUtf8String(businessZoneAccount.accountName.toString()),
          zoneId: businessZoneAccount.zoneId.toString(),
          zoneName: businessZoneAccount.zoneName.toString(),
          balance: businessZoneAccount.balance,
          accountStatus: ethers.toUtf8String(businessZoneAccount.accountStatus.toString()),
          appliedAt: businessZoneAccount.appliedAt.toString(),
          registeredAt: businessZoneAccount.registeredAt.toString(),
          terminatingAt: businessZoneAccount.terminatingAt.toString(),
          terminatedAt: businessZoneAccount.terminatedAt.toString(),
        }

        console.log('--- Linked Business Zone Account ---')
        printTable({ data: businessZoneInfo })
      }
    } else {
      console.log('BusinessZoneAccounts: Not linked from Business Zone.')
    }
  })
