import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('registerValidator', 'register validator', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .addParam('issuerId', 'issuer id')
  .addParam('validName', 'validator name')
  .addParam('validKey', 'validator key')
  .addParam('flag', 'flag')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    const kmsSigner = kmsSignerProvider({ hre })

    let { validKey = '', validId: validatorId = '', issuerId = '', validName = '', flag = '' } = { ...taskArguments }
    validName = convertToHex({ hre, value: validName })
    const validatorIdHex = convertToHex({ hre, value: validatorId })
    issuerId = convertToHex({ hre, value: issuerId })

    const traceId = convertToHex({ hre, value: 'trace1' })

    const addrValid = new ethers.Wallet(validKey).address

    const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

    if (flag[0] === '1') {
      console.log(`*** validatorID登録: ${validatorIdHex}`)
      const deadline = await Tools.getTime()
      const kmsSig = await kmsSigner.sign(
        ['bytes32', 'bytes32', 'bytes32', 'uint256'],
        [validatorIdHex, issuerId, validName, deadline],
      )
      const receipt = await contract.addValidator(validatorIdHex, issuerId, validName, traceId, deadline, kmsSig)
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
    if (flag[1] === '1') {
      console.log(`*** validatorID権限登録: ${validatorIdHex}=${addrValid}`)
      const deadline = await Tools.getTime()
      const kmsSig = await kmsSigner.sign(['bytes32', 'address', 'uint256'], [validatorIdHex, addrValid, deadline])
      const receipt = await contract.addValidatorRole(validatorIdHex, addrValid, traceId, deadline, kmsSig)
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
  })
