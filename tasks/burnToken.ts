import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('burnToken', 'burn token', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('amount', 'mint amount')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    let { issuerId = '', accountId = '', amount = '', issuerKey = '' } = { ...taskArguments }
    issuerId = convertToHex({ hre, value: issuerId })
    accountId = convertToHex({ hre, value: accountId })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Token' })

    console.log(`*** Token burn: ${amount}`)

    const receipt = await contract.burn(issuerId, accountId, amount, traceId)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
