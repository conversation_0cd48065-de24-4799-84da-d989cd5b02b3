import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('modAccount', 'mod Account', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .addParam('accountId', 'account Id')
  .addParam('accountName', 'account Name')
  .addParam('validatorKey', 'validator Key')
  .setAction(async (taskArguments, hre) => {
    try {
      let { validatorId = '', accountId = '', accountName = '', validatorKey = '' } = { ...taskArguments }
      validatorId = convertToHex({ hre, value: validatorId })
      accountId = convertToHex({ hre, value: accountId })
      accountName = convertToHex({ hre, value: accountName })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** modAccount Parameters **\n`)
      const params = {
        validatorId,
        accountId,
        accountName,
        validatorKey,
      }
      Tools.printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      const deadline = await Tools.getTime()
      const sig = await PrivateKey.sig(
        validatorKey,
        ['bytes32', 'bytes32', 'uint256'],
        [validatorId, accountId, deadline],
      )

      const receipt = await contract.modAccount(validatorId, accountId, accountName, traceId, deadline, sig[0])
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error: any) {
      Tools.showErrorDetails({ error })
    }
  })
