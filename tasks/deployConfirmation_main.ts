import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('deployConfirmation_main', 'Prints contracts setted to ContractManager', {
  filePath: path.basename(__filename),
}).setAction(async (taskArguments, hre) => {
  const TARGET = [
    'AccessCtrl',
    'Account',
    'ContractManager',
    'Error',
    'Issuer',
    'Provider',
    'Token',
    'TransferProxy',
    'Validator',
  ]
  for (let lp = 0; lp < TARGET.length; lp++) {
    const { deployed, contract } = await getContractWithSigner({ hre, contractName: TARGET[lp] })

    // Errorはversion()が無いため、アドレスのみ表示
    if (TARGET[lp] == 'Error') {
      console.log(`${TARGET[lp]}: ${deployed.address}`)
      continue
    }
    const version = await contract.version()
    console.log(`${TARGET[lp]}: \"${version}\" (${deployed.address})`)
  }
})
