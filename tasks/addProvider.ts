import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('addProvider', 'add provider', { filePath: path.basename(__filename) })
  .addParam('provId', 'provider id')
  .addParam('zoneId', 'zone id')
  .addParam('zoneName', 'zone name')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    let { provId = '', zoneId = '', zoneName = '' } = { ...taskArguments }

    const traceId = convertToHex({ hre, value: 'trace1' })
    provId = convertToHex({ hre, value: provId })
    zoneId = Number(zoneId)

    const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

    contract.connect(kmsSigner)

    console.log(`** addProvider Parameters **\n`)
    const params = {
      provId,
      zoneId,
      zoneName,
    }
    Tools.printTable({ data: params })

    const deadline = await Tools.getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'uint16', 'uint256'], [provId, zoneId, deadline])

    const receipt = await contract.addProvider(provId, zoneId, zoneName, traceId, deadline, kmsSig)

    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
