import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getValidator', 'get Validator', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .setAction(async (taskArguments, hre) => {
    try {
      let { validatorId = '' } = { ...taskArguments }
      validatorId = convertToHex({ hre, value: validatorId })

      console.log(`** getValidator Parameters **\n`)
      const params = {
        validatorId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      const result = await contract.getValidator(validatorId)

      const validatorInfo = {
        issuerId: result.issuerId,
        name: result.name,
        error: result.err,
      }

      console.log(`** Validator Information **\n`)
      printTable({ data: validatorInfo })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
