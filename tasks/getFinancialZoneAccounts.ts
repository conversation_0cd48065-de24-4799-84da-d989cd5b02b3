import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable } from './common/tools'

wrappedTask('getFinancialZoneAccount', 'get all financial zone account data', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    let { accountId = '' } = { ...taskArguments }
    accountId = convertToHex({ hre, value: accountId })

    const { contract } = await getContractWithSigner({ hre, contractName: 'FinancialCheck' })
    const receipt = await contract.getAccountLimit(accountId)

    const accountInfo = {
      accountId: accountId,
      mintLimit: receipt.accountLimitData.mintLimit,
      burnLimit: receipt.accountLimitData.burnLimit,
      transferLimit: receipt.accountLimitData.transferLimit,
      chargeLimit: receipt.accountLimitData.chargeLimit,
      cumulativeLimit: receipt.accountLimitData.cumulativeLimit,
      cumulativeAmount: receipt.accountLimitData.cumulativeAmount,
      cumulativeDate: receipt.accountLimitData.cumulativeDate,
    }

    console.log(`--- Financial Zone Account---`)
    printTable({ data: accountInfo })
  })
