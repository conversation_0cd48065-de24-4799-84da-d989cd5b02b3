import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getIssuer', 'Get issuer information', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'Issuer ID to retrieve')
  .setAction(async (taskArguments, hre) => {
    try {
      let { issuerId = '' } = { ...taskArguments }
      issuerId = convertToHex({ hre, value: issuerId })

      console.log(`** getIssuer Parameters **\n`)
      const params = {
        issuerId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

      const result = await contract.getIssuer(issuerId)

      const issuerInfo = {
        name: result.name,
        bankCode: result.bankCode,
        error: result.err,
      }

      console.log(`** Issuer Information **\n`)
      printTable({ data: issuerInfo })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
