import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('addValidator', 'add Validator', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('validatorId', 'validator Id')
  .addParam('name', 'name')
  .addParam('validatorKey', 'validator Key')
  .setAction(async (taskArguments, hre) => {
    try {
      let { issuerId = '', validatorId = '', name = '', validatorKey = '' } = { ...taskArguments }

      validatorId = convertToHex({ hre, value: validatorId })
      issuerId = convertToHex({ hre, value: issuerId })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** addValidator Parameters **\n`)
      const params = {
        issuerId,
        validatorId,
        name,
      }
      Tools.printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      const deadline = await Tools.getTime()
      const sig = await PrivateKey.sig(
        validatorKey,
        ['bytes32', 'bytes32', 'bytes32', 'uint256'],
        [issuerId, validatorId, name, deadline],
      )

      const receipt = await contract.addValidator(issuerId, validatorId, name, traceId, deadline, sig[0])

      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error: any) {
      Tools.showErrorDetails({ error })
    }
  })
