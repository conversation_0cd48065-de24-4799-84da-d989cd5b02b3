import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('deployConfirmation_ibc', 'Prints contracts setted to ContractManager', {
  filePath: path.basename(__filename),
}).setAction(async (taskArguments, hre) => {
  const { network } = hre

  const TARGET = ['Error', 'OwnableIBCHandler', 'AccountSyncBridge', 'BalanceSyncBridge', 'JPYTokenTransferBridge']

  console.log(`*** Deployed Contract 情報 on ${network.name} network ***`)
  console.log('Name\t/\tAddress\t/\tVersion')
  for (let lp = 0; lp < TARGET.length; lp++) {
    const { deployed, contract } = await getContractWithSigner({ hre, contractName: TARGET[lp] })

    // Error、OwnableIBCHandler はversion()が無いため、アドレスのみ表示
    if (TARGET[lp] == 'Error' || TARGET[lp] == 'OwnableIBCHandler') {
      console.log('%s\t%s', TARGET[lp], deployed.address)
      continue
    }
    const version = await contract.version()
    console.log('%s\t %s\t %s', TARGET[lp], deployed.address, version)
  }
})
