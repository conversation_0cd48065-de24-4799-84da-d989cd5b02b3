import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('_unregisterEscrowAcc', 'unregister escrow account', { filePath: path.basename(__filename) })
  .addParam('srcZoneId', 'src zone id')
  .addParam('dstZoneId', 'dst zone id')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    let { srcZoneId = '', dstZoneId = '' } = { ...taskArguments }

    srcZoneId = Number(srcZoneId)
    dstZoneId = Number(dstZoneId)

    const { contract } = await getContractWithSigner({ hre, contractName: 'JPYTokenTransferBridge' })

    const deadline = (await Tools.getTime()) + 10
    const kmsSig = await kmsSigner.sign(['uint16', 'uint256', 'uint256'], [srcZoneId, dstZoneId, deadline])

    const receipt = await contract.unregisterEscrowAccount(srcZoneId, dstZoneId, deadline, kmsSig)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
