import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('addIssuerRole', 'add issuer role', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre
    const kmsSigner = kmsSignerProvider({ hre })

    const traceId = ethers.toBeHex(ethers.toBigInt(ethers.toUtf8Bytes('trace1'))).padEnd(66, '0')

    const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

    contract.connect(kmsSigner)

    let { issuerId = '', issuerKey = '' } = taskArguments

    issuerId = convertToHex({ hre, value: issuerId })

    const addrIssuer = new ethers.Wallet(issuerKey).address

    console.log(`*** add issuer role: ${issuerId}=${addrIssuer}`)

    const deadline = await Tools.getTime()
    const kmsSig = await kmsSigner.sign(['uint256', 'address', 'uint256'], [issuerId, addrIssuer, deadline])

    const receipt = await contract.addIssuerRole(issuerId, addrIssuer, traceId, deadline, kmsSig)

    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
