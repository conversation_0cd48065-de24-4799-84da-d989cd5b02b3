import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable } from './common/tools'

wrappedTask('getProvider', 'get provider', { filePath: path.basename(__filename) }).setAction(async (_, hre) => {
  try {
    const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

    const provider = await contract.getProvider()

    console.log(`** Provider Information **\n`)
    const formattedProvider = {
      'Provider ID': provider.providerId,
      'Zone ID': provider.zoneId,
      'Zone Name': provider.zoneName,
    }
    printTable({ data: formattedProvider })
  } catch (error: any) {
    console.error(error)
  }
})
