import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('setBizZoneTerminated', 'Set Terminated BusinessZone Account.', {
  filePath: path.basename(__filename),
})
  .addParam('zoneId', 'zone id')
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    let { zoneId = '', accountId = '' } = { ...taskArguments }
    accountId = convertToHex({ hre, value: accountId })

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

    const receipt = await contract.setBizZoneTerminated(zoneId, accountId, traceId)

    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
