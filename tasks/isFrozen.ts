import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('isFrozen', 'Check if account is frozen', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    try {
      let { accountId = '' } = { ...taskArguments }
      accountId = convertToHex({ hre, value: accountId })

      console.log(`** isFrozen Parameters **\n`)
      const params = {
        accountId: accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

      const receipt = await contract.isFrozen(accountId)

      const formattedReceipt = {
        result: receipt.frozen ? 'frozen' : 'not frozen',
        reason: receipt.err,
      }

      console.log(`** isFrozen Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
