import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getDestinationAccount', 'get DestinationAccount', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account Id')
  .setAction(async (taskArguments, hre) => {
    try {
      let { accountId = '' } = { ...taskArguments }
      accountId = convertToHex({ hre, value: accountId })

      console.log(`** getDestinationAccount Parameters **\n`)
      const params = {
        accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      const result = await contract.getDestinationAccount(accountId)

      const accountInfo = {
        accountName: result.accountName,
        error: result.err,
      }

      console.log(`** Destination Account Information **\n`)
      printTable({ data: accountInfo })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
