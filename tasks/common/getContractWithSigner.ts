import { HardhatRuntimeEnvironment } from 'hardhat/types'

type GetContractWithSignerParams = {
  hre: HardhatRuntimeEnvironment
  contractName: string
  hasSigner?: boolean
  hasDeployer?: boolean
}

export const getContractWithSigner = async ({ hre, contractName }: GetContractWithSignerParams) => {
  const { ethers, deployments, getNamedAccounts } = hre

  const contractDeployed = await deployments.get(contractName)
  const contract = await ethers.getContractAt(contractName, contractDeployed.address)

  const { deployer: deployerAccount } = await getNamedAccounts()
  const deployerHardhat = await ethers.getSigner(deployerAccount)
  await contract.connect(deployerHardhat)
  deployerHardhat

  return {
    deployed: contractDeployed,
    contract,
    deployer: deployerHardhat,
  }
}
