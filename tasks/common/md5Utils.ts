import crypto from 'crypto'
import * as fs from 'fs'

type md5Params = {
  obj: string
  item: string
  network: string
}

export const md5 = async ({ obj, item, network }: md5Params): Promise<void> => {
  const lockFilePath = `./scripts/backup-restore/backupfiles/${network}/md5.lock`
  const md5FilePath = `./scripts/backup-restore/backupfiles/${network}/md5.json`

  while (fs.existsSync(lockFilePath)) {
    await new Promise((resolve) => setTimeout(resolve, 100)) // 100ms 대기
  }
  await fs.writeFileSync(lockFilePath, '')

  try {
    const hash = crypto.createHash('md5').update(obj).digest('hex')

    const md5Obj = JSON.parse(fs.readFileSync(md5FilePath, 'utf8'))
    md5Obj[item] = hash
    await fs.writeFileSync(md5FilePath, JSON.stringify(md5Obj), 'utf8')
  } finally {
    fs.unlinkSync(lockFilePath)
  }
}

type md5ValidParams = {
  obj: string
  item: string
  network: string
}

export const md5Valid = ({ obj, item, network }: md5ValidParams): boolean => {
  const hash = crypto.createHash('md5').update(obj).digest('hex')
  const md5Obj = JSON.parse(fs.readFileSync(`./scripts/backup-restore/backupfiles/${network}/md5.json`, 'utf8'))
  return hash === md5Obj[item]
}
