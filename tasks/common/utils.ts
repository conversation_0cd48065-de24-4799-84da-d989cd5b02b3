export const checkArraysEqual = (arr1, arr2) => {
  return (
    Array.isArray(arr1) &&
    Array.isArray(arr2) &&
    arr1.length === arr2.length &&
    JSON.stringify(arr1) === JSON.stringify(arr2)
  )
}

export const sortArrays = (params) => {
  const result = params.map((v, i) => {
    return v.arr.sort((a, b) => {
      return a[v.key] - b[v.key]
    })
  })
  return result
}

export const getItemArray = (arr, key) => {
  return arr.map((v) => v[key])
}
