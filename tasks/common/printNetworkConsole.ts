import { HardhatRuntimeEnvironment } from 'hardhat/types'

type PrintNetworkConsole = {
  hre: HardhatRuntimeEnvironment
  path?: string
}

export const printNetworkConsole = ({ hre, path }: PrintNetworkConsole) => {
  const { network } = hre
  console.log('***********************************************')
  console.log(`* Execute function on ${network.name} network *`)
  if (path) {
    console.log(`* Execute function on ${path} *`)
  }
  console.log('***********************************************')
}
