import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('registerIssuer', 'register issuer', {
  filePath: path.basename(__filename),
})
  .addParam('issuerId', 'issuer id')
  .addParam('bankCode', 'bank code')
  .addParam('issuerName', 'issuer name')
  .addParam('issuerKey', 'issuer key')
  .addParam('flag', 'flag')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    const kmsSigner = kmsSignerProvider({ hre })

    let { issuerKey = '', issuerId = '', bankCode = '', issuerName = '', flag = '' } = { ...taskArguments }
    issuerId = convertToHex({ hre, value: issuerId })

    const traceId = convertToHex({ hre, value: 'trace1' })

    const addrIssuer = new ethers.Wallet(issuerKey).address

    const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

    if (flag[0] === '1') {
      console.log(`*** issuerID登録: ${issuerId}`)
      const deadline = await Tools.getTime()
      const kmsSig = await kmsSigner.sign(
        ['bytes32', 'uint16', 'string', 'uint256'],
        [issuerId, bankCode, issuerName, deadline],
      )
      const receipt = await contract.addIssuer(issuerId, bankCode, issuerName, traceId, deadline, kmsSig)
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }

    if (flag[1] === '1') {
      console.log(`*** issuer権限登録: ${issuerId}=${addrIssuer}`)
      const deadline = await Tools.getTime()
      const kmsSig = await kmsSigner.sign(['uint256', 'address', 'uint256'], [issuerId, addrIssuer, deadline])
      const receipt = await contract.addIssuerRole(issuerId, addrIssuer, traceId, deadline, kmsSig)
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
  })
