import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails, showEthersRes } from '@tasks/common/tools'
import path from 'path'

wrappedTask('dvpMulti', 'deliver two nfts vs pay token', { filePath: path.basename(__filename) })
  .addParam('sendAccountId', 'send account id')
  .addParam('fromAccountId', 'from account id')
  .addParam('toAccountId', 'to account id')
  .addParam('amount', 'amount')
  .addParam('tokenId1', 'tokenId1')
  .addParam('tokenId2', 'tokenId2')
  .addParam('memo', 'memo')
  .setAction(async (args, hre) => {
    const tokenId1 = args.tokenId1
    const tokenId2 = args.tokenId2
    const misc2 = `${tokenId1},${tokenId2}`

    const params = {
      sendAccountId: convertToHex({ hre, value: args.sendAccountId }),
      fromAccountId: convertToHex({ hre, value: args.fromAccountId }),
      toAccountId: convertToHex({ hre, value: args.toAccountId }),
      amount: args.amount,
      misc1: convertToHex({ hre, value: 'renewable' }),
      misc2: String(misc2),
      memo: args.memo,
    }

    console.log(`** Input parameter Information **\n`)
    printTable({ data: params })

    const traceId = convertToHex({ hre, value: 'trace1' })

    try {
      const { contract } = await getContractWithSigner({
        hre,
        contractName: 'RenewableEnergyToken',
      })
      const receipt = await contract.customTransfer(
        params.sendAccountId,
        params.fromAccountId,
        params.toAccountId,
        params.amount,
        params.misc1,
        params.misc2,
        params.memo,
        traceId,
      )

      console.log(`** renewableEnergyToken.customTransfer(multi token transfer) receipt Information **\n`)
      const res = await receipt.wait()
      showEthersRes({ res })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
