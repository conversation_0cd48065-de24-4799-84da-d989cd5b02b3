import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from '@tasks/common/tools'
import path from 'path'

wrappedTask('getTokenCount', 'get renewable token count', { filePath: path.basename(__filename) }).setAction(
  async (args, hre) => {
    try {
      const { contract } = await getContractWithSigner({
        hre,
        contractName: 'RenewableEnergyToken',
      })

      const receipt = await contract.getTokenCount()

      const formattedReceipt = {
        tokenCount: receipt.toString(),
      }
      console.log(`** getTokenCount receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  },
)
