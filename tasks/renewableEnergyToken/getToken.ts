import { envVers } from '@/envVers'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from '@tasks/common/tools'
import { TokenStatus } from '@tasks/define/tokenStatusEnum'
import path from 'path'

wrappedTask('getToken', 'get token by token id', { filePath: path.basename(__filename) })
  .addParam('tokenId', 'token id')
  .setAction(async (args, hre) => {
    const tokenId = convertToHex({ hre, value: args.tokenId || envVers.localTest.renewableId1 })

    console.log(`** getToken Parameters **\n`)
    const params = {
      tokenId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({
        hre,
        contractName: 'RenewableEnergyToken',
      })
      const receipt = (await contract.getToken(tokenId))[0]

      const formattedReceipt = {
        tokenStatus: receipt.tokenStatus + ' (' + TokenStatus[receipt.tokenStatus] + ')',
        metadataId: receipt.metadataId,
        metadataHash: receipt.metadataHash,
        mintAccountId: receipt.mintAccountId,
        ownerAccountId: receipt.ownerAccountId,
        previousAccountId: receipt.previousAccountId,
        isLocked: receipt.isLocked,
      }
      console.log(`** getToken receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
