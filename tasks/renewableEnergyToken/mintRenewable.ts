import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails, showEthersRes } from '@tasks/common/tools'
import path from 'path'

wrappedTask('mintRenewable', 'mint renewable energy token', { filePath: path.basename(__filename) })
  .addParam('tokenId', 'token id')
  .addParam('metadataId', 'metadata id')
  .addParam('metadataHash', 'metadata hash')
  .addParam('mintAccountId', 'mintAccountId')
  .addParam('ownerAccountId', 'ownerAccountId')
  .setAction(async (args, hre) => {
    const tokenId = convertToHex({ hre, value: args.tokenId || Math.random().toString(36).substring(2, 7) })
    const metadataId = convertToHex({ hre, value: args.metadataId })
    const metadataHash = convertToHex({ hre, value: args.metadataHash })
    const mintAccountId = convertToHex({ hre, value: args.mintAccountId })
    const ownerAccountId = convertToHex({ hre, value: args.ownerAccountId })

    const isLocked = false
    const traceId = convertToHex({ hre, value: 'trace1' })

    const params = {
      'Token ID': tokenId,
      'Metadata ID': metadataId,
      'Metadata Hash': metadataHash,
      'Mint Account ID': mintAccountId,
      'Owner Account ID': ownerAccountId,
      'Is Locked': isLocked,
      'Trace ID': traceId,
    }

    console.log(`** Input parameter Information **\n`)

    printTable({ data: params })

    const { contract } = await getContractWithSigner({
      hre,
      contractName: 'RenewableEnergyToken',
    })

    try {
      const receipt = await contract.mint(
        tokenId,
        metadataId,
        metadataHash,
        mintAccountId,
        ownerAccountId,
        isLocked,
        traceId,
      )

      console.log(`** renewableEnergyToken.mint receipt Information **\n`)
      const res = await receipt.wait()
      showEthersRes({ res })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
