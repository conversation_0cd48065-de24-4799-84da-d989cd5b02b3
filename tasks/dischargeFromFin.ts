import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printSection, printTable, showErrorDetails, showEthersRes } from '@tasks/common/tools'
import path from 'path'
import web3 from 'web3'

wrappedTask('dischargeFromFin', 'Discharge DCJPY from Biz to Fin, but started from Fin.', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'account id')
  .addParam('fromZoneId', 'from zone id')
  .addParam('toZoneId', 'to zone id')
  .addParam('amount', 'amount')
  .addParam('timeoutHeight', 'timeoutHeight')
  .setAction(async (args, hre) => {
    const { network, getNamedAccounts, ethers } = hre

    printSection({ title: `Execute function on ${network.name} network: ${path.basename(__filename)}` })

    const accountId = ethers.toBeHex(ethers.toBigInt(ethers.toUtf8Bytes(args.accountId))).padEnd(66, '0')
    const fromZoneId = Number(args.fromZoneId)
    const toZoneId = Number(args.toZoneId)
    const amount = Number(args.amount)
    const timeoutHeight = Number(args.timeoutHeight)
    const traceId = ethers.toBeHex(ethers.toBigInt(ethers.toUtf8Bytes('traceId'))).padEnd(66, '0')

    const params = {
      'Account Id': accountId,
      'From ZoneId': fromZoneId,
      'To ZoneId': toZoneId,
      amount: amount,
      'timeout Height': timeoutHeight,
      'Trace ID': traceId,
    }

    console.log(`** Input parameter Information **\n`)
    printTable({ data: params })

    const toUint256HexPadded = (x) => web3.utils.padLeft(web3.utils.toHex(x), 64)

    const bridgeContractDeployed = await hre.deployments.get('JPYTokenTransferBridge')
    const bridgeContract = await ethers.getContractAt('JPYTokenTransferBridge', bridgeContractDeployed.address)
    const { signer1 } = await getNamedAccounts()
    const signer1Hardhat = await ethers.getSigner(signer1)
    bridgeContract.connect(signer1Hardhat)

    try {
      const receipt = await bridgeContract.discharge(accountId, fromZoneId, toZoneId, amount, timeoutHeight, traceId)
      console.log(`** JPYTokenTransferBridge.transfer receipt Information **\n`)
      const res = await receipt.wait()
      showEthersRes({ res })

      const packetSequences = toUint256HexPadded(receipt)
      console.log(packetSequences)
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
