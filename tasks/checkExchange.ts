import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('checkExchange', 'Check if an account can charge.', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .addParam('fromZoneId', 'from zone id')
  .addParam('toZoneId', 'to zone id')
  .addParam('amount', 'amount')
  .setAction(async (taskArguments, hre) => {
    let { accountId = '', fromZoneId = '', toZoneId = '', amount = '' } = { ...taskArguments }
    accountId = convertToHex({ hre, value: accountId })
    toZoneId = Number(toZoneId)
    fromZoneId = Number(fromZoneId)
    amount = Number(amount)

    console.log(`** checkExchange Parameters **\n`)
    const params = {
      accountId: accountId,
      fromZoneId: fromZoneId,
      toZoneId: toZoneId,
      amount: amount,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'FinancialCheck' })

      const receipt = await contract.checkExchange(accountId, fromZoneId, toZoneId, amount)

      const formattedReceipt = {
        result: receipt.success ? 'ok' : 'failed',
        reason: receipt.err,
      }

      console.log(`** checkExchange receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
