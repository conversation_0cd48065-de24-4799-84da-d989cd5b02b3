import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable } from './common/tools'

wrappedTask('getAccountInfo', 'Gets the Information for account ID.', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'account id')
  .addParam('validId', 'validator id')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    let { accountId = '', validId = '' } = { ...taskArguments }
    accountId = convertToHex({ hre, value: accountId })
    const validatorId = convertToHex({ hre, value: validId })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })
    const receipt = await contract.getAccount(validatorId, accountId)

    const accountInfo = {
      accountId: ethers.toUtf8String(accountId),
      accountName: ethers.toUtf8String(receipt.accountName.toString()),
      accountStatus: ethers.toUtf8String(receipt.accountStatus.toString()),
      balance: receipt.balance.toString(),
      appliedAt: receipt.appliedAt.toString(),
      registeredAt: receipt.registeredAt.toString(),
      terminatingAt: receipt.terminatingAt.toString(),
      terminatedAt: receipt.terminatedAt.toString(),
    }

    console.log(`*** Account Information: ${ethers.toUtf8String(accountId)}`)
    printTable({ data: accountInfo })
  })
