import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('transferSingleMeasureGas', 'single transfer gas measurement', {
  filePath: path.basename(__filename),
})
  .addParam('from', 'from account id')
  .addParam('to', 'to account id')
  .addParam('amount', 'transfer amount')
  .addParam('misc1', 'misc1')
  .addParam('misc2', 'misc2')
  .addParam('memo', 'memo')
  .addParam('traceId', 'traceId')
  .addParam('transferCount', 'transfer count')
  .setAction(async (taskArguments, hre) => {
    // parameters
    let {
      from = '',
      to = '',
      amount = '',
      misc1 = '',
      misc2 = '',
      memo = '',
      traceId = '',
      transferCount = '',
    } = { ...taskArguments }

    from = convertToHex({ hre, value: from })
    to = convertToHex({ hre, value: to })
    misc1 = convertToHex({ hre, value: misc1 })
    misc2 = convertToHex({ hre, value: misc2 })
    memo = convertToHex({ hre, value: memo })
    traceId = convertToHex({ hre, value: traceId })

    const gasUsed: any = []
    const senderAccount = from

    // await tokenContract.connect(signer1Hardhat)
    const { contract } = await getContractWithSigner({ hre, contractName: 'Token' })

    for (let count = 0; transferCount > count; count++) {
      const receipt = await contract.transferSingle(senderAccount, from, to, amount, misc1, misc2, memo, traceId)
      await receipt.wait().then((receipt) => {
        if (receipt) {
          gasUsed.push(receipt.gasUsed)
        } else {
          console.log(`transferSingle(count=${gasUsed.length}): transaction receipt is null`)
        }
      })
    }

    console.log(
      `transferSingle(count=${gasUsed.length}): ${gasUsed.reduce((sum, e) => sum + e, BigInt(0))} [${gasUsed}]`,
    )
  })
