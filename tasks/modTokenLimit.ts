import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('modTokenLimit', 'mod Token Limit', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('itemFlgs', 'item Flgs')
  .addParam('limitAmounts', 'limit Amounts')
  .addParam('issuerKey', 'issuer Key')
  .setAction(async (taskArguments, hre) => {
    try {
      let { accountId = '', issuerId = '', itemFlgs = '', limitAmounts = '', issuerKey = '' } = { ...taskArguments }

      accountId = convertToHex({ hre, value: accountId })
      issuerId = convertToHex({ hre, value: issuerId })
      const traceId = convertToHex({ hre, value: 'trace1' })

      itemFlgs = JSON.parse(itemFlgs)
      limitAmounts = JSON.parse(limitAmounts)

      console.log(`** modTokenLimit Parameters **\n`)
      const params = {
        issuerId,
        accountId,
        itemFlgs,
        limitAmounts,
      }
      Tools.printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

      const deadline = await Tools.getTime()
      const sig = await PrivateKey.sig(
        issuerKey,
        ['bytes32', 'bytes32', 'bool[]', 'uint256[]', 'uint256'],
        [issuerId, accountId, itemFlgs, limitAmounts, deadline],
      )

      const receipt = await contract.modTokenLimit(
        issuerId,
        accountId,
        itemFlgs,
        limitAmounts,
        traceId,
        deadline,
        sig[0],
      )

      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error: any) {
      Tools.showErrorDetails({ error })
    }
  })
