import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('isTerminated', 'is Terminated', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account Id')
  .setAction(async (taskArguments, hre) => {
    try {
      let { accountId = '' } = { ...taskArguments }
      accountId = convertToHex({ hre, value: accountId })

      console.log(`** isTerminated Parameters **\n`)
      const params = {
        accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Account' })

      const receipt = await contract.isTerminated(accountId)

      console.log(`** isTerminated Receipt Information **\n`)
      printTable({ data: receipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
