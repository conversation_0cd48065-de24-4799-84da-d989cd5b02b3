import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('addProviderRole', 'add provider role', { filePath: path.basename(__filename) })
  .addParam('provId', 'provider id')
  .addParam('provKey', 'provider key')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre
    const kmsSigner = kmsSignerProvider({ hre })

    const traceId = ethers.toBeHex(ethers.toBigInt(ethers.toUtf8Bytes('trace1'))).padEnd(66, '0')

    const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

    contract.connect(kmsSigner)

    let { provId = '', provKey = '' } = taskArguments

    provId = convertToHex({ hre, value: provId })

    const addrProv = new ethers.Wallet(provKey).address

    console.log(`*** add provider role: ${provId}=${addrProv}`)

    const deadline = await Tools.getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'address', 'uint256'], [provId, addrProv, deadline])

    const receipt = await contract.addProviderRole(provId, addrProv, traceId, deadline, kmsSig)

    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
