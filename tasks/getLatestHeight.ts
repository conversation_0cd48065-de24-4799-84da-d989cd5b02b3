import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('getLatestHeight', 'Get latest height stored in Yui contracts', {
  filePath: path.basename(__filename),
})
  .addParam('clientId', 'client id for yui contracts')
  .setAction(async (taskArguments, hre) => {
    const { clientId = '' } = { ...taskArguments }

    const { contract } = await getContractWithSigner({ hre, contractName: 'IBFT2Client' })

    const result = await contract.getLatestHeight(clientId)
    console.log(`revision_number: ${result[0][0].toString()}`)
    console.log(`revision_height: ${result[0][1].toString()}`)
  })
