import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as utils from '@test/common/utils'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('recoverPacket', 'set main contract address for bridge contract', {
  filePath: path.basename(__filename),
})
  .addParam('packetData', 'packet data')
  .addParam('contractName', 'contract name')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })
    const { packetData = '', contractName = '' } = { ...taskArguments }

    let bridgeContract
    let deployer1
    let deadline
    let msgSalt
    let kmsSig

    if (contractName === 'AccountSyncBridge') {
      const { contract, deployer } = await getContractWithSigner({
        hre,
        contractName: 'AccountSyncBridge',
      })
      contract.connect(kmsSigner)
      deadline = (await Tools.getTime()) + 10
      msgSalt = utils.toBytes32('accountSync')
      kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])
      bridgeContract = contract
      deployer1 = deployer
    } else if (contractName === 'JPYTokenTransferBridge') {
      const { contract, deployer } = await getContractWithSigner({
        hre,
        contractName: 'JPYTokenTransferBridge',
      })
      contract.connect(kmsSigner)
      deadline = (await Tools.getTime()) + 10
      msgSalt = utils.toBytes32('jpyTokenTransfer')
      kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])
      bridgeContract = contract
      deployer1 = deployer
    } else if (contractName === 'BalanceSyncBridge') {
      const { contract, deployer } = await getContractWithSigner({
        hre,
        contractName: 'BalanceSyncBridge',
      })
      contract.connect(kmsSigner)
      deadline = (await Tools.getTime()) + 10
      msgSalt = utils.toBytes32('balanceSync')
      kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])
      bridgeContract = contract
      deployer1 = deployer
    } else {
      console.log('Invalid contract name')
      return
    }

    const res = await bridgeContract.recoverPacket(packetData, deployer1, deadline, kmsSig)
    await res
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
