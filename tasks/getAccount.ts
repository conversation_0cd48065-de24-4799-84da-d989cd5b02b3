import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getAccount', 'get Account', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer Id')
  .addParam('accountId', 'account Id')
  .setAction(async (taskArguments, hre) => {
    try {
      let { issuerId = '', accountId = '' } = { ...taskArguments }
      issuerId = convertToHex({ hre, value: issuerId })
      accountId = convertToHex({ hre, value: accountId })

      console.log(`** getAccount Parameters **\n`)
      const params = {
        issuerId,
        accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

      const result = await contract.getAccount(issuerId, accountId)

      const accountInfo = {
        accountName: result.accountName,
        balance: result.balance,
        accountStatus: result.accountStatus,
        reasonCode: result.reasonCode,
        error: result.err,
      }

      console.log(`** Account Information **\n`)
      printTable({ data: accountInfo })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
