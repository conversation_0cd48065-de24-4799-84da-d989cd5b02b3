import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('hasIssuer', 'Check if issuer exists', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .setAction(async (taskArguments, hre) => {
    let { issuerId = '' } = { ...taskArguments }
    issuerId = convertToHex({ hre, value: issuerId })

    console.log(`** hasIssuer Parameters **\n`)
    const params = {
      issuerId: issuerId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

      const receipt = await contract.hasIssuer(issuerId)

      const formattedReceipt = {
        result: receipt.success ? 'ok' : 'failed',
        reason: receipt.err,
      }

      console.log(`** hasIssuer Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
