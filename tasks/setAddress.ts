import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as utils from '@test/common/utils'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('setAddress', 'set main contract address for bridge contract', {
  filePath: path.basename(__filename),
})
  .addParam('validatorAddress', 'validator address')
  .addParam('ibcTokenAddress', 'token service address')
  .addParam('accountAddress', 'account address')
  .addParam('accessCtrlAddress', 'access ctrl address')
  .addParam('businessZoneAccountAddress', 'business zone account address')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    let {
      validatorAddress = '',
      ibcTokenAddress = '',
      accountAddress = '',
      accessCtrlAddress = '',
      businessZoneAccountAddress = '',
    } = { ...taskArguments }

    console.log(`VALIDATOR_ADDRESS= ${validatorAddress}`)
    console.log(`IBC_TOKEN_ADDRESS= ${ibcTokenAddress}`)
    console.log(`ACCOUNT_ADDRESS= ${accountAddress}`)
    console.log(`ACCESS_CTRL_ADDRESS= ${accessCtrlAddress}`)
    console.log(`BUSINESS_ZONE_ACCOUNT_ADDRESS= ${businessZoneAccountAddress}`)

    const { contract: accountSyncBridge } = await getContractWithSigner({ hre, contractName: 'AccountSyncBridge' })
    const { contract: tokenTransferBridge } = await getContractWithSigner({
      hre,
      contractName: 'JPYTokenTransferBridge',
    })
    const { contract: balanceSyncBridge } = await getContractWithSigner({ hre, contractName: 'BalanceSyncBridge' })

    tokenTransferBridge.connect(kmsSigner)
    accountSyncBridge.connect(kmsSigner)
    balanceSyncBridge.connect(kmsSigner)

    let deadline
    let msgSalt
    let res
    let kmsSig

    console.log(`*** AccountSyncBridgeの参照先コントラクトアドレス更新`)
    deadline = (await Tools.getTime()) + 10
    msgSalt = utils.toBytes32('accountSync')
    kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])

    res = await accountSyncBridge.setAddress(
      validatorAddress,
      accessCtrlAddress,
      businessZoneAccountAddress,
      ibcTokenAddress,
      deadline,
      kmsSig,
    )
    await res
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))

    console.log(`*** BalanceSyncBridgeの参照先コントラクトアドレス更新`)
    deadline = (await Tools.getTime()) + 10
    msgSalt = utils.toBytes32('balanceSync')
    kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])

    res = await balanceSyncBridge.setAddress(ibcTokenAddress, accountAddress, accessCtrlAddress, deadline, kmsSig)
    await res
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))

    console.log(`*** JPYTokenTransferBridgeの参照先コントラクトアドレス更新`)
    deadline = (await Tools.getTime()) + 10
    msgSalt = utils.toBytes32('tokenTransfer')
    kmsSig = await kmsSigner.sign(['bytes32', 'uint256'], [msgSalt, deadline])

    res = await tokenTransferBridge.setAddress(ibcTokenAddress, accessCtrlAddress, deadline, kmsSig)
    await res
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
