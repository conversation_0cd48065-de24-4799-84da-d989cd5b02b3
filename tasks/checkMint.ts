import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('checkMint', 'Check if an account can mint.', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('amount', 'amount')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    let { issuerId = '', accountId = '', amount = '', issuerKey = '' } = { ...taskArguments }
    issuerId = convertToHex({ hre, value: issuerId })
    accountId = convertToHex({ hre, value: accountId })
    amount = Number(amount)

    console.log(`** checkMint Parameters **\n`)
    const params = {
      issuerId: issuerId,
      accountId: accountId,
      amount: amount,
      issuerKey: issuerKey,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

      const deadline = await Tools.getTime()
      const sig = await PrivateKey.sig(
        issuerKey,
        ['bytes32', 'bytes32', 'uint256', 'uint256'],
        [issuerId, accountId, amount, deadline],
      )

      const receipt = await contract.checkMint(issuerId, accountId, amount, deadline, sig[0])

      const formattedReceipt = {
        result: receipt.success ? 'ok' : 'failed',
        reason: receipt.err,
      }

      console.log(`** checkMint receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
