import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('hasProvider', 'Check if provider exists', { filePath: path.basename(__filename) })
  .addParam('provId', 'provider id')
  .setAction(async (taskArguments, hre) => {
    let { provId = '' } = { ...taskArguments }
    provId = convertToHex({ hre, value: provId })

    console.log(`** hasProvider Parameters **\n`)
    const params = {
      provId: provId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

      const receipt = await contract.hasProvider(provId)

      const formattedReceipt = {
        result: receipt.success ? 'ok' : 'failed',
        reason: receipt.err,
      }

      console.log(`** hasProvider Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
