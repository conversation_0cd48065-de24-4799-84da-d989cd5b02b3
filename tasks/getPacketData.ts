import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('getSendPacketData', 'Gets SendPacket event data from OwnableIBCHandler', {
  filePath: path.basename(__filename),
})
  .addParam('blockNumber', 'The block number to start looking for events')
  .setAction(async (taskArguments, hre) => {
    const { network } = hre
    const blockNumber = taskArguments.blockNumber

    console.log(`*** Fetching SendPacket events from block number ${blockNumber} on ${network.name} network ***`)

    const { contract } = await getContractWithSigner({ hre, contractName: 'OwnableIBCHandler' })

    const filter = contract.filters.SendPacket()
    const events = await contract.queryFilter(filter, blockNumber)

    if (events.length === 0) {
      console.log('No SendPacket events found')
    } else {
      events.forEach((event) => {
        console.log(`SendPacket event data: ${event.data}`)
      })
    }
  })
