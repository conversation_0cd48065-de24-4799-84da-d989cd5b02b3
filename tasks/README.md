# ./tasks

コントラクトを外部から直接実行するためのスクリプト集

Solidity内の実行したい各関数について、.tsファイルを作成し、hardhatコマンドを利用して外部からコントラクトの関数を実行することが可能となっている。

## 主な使用方法

### hardhatタスクの実行方法

```bash
npx hardhat [実行したいタスク名] --network [実行環境名] --[タスクごとの各種パラメータ名] [パラメータ]
```

`NETWORK`は、hardhat.config.tsの`networks`設定になる。  
いまは`localFin`と`localBiz`と`main`のみ.

### タスクの実行例

前提：ローカル環境のコントラクト環境構築が完了していること
下記のコマンドを実行することでlocalFin環境の特定のバリデータに紐づくAccountの一覧を取得することができる

```bash
npx hardhat getAccountList --network localFin --validator-id 8888
```

### 各taskの.tsファイルについての説明

`task()`の第一引数の値がtask名となり、npx hardhat [task名]として実行する.
`addParam()`で指定されている値がtask実行時に指定する必要があるパラメータとなる.

※tsファイル上ではキャメルケースで定義されているが、シェル上で指定する際はケバブケースとなるため注意.

```typescript
import { task } from 'hardhat/config';
import { toUtf8String } from '@ethersproject/strings/src.ts/utf8';

task('getAccountList', 'Get all Account information associated with Validator.')
  .addParam('validatorId', 'validator id')
  .setAction(async (taskArguments, hre) => {
```

contractインスタンスをawait処理を行って呼び出している下記の箇所で実際にコントラクトの呼び出しを行い、その後呼び出しの結果を整形して表示している。

```typescript
    const receipt = await validatorContract.getAccountList(validatorId, offset, limit, sort_order);

    console.log('Account List:');
    receipt[0].forEach((account) => {
      console.log(`accountId: ${account.accountId}`);
      console.log(`balance: ${account.balance.toString()}`);
      console.log(`accountStatus: ${account.accountStatus}`);
      console.log('---------------------------------------'); // アカウントごとに区切る
    });
    console.log(`Total Count: ${receipt.totalCount}`);
  });
```

### タスクの説明
* `registerEscrowAccount.ts`：初期データ登録時のバリデータIDを紐付けてアカウントを登録する
* `registerEscrowAcc.ts`：Bridgeコントラクト用のアカウントを登録する