import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('addAccount', 'add Account', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account Id')
  .addParam('validatorId', 'validator Id')
  .addParam('accountName', 'account Name')
  .setAction(async (taskArguments, hre) => {
    try {
      let { accountId = '', validatorId = '', accountName = '' } = { ...taskArguments }

      validatorId = convertToHex({ hre, value: validatorId })
      accountId = convertToHex({ hre, value: accountId })
      accountName = convertToHex({ hre, value: accountName })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** addAccount Parameters **\n`)
      const params = {
        accountId,
        validatorId,
        accountName,
      }
      Tools.printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      const receipt = await contract.addAccount(
        validatorId,
        accountId,
        accountName,
        [50000, 50000, 50000, 50000, 50000],
        traceId,
      )

      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error: any) {
      Tools.showErrorDetails({ error })
    }
  })
