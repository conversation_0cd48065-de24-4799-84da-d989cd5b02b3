import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('modIssuer', 'Modify issuer information', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'Issuer ID to modify')
  .addParam('issuerName', 'New issuer name')
  .setAction(async (taskArguments, hre) => {
    try {
      let { issuerId = '', issuerName = '' } = { ...taskArguments }
      issuerId = convertToHex({ hre, value: issuerId })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** addIssuer Parameters **\n`)
      const params = {
        issuerId,
        issuerName,
      }
      Tools.printTable({ data: params })

      const kmsSigner = kmsSignerProvider({ hre })
      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

      const deadline = await Tools.getTime()
      const kmsSig = await kmsSigner.sign(['bytes32', 'string', 'uint256'], [issuerId, issuerName, deadline])

      const receipt = await contract.modIssuer(issuerId, issuerName, traceId, deadline, kmsSig)

      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error: any) {
      Tools.showErrorDetails({ error })
    }
  })
