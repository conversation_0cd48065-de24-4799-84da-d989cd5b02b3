import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('registerProvider', 'register provider', {
  filePath: path.basename(__filename),
})
  .addParam('provId', 'provider id')
  .addParam('zoneId', 'zone id')
  .addParam('zoneName', 'zone name')
  .addParam('provKey', 'provider key')
  .addParam('flag', 'flag')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre
    const kmsSigner = kmsSignerProvider({ hre })

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

    contract.connect(kmsSigner)

    let { provId = '', zoneId = '', zoneName = '', provKey = '', flag = '' } = { ...taskArguments }

    provId = convertToHex({ hre, value: provId })

    const addrProv = new ethers.Wallet(provKey).address

    if (flag[0] === '1') {
      const provider = await contract.getProvider()
      if (parseInt(provider.providerId) != 0) {
        console.log('*** Providerがすでに登録してあるため登録不可、Prov権限登録（実行する場合）は上書きする')
        console.log('登録済みProvider ID:', provider.providerId)
      } else {
        console.log(`*** provID登録: ${provId}`)
        const deadline = await Tools.getTime()
        const kmsSig = await kmsSigner.sign(['bytes32', 'uint16', 'uint256'], [provId, zoneId, deadline])

        console.log('provId', provId)
        console.log('zoneId', zoneId)
        console.log('zoneName', zoneName)
        const receipt = await contract.addProvider(provId, zoneId, zoneName, traceId, deadline, kmsSig)

        await receipt
          .wait()
          .then((res) => {
            Tools.showEthersRes({ res })
          })
          .catch((error) => console.log(error))
      }
    }

    if (flag[1] === '1') {
      console.log(`*** prov権限登録: ${provId}=${addrProv}`)

      const deadline = await Tools.getTime()
      const kmsSig = await kmsSigner.sign(['bytes32', 'address', 'uint256'], [provId, addrProv, deadline])

      const receipt = await contract.addProviderRole(provId, addrProv, traceId, deadline, kmsSig)

      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
  })
