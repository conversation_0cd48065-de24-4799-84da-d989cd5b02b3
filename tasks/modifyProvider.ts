import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('modifyProvider', 'modify provider', {
  filePath: path.basename(__filename),
})
  .addParam('provId', 'provider id')
  .addParam('provName', 'provider name')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })
    const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })
    contract.connect(kmsSigner)

    let { provId = '', provName = '' } = { ...taskArguments }
    provName = convertToHex({ hre, value: provName })
    provId = convertToHex({ hre, value: provId })

    const traceId = convertToHex({ hre, value: 'trace1' })

    console.log(`*** provName更新: ${provId}`)
    const deadline = await Tools.getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'bytes32', 'uint256'], [provId, provName, deadline])
    console.log('provId', provId)
    console.log('provName', provName)
    const receipt = await contract.modProvider(provId, provName, traceId, deadline, kmsSig)

    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
