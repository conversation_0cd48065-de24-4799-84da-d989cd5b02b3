import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('approve', 'approve some amounts to specific account id', { filePath: path.basename(__filename) })
  .addParam('validId', 'validator id')
  .addParam('ownerId', 'owner account id')
  .addParam('spenderId', 'spender account id')
  .addParam('amount', 'approve amount')
  .setAction(async (taskArguments, hre) => {
    let { validId: validatorId = '', ownerId = '', spenderId = '', amount = '', key = '' } = { ...taskArguments }
    ownerId = convertToHex({ hre, value: ownerId })
    spenderId = convertToHex({ hre, value: spenderId })
    const validatorIdHex = convertToHex({ hre, value: validatorId })
    amount = Number(amount)

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Token' })

    console.log(`*** Token approve: ${amount}`)

    const receipt = await contract.approve(validatorIdHex, ownerId, spenderId, amount, traceId)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
