import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getAllowance', 'get Allowance', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .addParam('ownerId', 'owner Id')
  .addParam('spenderId', 'spender Id')
  .setAction(async (taskArguments, hre) => {
    try {
      let { validatorId = '', ownerId = '', spenderId = '' } = { ...taskArguments }
      validatorId = convertToHex({ hre, value: validatorId })
      ownerId = convertToHex({ hre, value: ownerId })
      spenderId = convertToHex({ hre, value: spenderId })

      console.log(`** getAllowance Parameters **\n`)
      const params = {
        validatorId,
        ownerId,
        spenderId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Token' })

      const receipt = await contract.getAllowance(validatorId, ownerId, spenderId)

      const formattedReceipt = {
        allowance: receipt.allowance.toString(),
        approvedAt: receipt.approvedAt.toString(),
        error: receipt.err,
      }

      console.log(`** getAllowance Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
