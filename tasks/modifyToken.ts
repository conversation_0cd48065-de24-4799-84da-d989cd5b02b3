import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('modifyToken', 'modify token', {
  filePath: path.basename(__filename),
})
  .addParam('provKey', 'provider key')
  .addParam('tokenId', 'token id')
  .addParam('tokenName', 'token name')
  .addParam('symbol', 'symbol')
  .setAction(async (taskArguments, hre) => {
    let { provKey: providerKey = '', tokenId = '', tokenName = '', symbol = '' } = { ...taskArguments }
    tokenName = convertToHex({ hre, value: tokenName })
    tokenId = convertToHex({ hre, value: tokenId })
    symbol = convertToHex({ hre, value: symbol })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

    console.log(`*** Token登録情報更新: 0x${tokenId}`)
    const deadline = await Tools.getTime()
    const sig = await PrivateKey.sig(
      providerKey,
      ['bytes32', 'bytes32', 'bytes32', 'uint256'],
      [tokenId, tokenName, symbol, deadline],
    )

    const receipt = await contract.modToken(tokenId, tokenName, symbol, deadline, sig[0])

    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
