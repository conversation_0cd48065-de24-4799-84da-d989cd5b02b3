import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'
import { showErrorDetails } from './common/tools'

wrappedTask('addAccountRole', 'Add account role', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .addParam('accountKey', 'account key')
  .addParam('issuerKey', 'issuer key')
  .addParam('issuerId', 'issuer id')
  .setAction(async (taskArguments, hre) => {
    try {
      let { accountId = '', accountKey = '', issuerKey = '', issuerId = '' } = { ...taskArguments }

      accountId = convertToHex({ hre, value: accountId })
      issuerId = convertToHex({ hre, value: issuerId })
      const { ethers } = hre
      const addrAccount = new ethers.Wallet(accountKey).address

      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** addAccountRole Parameters **\n`)
      const params = {
        accountId,
        issuerId,
        accountAddress: addrAccount,
      }
      Tools.printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

      const deadline = await Tools.getTime()
      const sig = await PrivateKey.sig(
        issuerKey,
        ['bytes32', 'bytes32', 'address', 'uint256'],
        [issuerId, accountId, addrAccount, deadline],
      )

      const receipt = await contract.addAccountRole(issuerId, accountId, addrAccount, traceId, deadline, sig[0])

      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
