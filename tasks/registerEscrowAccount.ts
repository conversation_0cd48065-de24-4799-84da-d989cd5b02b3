import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('registerEscrowAccount', 'register escrow account', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .addParam('accountId', 'account id')
  .addParam('accountName', 'account name')
  .setAction(async (taskArguments, hre) => {
    const { validId: validatorId = '', accountId = '', accountName = '' } = { ...taskArguments }
    const limitAmounts = [5000, 5000, 5000, 5000, 5000]

    const validatorIdHex = convertToHex({ hre, value: validatorId })
    accountId = convertToHex({ hre, value: accountId })
    accountName = convertToHex({ hre, value: accountName })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

    const receipt = await contract.addAccount(validatorIdHex, accountId, accountName, limitAmounts, traceId)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
