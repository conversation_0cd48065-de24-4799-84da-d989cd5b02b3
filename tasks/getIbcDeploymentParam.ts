import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('getIbcDeploymentParam', 'Get the address of the contract required for IBC deployment.', {
  filePath: path.basename(__filename),
}).setAction(async (taskArguments, hre) => {
  console.log('*** IBC Deployment Param 情報')
  const TARGET = [
    'Token',
    'IBCToken',
    'Validator',
    'Provider',
    'Account',
    'Issuer',
    'BusinessZoneAccount',
    'FinancialZoneAccount',
    'FinancialCheck',
    'ContractManager',
    'AccessCtrl',
  ]
  for (let lp = 0; lp < TARGET.length; lp++) {
    const { contract, deployed } = await getContractWithSigner({ hre, contractName: TARGET[lp] })
    const version = await contract.version()
    console.log('%s\t%s\t%s', TARGET[lp], deployed.address, version)
  }
})
