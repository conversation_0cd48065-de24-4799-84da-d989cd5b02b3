import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import web3 from 'web3'

wrappedTask('syncAccount', 'Sync account status from Biz zone to Fin zone.', {
  filePath: path.basename(__filename),
})
  .addParam('validatorId', 'validator id')
  .addParam('accountId', 'account id')
  .addParam('accountName', 'account name')
  .addParam('fromZoneId', 'from zone id')
  .addParam('zoneName', 'zone name')
  .addParam('accountStatus', 'account status')
  .addParam('reasonCode', 'reasonCode')
  .addParam('approvalAmount', 'Approved amount')
  .addParam('traceId', 'trace id')
  .addParam('timeoutHeight', 'timeoutHeight')
  .setAction(async (taskArguments, hre) => {
    let {
      validatorId = '',
      accountId = '',
      accountName = '',
      fromZoneId = '',
      zoneName = '',
      accountStatus = '',
      reasonCode = '',
      approvalAmount = '0',
      traceId = '',
      timeoutHeight = '',
    } = { ...taskArguments }

    const validatorIdHex = convertToHex({ hre, value: validatorId })
    accountId = convertToHex({ hre, value: accountId })
    accountName = convertToHex({ hre, value: accountName })
    fromZoneId = Number(fromZoneId)
    accountStatus = convertToHex({ hre, value: accountStatus })
    reasonCode = convertToHex({ hre, value: reasonCode })
    traceId = convertToHex({ hre, value: traceId })
    timeoutHeight = Number(timeoutHeight)

    const toUint256HexPadded = (x) => web3.utils.padLeft(web3.utils.toHex(x), 64)

    const { contract } = await getContractWithSigner({ hre, contractName: 'AccountSyncBridge' })

    const packetSequences = toUint256HexPadded(
      await contract.syncAccount(
        validatorIdHex,
        accountId,
        accountName,
        fromZoneId,
        zoneName,
        accountStatus,
        reasonCode,
        approvalAmount,
        traceId,
        timeoutHeight,
      ),
    )
    console.log(packetSequences)
  })
