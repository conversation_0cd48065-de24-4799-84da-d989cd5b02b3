import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getTokenInfo', 'get Token', { filePath: path.basename(__filename) })
  .addParam('providerId', 'provider Id')
  .setAction(async (taskArguments, hre) => {
    try {
      let { providerId = '' } = { ...taskArguments }
      providerId = convertToHex({ hre, value: providerId })

      console.log(`** getToken Parameters **\n`)
      const params = {
        providerId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

      const result = await contract.getToken(providerId)

      const tokenInfo = {
        tokenId: result.tokenId,
        name: result.name,
        symbol: result.symbol,
        totalSupply: result.totalSupply,
        enabled: result.enabled,
        error: result.err,
      }

      console.log(`** Token Information **\n`)
      printTable({ data: tokenInfo })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
