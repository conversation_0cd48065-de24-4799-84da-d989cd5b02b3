import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('modifyIssuer', 'register issuer', {
  filePath: path.basename(__filename),
})
  .addParam('issuerId', 'issuer id')
  .addParam('issuerName', 'issuer name')
  .setAction(async (taskArguments, hre) => {
    let { issuerId = '', issuerName = '' } = { ...taskArguments }
    issuerId = convertToHex({ hre, value: issuerId })
    const kmsSigner = kmsSignerProvider({ hre })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })
    contract.connect(kmsSigner)

    const traceId = convertToHex({ hre, value: 'trace1' })

    console.log(`*** issuerName更新: ${issuerName.toString(16)}`)
    const deadline = await Tools.getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'string', 'uint256'], [issuerId, issuerName, deadline])
    const receipt = await contract.modIssuer(issuerId, issuerName, traceId, deadline, kmsSig)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
