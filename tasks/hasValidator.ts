import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('hasValidator', 'Check if validator exists', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator id')
  .setAction(async (taskArguments, hre) => {
    let { validatorId = '' } = { ...taskArguments }
    validatorId = convertToHex({ hre, value: validatorId })

    console.log(`** hasValidator Parameters **\n`)
    const params = {
      validatorId: validatorId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      const receipt = await contract.hasValidator(validatorId)

      const formattedReceipt = {
        result: receipt.success ? 'ok' : 'failed',
        reason: receipt.err,
      }

      console.log(`** hasValidator Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
