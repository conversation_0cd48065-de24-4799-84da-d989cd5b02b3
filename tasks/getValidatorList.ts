import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable } from './common/tools'

wrappedTask('getValidatorList', 'Gets the Information for account ID.', {
  filePath: path.basename(__filename),
}).setAction(async (taskArguments, hre) => {
  const offset = 0
  const limit = 100

  const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

  const receipt = await contract.getValidatorList(limit, offset)
  console.log('Validator List:')
  receipt[0].forEach((validator, index) => {
    const validatorInfo = {
      validatorId: validator.validatorId,
      name: validator.name,
      issuerId: validator.issuerId,
    }

    console.log(`--- Validator ${index + 1} ---`)
    printTable({ data: validatorInfo })
  })

  console.log(`Total Count: ${receipt.totalCount}`)
})
