import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('registerAcc', 'register account', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'id')
  .addParam('accountName', 'name')
  .addParam('accountKey', 'key')
  .addParam('issuerKey', 'issuer key')
  .addParam('issuerId', 'issuer id')
  .addParam('validId', 'validetor id')
  .addParam('flag', 'flag')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    let {
      accountKey = '',
      accountId = '',
      accountName = '',
      issuerKey = '',
      issuerId = '',
      validId: validatorId = '',
      flag = '',
    } = { ...taskArguments }
    issuerId = convertToHex({ hre, value: issuerId })
    accountId = convertToHex({ hre, value: accountId })
    accountName = convertToHex({ hre, value: accountName })
    const validatorIdHex = convertToHex({ hre, value: validatorId })

    const traceId = convertToHex({ hre, value: 'trace1' })

    const addrAccount = new ethers.Wallet(accountKey).address

    const { contract: issuerContract } = await getContractWithSigner({ hre, contractName: 'Issuer' })
    const { contract: validatorContract } = await getContractWithSigner({
      hre,
      contractName: 'Validator',
    })

    if (flag[0] === '1') {
      console.log(`*** accountID登録: ${accountId}`)
      const receipt = await validatorContract.addAccount(
        validatorIdHex,
        accountId,
        accountName,
        [50000, 50000, 50000, 50000, 50000],
        traceId,
      )
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }

    if (flag[1] === '1') {
      console.log(`*** accountID権限登録: ${accountId}=${addrAccount}`)

      const deadline = await Tools.getTime()
      const sig = await PrivateKey.sig(
        issuerKey,
        ['bytes32', 'bytes32', 'address', 'uint256'],
        [issuerId, accountId, addrAccount, deadline],
      )

      const receipt = await issuerContract.addAccountRole(issuerId, accountId, addrAccount, traceId, deadline, sig[0])
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
  })
