import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('checkFinAccountStatus', 'Check fin account status.', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    let { accountId = '' } = { ...taskArguments }
    accountId = convertToHex({ hre, value: accountId })

    console.log(`** checkFinAccountStatus Parameters **\n`)
    const params = {
      accountId: accountId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'FinancialCheck' })

      const receipt = await contract.checkFinAccountStatus(accountId)

      const formattedReceipt = {
        accountStatus: ethers.toUtf8String(receipt.accountStatus.toString()),
        reason: receipt.err,
      }

      console.log(`** checkFinAccountStatus receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
