import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('registerToken', 'register token', {
  filePath: path.basename(__filename),
})
  .addParam('provKey', 'provider key')
  .addParam('tokenId', 'token id')
  .addParam('provId', 'provider id')
  .addParam('tokenName', 'token name')
  .addParam('symbol', 'symbol')
  .setAction(async (taskArguments, hre) => {
    let {
      provKey: providerKey = '',
      tokenId = '',
      provId: providerId = '',
      tokenName = '',
      symbol = '',
    } = { ...taskArguments }
    tokenName = convertToHex({ hre, value: tokenName })
    tokenId = convertToHex({ hre, value: tokenId })
    const providerIdHex = convertToHex({ hre, value: providerId })
    symbol = convertToHex({ hre, value: symbol })
    // const deposited = false;

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

    console.log(`*** Token登録: 0x${tokenId}`)
    const deadline = await Tools.getTime()
    const sig = await PrivateKey.sig(
      providerKey,
      ['bytes32', 'bytes32', 'bytes32', 'bytes32', 'uint256'],
      [providerIdHex, tokenId, tokenName, symbol, deadline],
    )

    const receipt = await contract.addToken(providerIdHex, tokenId, tokenName, symbol, traceId, deadline, sig[0])

    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
