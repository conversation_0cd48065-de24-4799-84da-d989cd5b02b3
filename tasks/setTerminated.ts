import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'
import { printTable } from './common/tools'

wrappedTask('setTerminated', 'set Terminated', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .addParam('accountId', 'account Id')
  .addParam('reasonCode', 'reason Code')
  .setAction(async (taskArguments, hre) => {
    try {
      let { validatorId = '', accountId = '', reasonCode = '' } = { ...taskArguments }
      validatorId = convertToHex({ hre, value: validatorId })
      accountId = convertToHex({ hre, value: accountId })
      reasonCode = convertToHex({ hre, value: reasonCode })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** setTerminated Parameters **\n`)
      const params = {
        validatorId,
        accountId,
        reasonCode,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      const receipt = await contract.setTerminated(validatorId, accountId, reasonCode, traceId)

      console.log(`** setTerminated Receipt Information **\n`)
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error: any) {
      Tools.showErrorDetails({ error })
    }
  })
