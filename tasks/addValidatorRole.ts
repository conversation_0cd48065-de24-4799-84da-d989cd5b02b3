import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('addValidatorRole', 'add validator role', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator id')
  .addParam('validatorKey', 'validator key')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre
    const kmsSigner = kmsSignerProvider({ hre })

    let { validatorId = '', validatorKey = '' } = taskArguments
    validatorId = convertToHex({ hre, value: validatorId })

    const addrValidator = new ethers.Wallet(validatorKey).address

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract: validatorContract } = await getContractWithSigner({ hre, contractName: 'Validator' })

    console.log(`*** add validator role: ${validatorId}=${addrValidator}`)

    const deadline = await Tools.getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'address', 'uint256'], [validatorId, addrValidator, deadline])

    const receipt = await validatorContract.addValidatorRole(validatorId, addrValidator, traceId, deadline, kmsSig)

    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
