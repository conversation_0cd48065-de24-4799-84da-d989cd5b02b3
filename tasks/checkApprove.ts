import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as utils from '@test/common/utils'
import BigNumber from 'bignumber.js'
import path from 'path'
import * as Tools from './common/tools'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('checkApprove', 'Check if an account can approve.', { filePath: path.basename(__filename) })
  .addParam('validId', 'validator id')
  .addParam('ownerId', 'owner id')
  .addParam('spenderId', 'spender id')
  .addParam('amount', 'amount')
  .addParam('validKey', 'validator key')
  .setAction(async (taskArguments, hre) => {
    let { validId: validatorId = '', ownerId = '', spenderId = '', amount = '', validKey = '' } = { ...taskArguments }
    ownerId = convertToHex({ hre, value: ownerId })
    spenderId = convertToHex({ hre, value: spenderId })
    const validatorIdHex = convertToHex({ hre, value: validatorId })
    const validatorKey = validKey
    amount = Number(amount)

    const orderN = '0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141'
    const accountPrivateKey = '0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a'
    const issuerPrivateKey = '0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6'
    const commitPrivateKey = '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
    const accDtn = new BigNumber('999999999999999999')

    const sigInfo = await utils.siginfoGenerator(commitPrivateKey, accountPrivateKey, orderN, issuerPrivateKey, accDtn)
    const msgSalt = utils.toBytes32('approve')
    const accSigInfo = PrivateKey.sig(
      sigInfo.signer,
      ['bytes32', 'bytes32', 'uint256', 'bytes32'],
      [ownerId, spenderId, amount, msgSalt],
    )

    console.log(`** checkApprove Parameters **\n`)
    const params = {
      validatorId: validatorIdHex,
      ownerId: ownerId,
      spenderId: spenderId,
      amount: amount,
      validKey: validatorKey,
      sigInfo: sigInfo,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'Token' })

      const deadline = await Tools.getTime()
      const sig = await PrivateKey.sig(
        validatorKey,
        ['bytes32', 'bytes32', 'uint256'],
        [validatorIdHex, ownerId, deadline],
      )

      const receipt = await contract.checkApprove(
        validatorIdHex,
        ownerId,
        spenderId,
        amount,
        accSigInfo[0],
        sigInfo.info,
        deadline,
        sig[0],
      )

      const formattedReceipt = {
        result: receipt.success ? 'ok' : 'failed',
        reason: receipt.err,
      }

      console.log(`** checkApprove receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
