import PrivateKey from '@/privateKey'
import * as utils from '@test/common/utils'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as Tools from './common/tools'

wrappedTask('checkTransaction', 'Check if an account can transfer.', { filePath: path.basename(__filename) })
  .addParam('validId', 'validator id')
  .addParam('zoneId', 'zone id')
  .addParam('sendAccountId', 'send account id')
  .addParam('fromAccountId', 'from account id')
  .addParam('toAccountId', 'to account id')
  .addParam('amount', 'amount')
  .setAction(async (taskArguments, hre) => {
    let {
      validId = '',
      zoneId = '',
      sendAccountId = '',
      fromAccountId = '',
      toAccountId = '',
      amount = '',
    } = { ...taskArguments }
    sendAccountId = convertToHex({ hre, value: sendAccountId })
    fromAccountId = convertToHex({ hre, value: fromAccountId })
    toAccountId = convertToHex({ hre, value: toAccountId })
    zoneId = Number(zoneId)

    validId = convertToHex({ hre, value: validId })
    amount = Number(amount)

    const misc1 = convertToHex({ hre, value: '' })
    const misc2 = ''

    const orderN = '0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141'
    const accountPrivateKey = '0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a'
    const issuerPrivateKey = '0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6'
    const commitPrivateKey = '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
    const deadline = await Tools.getTime()

    const sigInfo = await utils.siginfoGenerator(
      commitPrivateKey,
      accountPrivateKey,
      orderN,
      issuerPrivateKey,
      deadline,
    )

    const msgSalt = utils.toBytes32('transfer')
    const accSigInfo = PrivateKey.sig(
      sigInfo.signer,
      ['bytes32', 'bytes32', 'bytes32', 'uint256', 'bytes32'],
      [sendAccountId, fromAccountId, toAccountId, amount, msgSalt],
    )

    const validSigInfo = PrivateKey.sig(
      issuerPrivateKey.slice(2),
      ['bytes32', 'uint16', 'bytes32', 'bytes32', 'bytes32', 'uint256', 'uint256'],
      [validId, zoneId, sendAccountId, fromAccountId, toAccountId, amount, deadline],
    )

    console.log(`** checkTransaction Parameters **\n`)
    const params = {
      validId: validId,
      zoneId: zoneId,
      sendAccountId: sendAccountId,
      fromAccountId: fromAccountId,
      toAccountId: toAccountId,
      amount: amount,
      miscValue1: misc1,
      miscValue2: misc2,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'FinancialCheck' })

      const receipt = await contract.checkTransaction(
        zoneId,
        sendAccountId,
        fromAccountId,
        toAccountId,
        amount,
        misc1,
        misc2,
        accSigInfo[0],
        sigInfo.info,
      )

      const formattedReceipt = {
        result: receipt.success ? 'ok' : 'failed',
        reason: receipt.err,
      }
      console.log(`** checkTransaction receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
