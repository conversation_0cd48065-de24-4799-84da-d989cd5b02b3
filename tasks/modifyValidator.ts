import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('modifyValidator', 'modify validator', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .addParam('validName', 'validator name')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })
    let { validId = '', validName = '' } = { ...taskArguments }
    validName = convertToHex({ hre, value: validName })
    validId = convertToHex({ hre, value: validId })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })
    contract.connect(kmsSigner)

    console.log(`*** validName更新: ${validName.toString(16)}`)
    const deadline = await Tools.getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'bytes32', 'uint256'], [validId, validName, deadline])
    const traceId = convertToHex({ hre, value: 'trace1' })

    const receipt = await contract.modValidator(validId, validName, traceId, deadline, kmsSig)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
