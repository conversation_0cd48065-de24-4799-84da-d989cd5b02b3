import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { kmsSignerProvider } from './common/kmsSignerProvider'
import * as Tools from './common/tools'

wrappedTask('modValidator', 'mod Validator', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator id')
  .addParam('name', 'name')
  .addParam('validatorKey', 'validator Key')
  .setAction(async (taskArguments, hre) => {
    try {
      const kmsSigner = kmsSignerProvider({ hre })

      let { validatorId = '', name = '', validatorKey = '' } = { ...taskArguments }
      validatorId = convertToHex({ hre, value: validatorId })
      name = convertToHex({ hre, value: name })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** modValidator Parameters **\n`)
      const params = {
        validatorId,
        name,
      }
      Tools.printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      const deadline = await Tools.getTime()
      const kmsSig = await kmsSigner.sign(['bytes32', 'bytes32', 'uint256'], [validatorId, name, deadline])

      const receipt = await contract.modValidator(validatorId, name, traceId, deadline, kmsSig)
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error: any) {
      Tools.showErrorDetails({ error })
    }
  })
