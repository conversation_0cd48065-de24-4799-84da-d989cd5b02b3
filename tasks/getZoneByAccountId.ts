import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getZoneByAccountId', 'get Zone By AccountId', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .addParam('accountId', 'account Id')
  .setAction(async (taskArguments, hre) => {
    try {
      let { validatorId = '', accountId = '' } = { ...taskArguments }
      validatorId = convertToHex({ hre, value: validatorId })
      accountId = convertToHex({ hre, value: accountId })

      console.log(`** getZoneByAccountId Parameters **\n`)
      const params = {
        validatorId,
        accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

      const result = await contract.getZoneByAccountId(validatorId, accountId)

      const zoneInfo = {
        zones: result.zones,
        error: result.err,
      }

      console.log(`** Zone Information **\n`)
      printTable({ data: zoneInfo })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
