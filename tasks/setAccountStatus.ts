import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('setAccountStatus', 'set account status', {
  filePath: path.basename(__filename),
})
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('accountStatus', 'account status')
  .addParam('reasonCode', 'reason code')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    let { issuerId = '', accountId = '', accountStatus = '', reasonCode = '', issuerKey = '' } = { ...taskArguments }
    issuerId = convertToHex({ hre, value: issuerId })
    accountId = convertToHex({ hre, value: accountId })
    accountStatus = convertToHex({ hre, value: accountStatus })
    reasonCode = convertToHex({ hre, value: reasonCode })

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

    console.log(`*** Set Account Status`)
    const deadline = await Tools.getTime()
    const sig = await PrivateKey.sig(
      issuerKey,
      ['bytes32', 'bytes32', 'bytes32', 'uint256'],
      [issuerId, accountId, reasonCode, deadline],
    )

    const receipt = await contract.setAccountStatus(
      issuerId,
      accountId,
      accountStatus,
      reasonCode,
      traceId,
      deadline,
      sig[0],
    )
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
