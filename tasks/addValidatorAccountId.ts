import { getContractWithSigner } from '@/tasks/common/getContractWithSigner'
import { convertToHex } from '@tasks/common/convertToHex'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('addValidatorAccountId', 'add validator accountId', { filePath: path.basename(__filename) })
  .addParam('validId', 'validetor id')
  .addParam('accountId', 'account id')
  .addParam('traceId', 'trace id')
  .setAction(async (taskArguments, hre) => {
    let { validId = '', accountId = '', traceId = '' } = { ...taskArguments }
    const validatorId = convertToHex({ hre, value: validId })
    accountId = convertToHex({ hre, value: accountId })
    traceId = convertToHex({ hre, value: traceId })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

    console.log(`*** バリデータが直接管理するアカウントID追加: ${accountId}`)
    const receipt = await contract.addValidatorAccountId(validatorId, accountId, traceId)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
