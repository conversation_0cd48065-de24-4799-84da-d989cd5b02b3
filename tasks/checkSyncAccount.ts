import PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as utils from '@test/common/utils'
import BigNumber from 'bignumber.js'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('checkSyncAccount', 'Check if an account can synchronize.', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .addParam('validId', 'validator id')
  .addParam('zoneId', 'zone id')
  .addParam('accountStatus', 'account status')
  .setAction(async (taskArguments, hre) => {
    let { accountId = '', validId = '', zoneId = '', accountStatus = '' } = { ...taskArguments }
    accountId = convertToHex({ hre, value: accountId })
    const validatorId = convertToHex({ hre, value: validId })
    zoneId = Number(zoneId)
    accountStatus = convertToHex({ hre, value: accountStatus })

    const orderN = '0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141'
    const accountPrivateKey = '0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a'
    const issuerPrivateKey = '0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6'
    const commitPrivateKey = '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
    const accDtn = new BigNumber('999999999999999999')

    const sigInfo = await utils.siginfoGenerator(commitPrivateKey, accountPrivateKey, orderN, issuerPrivateKey, accDtn)
    const msgSalt = utils.toBytes32('synchronous')
    const accSigInfo = PrivateKey.sig(sigInfo.signer, ['bytes32', 'bytes32'], [accountId, msgSalt])

    console.log(`** checkSyncAccount Parameters **\n`)
    const params = {
      accountId: accountId,
      validatorId,
      zoneId: zoneId,
      accountStatus: accountStatus,
      sigInfo: sigInfo,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'FinancialCheck' })

      const receipt = await contract.checkSyncAccount(
        validatorId,
        accountId,
        zoneId,
        accountStatus,
        accSigInfo[0],
        sigInfo.info,
      )

      const formattedReceipt = {
        result: receipt.success ? 'ok' : 'failed',
        reason: receipt.err,
      }
      printTable({ data: formattedReceipt })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
