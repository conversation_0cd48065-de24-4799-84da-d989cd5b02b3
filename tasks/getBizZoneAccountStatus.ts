import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getBizZoneAccountStatus', 'Get Biz Zone Account.', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'account id')
  .addParam('zoneId', 'zone id')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    let { accountId = '', zoneId = '' } = { ...taskArguments }
    accountId = convertToHex({ hre, value: accountId })
    zoneId = Number(zoneId)

    console.log(`** getBizZoneAccountStatus Parameters **\n`)
    const params = {
      accountId: accountId,
      zoneId: zoneId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner({ hre, contractName: 'FinancialCheck' })

      const receipt = await contract.getBizZoneAccountStatus(accountId, zoneId)

      printTable({ data: { accountStatus: ethers.toUtf8String(receipt.accountStatus.toString()) } })
    } catch (error: any) {
      showErrorDetails({ error })
    }
  })
