import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('registerBizZone', 'register biz zone', {
  filePath: path.basename(__filename),
})
  .addParam('zoneId', 'zone id')
  .addParam('zoneName', 'zone name')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

    let { zoneId = '', zoneName = '' } = { ...taskArguments }

    console.log(`*** biz zone登録: ${zoneId}`)
    const deadline = await Tools.getTime()
    const kmsSig = await kmsSigner.sign(['uint16', 'string', 'uint256'], [zoneId, zoneName, deadline])
    console.log('zoneId', zoneId)
    console.log('zoneName', zoneName)
    const receipt = await contract.addBizZone(zoneId, zoneName, deadline, kmsSig)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
