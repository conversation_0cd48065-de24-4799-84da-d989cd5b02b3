import { wrappedTask } from '@/tasks/common/taskMiddleware'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import path from 'path'
import web3 from 'web3'
import * as Tools from './common/tools'

wrappedTask('transferSingle', 'Execute transfer within the same zone.', {
  filePath: path.basename(__filename),
})
  .addParam('sendAccountId', 'send account id')
  .addParam('fromAccountId', 'from account id')
  .addParam('toAccountId', 'to account id')
  .addParam('amount', 'amount')
  .addParam('miscValue1', 'miscValue1')
  .addParam('miscValue2', 'miscValue2')
  .addParam('memo', 'memo')
  .addParam('traceId', 'traceId')
  .setAction(async (taskArguments, hre) => {
    let {
      sendAccountId = '',
      fromAccountId = '',
      toAccountId = '',
      amount = '',
      miscValue1 = '',
      miscValue2 = '',
      memo = '',
      traceId = '',
    } = { ...taskArguments }

    sendAccountId = convertToHex({ hre, value: sendAccountId })
    fromAccountId = convertToHex({ hre, value: fromAccountId })
    toAccountId = convertToHex({ hre, value: toAccountId })
    miscValue1 = convertToHex({ hre, value: miscValue1 })
    miscValue2 = convertToHex({ hre, value: miscValue2 })
    memo = convertToHex({ hre, value: memo })
    traceId = convertToHex({ hre, value: traceId })

    const toUint256HexPadded = (x) => web3.utils.padLeft(web3.utils.toHex(x), 64)

    const { contract } = await getContractWithSigner({ hre, contractName: 'Token' })
    const result = await contract.transferSingle(
      sendAccountId,
      fromAccountId,
      toAccountId,
      amount,
      miscValue1,
      miscValue2,
      memo,
      traceId,
    )
    await result
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
