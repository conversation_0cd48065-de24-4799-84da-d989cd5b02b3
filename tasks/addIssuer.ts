import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('addIssuer', 'Add issuer', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'Issuer ID')
  .addParam('bankCode', 'Bank code')
  .addParam('issuerName', 'Issuer name')
  .setAction(async (taskArguments, hre) => {
    try {
      let { issuerId = '', bankCode = '', issuerName = '' } = { ...taskArguments }
      issuerId = convertToHex({ hre, value: issuerId })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** addIssuer Parameters **\n`)
      const params = {
        issuerId,
        bankCode,
        issuerName,
      }
      Tools.printTable({ data: params })

      const kmsSigner = kmsSignerProvider({ hre })
      const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

      const deadline = await Tools.getTime()
      const kmsSig = await kmsSigner.sign(
        ['bytes32', 'uint16', 'string', 'uint256'],
        [issuerId, bankCode, issuerName, deadline],
      )

      const receipt = await contract.addIssuer(issuerId, bankCode, issuerName, traceId, deadline, kmsSig)

      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error: any) {
      Tools.showErrorDetails({ error })
    }
  })
