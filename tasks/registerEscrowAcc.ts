import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('registerEscrowAcc', 'register escrow account', {
  filePath: path.basename(__filename),
})
  .addParam('srcZoneId', 'src zone id')
  .addParam('dstZoneId', 'dst zone id')
  .addParam('escrowAccount', 'escrow account')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    let { srcZoneId = '', dstZoneId = '', escrowAccount = '' } = { ...taskArguments }

    srcZoneId = Number(srcZoneId)
    dstZoneId = Number(dstZoneId)
    //ethers.utils.formatBytes32String()が32文字までしか渡せないためhexlifyとtoUtf8Bytesを使用して変換している
    escrowAccount = convertToHex({ hre, value: escrowAccount })

    console.log(`SRC_ZONE_ID= ${srcZoneId}`)
    console.log(`DST_ZONE_ID= ${dstZoneId}`)
    console.log(`ESCROW_ACCOUNT= ${escrowAccount}`)

    const { contract } = await getContractWithSigner({ hre, contractName: 'JPYTokenTransferBridge' })

    console.log(`*** Escrow Account登録: ESCROW_ACCOUNT=${escrowAccount}`)
    const deadline = (await Tools.getTime()) + 10
    const kmsSig = await kmsSigner.sign(
      ['uint16', 'uint16', 'bytes32', 'uint256'],
      [srcZoneId, dstZoneId, escrowAccount, deadline],
    )

    const receipt = await contract.registerEscrowAccount(srcZoneId, dstZoneId, escrowAccount, deadline, kmsSig)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
