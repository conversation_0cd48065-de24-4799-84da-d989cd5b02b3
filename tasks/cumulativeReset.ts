import * as PrivateKey from '@/privateKey'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('cumulativeReset', 'reset daily cumulative amount', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    let { issuerId = '', accountId = '', issuerKey = '' } = { ...taskArguments }
    issuerId = convertToHex({ hre, value: issuerId })
    accountId = convertToHex({ hre, value: accountId })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'Issuer' })

    console.log(`*** Issuer cumulative reset: ${accountId}`)
    const deadline = await Tools.getTime()
    const sig = await PrivateKey.sig(issuerKey, ['bytes32', 'bytes32', 'uint256'], [issuerId, accountId, deadline])

    const receipt = await contract.cumulativeReset(issuerId, accountId, traceId, deadline, sig[0])
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
