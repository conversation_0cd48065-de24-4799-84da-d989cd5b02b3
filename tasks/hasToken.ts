import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('hasToken', 'has Token', { filePath: path.basename(__filename) })
  .addParam('tokenId', 'token Id')
  .addParam('providerId', 'provider Id')
  .addParam('chkEnabled', 'chk Enabled')
  .setAction(async (taskArguments, hre) => {
    try {
      let { tokenId = '', providerId = '', chkEnabled = '' } = { ...taskArguments }
      tokenId = convertToHex({ hre, value: tokenId })
      providerId = convertToHex({ hre, value: providerId })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** hasToken Parameters **\n`)
      const params = {
        tokenId,
        providerId,
        chkEnabled,
      }
      Tools.printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'Token' })

      const receipt = await contract.hasToken(tokenId, chkEnabled)

      console.log(`** hasToken Receipt Information **\n`)
      Tools.printTable({ data: receipt })
    } catch (error: any) {
      Tools.showErrorDetails({ error })
    }
  })
