import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getZone', 'get Zone', { filePath: path.basename(__filename) }).setAction(async (taskArguments, hre) => {
  try {
    console.log(`** getZone Parameters **\n`)
    console.log('No parameters required')

    const { contract } = await getContractWithSigner({ hre, contractName: 'Provider' })

    const result = await contract.getZone()

    const zoneInfo = {
      zoneId: result.zoneId,
      zoneName: result.zoneName,
      error: result.err,
    }

    console.log(`** Zone Information **\n`)
    printTable({ data: zoneInfo })
  } catch (error: any) {
    showErrorDetails({ error })
  }
})
