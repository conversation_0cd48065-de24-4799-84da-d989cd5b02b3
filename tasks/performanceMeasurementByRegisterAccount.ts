import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import fs from 'fs'
import path from 'path'
import * as Tools from './common/tools'

wrappedTask('performanceMeasurementByRegisterAccount', 'performance measurement by register account', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'id')
  .addParam('validId', 'validator id')
  .addParam('count', 'validator count')
  .setAction(async (taskArguments, hre) => {
    const { accountId = '', validId = '', count = '' } = { ...taskArguments }

    const validatorId = convertToHex({ hre, value: validId })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const result: any = []
    const txText: any = []

    const startTime = performance.now()

    const { contract } = await getContractWithSigner({ hre, contractName: 'Validator' })

    const txs = [...Array(count).keys()].map(async (num) => {
      const currentAccountId = convertToHex({ hre, value: accountId + num })
      const currentAccountName = convertToHex({ hre, value: accountId + num })

      performance.mark('start' + num)
      const receipt = await contract.addAccount(
        validatorId,
        currentAccountId,
        currentAccountName,
        [0, 0, 0, 0, 0],
        traceId,
      )
      await receipt.wait().then((res) => {
        performance.mark('end' + num)
        Tools.showEthersRes({ res })
        performance.measure(num.toString(), 'start' + num, 'end' + num)
        console.log(performance.getEntriesByName(num.toString()))
        result.push(performance.getEntriesByName(num.toString())[0])
        txText.push(res)
      })
    })
    await Promise.all(txs)
      .then(() => console.log('promise-complete'))
      .catch((err) => console.log(err))
    const endTime = performance.now()
    console.log(`result: ${result}`)
    console.log(`json file: ${JSON.stringify(result)}`)
    await fs.writeFileSync('data-account-' + accountId + '.json', JSON.stringify(result))
    await fs.writeFileSync('txs-account-' + accountId + '.json', JSON.stringify(txText))
    console.log(endTime - startTime)
  })
