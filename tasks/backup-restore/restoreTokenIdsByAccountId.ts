import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as Tools from '@tasks/common/tools'
import path from 'path'

const fs = require('fs')

wrappedTask('restoreTokenIdsByAccountId', 'restore all account data', {
  filePath: path.basename(__filename),
}).setAction(async (taskArguments, hre) => {
  const { network } = hre

  let tokensByAccountId: Array<any> = []

  const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${network.name}/tokensbyaccount.json`)
  const isValid = md5Valid({ obj: data, item: 'tokensbyaccount', network: network.name })
  tokensByAccountId = JSON.parse(data)

  if (!isValid) {
    throw new Error('The data is invalid or empty data.')
  }

  const { contract } = await getContractWithSigner({
    hre,
    contractName: 'RenewableEnergyToken',
  })

  console.log(`*** restore accounts data...`)
  console.log(`*** restore renewable energy token data...`)
  const sigPrams = await getBackupSignature({ hre, salt: 'setRETokensAll' })
  const limit = 1000

  while (tokensByAccountId.length > limit) {
    const receipt = await contract.restoreTokenIdsByAccountId(
      tokensByAccountId.slice(0, limit),
      sigPrams.deadline,
      sigPrams.sig,
    )
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
    tokensByAccountId = tokensByAccountId.slice(limit)
  }

  if (tokensByAccountId.length > 0) {
    const receipt = await contract.restoreTokenIdsByAccountId(tokensByAccountId, sigPrams.deadline, sigPrams.sig)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  }
})
