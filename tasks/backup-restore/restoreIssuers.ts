import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as Tools from '@tasks/common/tools'
import path from 'path'

const fs = require('fs')

wrappedTask('restoreIssuers', 'restore all issuer data', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    const { network, getNamedAccounts, ethers } = hre

    let issuers: Array<any> = []

    const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${network.name}/issuers.json`)
    const isValid = md5Valid({ obj: data, item: 'issuers', network: network.name })

    if (!isValid) {
      throw new Error('The data is invalid backup data.')
    }

    issuers = JSON.parse(data)

    const { contract: issuerContract } = await getContractWithSigner({
      hre,
      contractName: 'Issuer',
    })
    const { contract: remigrationContract } = await getContractWithSigner({
      hre,
      contractName: 'RemigrationRestore',
    })

    console.log(`*** restore issuers data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'setIssuersAll' })
    const limit = 100

    while (issuers.length > limit) {
      const receipt = await remigrationContract.restoreIssuers(issuers.slice(0, limit), sigPrams.deadline, sigPrams.sig)
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
      issuers = issuers.slice(limit)
    }

    if (issuers.length > 0) {
      const receipt = await remigrationContract.restoreIssuers(issuers, sigPrams.deadline, sigPrams.sig)
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
  },
)
