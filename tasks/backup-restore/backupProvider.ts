import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('backupProviders', 'backup all provider data', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    const { network } = hre

    const { contract } = await getContractWithSigner({
      hre,
      contractName: 'RemigrationBackup',
    })

    console.log(`*** backup provider data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getProviderAll' })

    const provider = await contract.backupProviders(sigPrams.deadline, sigPrams.sig)
    if (provider.err != '') {
      console.log(provider.err)
    } else {
      saveBackupToJson({ data: provider.providers, fileName: 'provider', networkName: network.name })
    }
  },
)
