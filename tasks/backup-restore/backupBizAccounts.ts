import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('backupBizAccounts', 'backup all business zone account data', {
  filePath: path.basename(__filename),
}).setAction(async (taskArguments, hre) => {
  const { network } = hre

  const { contract: accountContract } = await getContractWithSigner({ hre, contractName: 'Account' })
  const { contract: remigrationContract } = await getContractWithSigner({
    hre,
    contractName: 'RemigrationBackup',
  })

  console.log(`*** backup business zone accounts data...`)
  const sigPrams = await getBackupSignature({ hre, salt: 'getBizAccountsAll' })

  const bizAccounts: Array<any> = []
  const bizAccountsExist: Array<any> = []
  let offset = 0
  const limit = 100

  const totalCount = await accountContract.getAccountCount()

  while (bizAccounts.length != totalCount) {
    if (bizAccounts.length > totalCount) {
      console.error(`Error: Accounts count ${bizAccounts.length} is greater than total count`)
      break
    }
    const [result, count, err] = await remigrationContract.backupBusinessZoneAccounts(
      offset,
      limit,
      sigPrams.deadline,
      sigPrams.sig,
    )
    if (err != '') {
      console.log(`backup ${bizAccounts.length + 1} ~ ${bizAccounts.length + result.length} failed`)
      console.log('Error:', err)
      break
    } else {
      console.log(`backup ${bizAccounts.length + 1} ~ ${bizAccounts.length + result.length} items to local`)
      for (const key of Object.keys(result)) {
        if (result[key].bizAccountsByZoneId.length != 0) {
          bizAccountsExist.push(result[key])
        }
        bizAccounts.push(result[key])
      }
    }
    offset++
  }

  saveBackupToJson({ data: bizAccountsExist, fileName: 'bizaccounts', networkName: network.name })
})
