import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as Tools from '@tasks/common/tools'
import path from 'path'

const fs = require('fs')

wrappedTask('restoreBizAccounts', 'restore all business zone account data', {
  filePath: path.basename(__filename),
}).setAction(async (taskArguments, hre) => {
  const { network } = hre

  let bizAccounts: Array<any> = []

  const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${network.name}/bizaccounts.json`)
  const isValid = md5Valid({ obj: data, item: 'bizaccounts', network: network.name })

  bizAccounts = JSON.parse(data)

  if (!isValid) {
    throw new Error('The data is invalid or empty.')
  }

  console.log('*************************************')
  console.log(`* Execute function on ${network.name} network *`)
  console.log('*************************************')

  const { contract } = await getContractWithSigner({
    hre,
    contractName: 'RemigrationRestore',
  })

  console.log(`*** restore business zone accounts data...`)

  const sigPrams = await getBackupSignature({ hre, salt: 'setBizAccountsAll' })
  const limit = 1000

  while (bizAccounts.length > limit) {
    const receipt = await contract.restoreBusinessZoneAccounts(
      bizAccounts.slice(0, limit),
      sigPrams.deadline,
      sigPrams.sig,
    )
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
    bizAccounts = bizAccounts.slice(limit)
  }

  if (bizAccounts.length > 0) {
    const receipt = await contract.restoreBusinessZoneAccounts(bizAccounts, sigPrams.deadline, sigPrams.sig)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  }
})
