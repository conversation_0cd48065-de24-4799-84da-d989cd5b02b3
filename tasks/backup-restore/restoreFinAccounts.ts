import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as Tools from '@tasks/common/tools'
import path from 'path'

const fs = require('fs')

wrappedTask('restoreFinAccounts', 'restore all financial zone account data', {
  filePath: path.basename(__filename),
}).setAction(async (taskArguments, hre) => {
  const { network } = hre

  let finAccounts: Array<any> = []

  const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${network.name}/finaccounts.json`)
  const isValid = md5Valid({ obj: data, item: 'finaccounts', network: network.name })

  if (!isValid) {
    throw new Error('The data is invalid backup data.')
  }

  finAccounts = JSON.parse(data)

  const { contract } = await getContractWithSigner({
    hre,
    contractName: 'RemigrationRestore',
  })

  console.log(`*** restore financial zone accounts data...`)

  const sigPrams = await getBackupSignature({ hre, salt: 'setFinAccountsAll' })
  const limit = 1000

  while (finAccounts.length > limit) {
    const receipt = await contract.restoreFinancialZoneAccounts(
      finAccounts.slice(0, limit),
      sigPrams.deadline,
      sigPrams.sig,
    )
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
    finAccounts = finAccounts.slice(limit)
  }

  if (finAccounts.length > 0) {
    const receipt = await contract.restoreFinancialZoneAccounts(finAccounts, sigPrams.deadline, sigPrams.sig)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  }
})
