import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('backupValidators', 'backup all validator data', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    const { network, getNamedAccounts, ethers } = hre

    const { contract: validatorContract } = await getContractWithSigner({
      hre,
      contractName: 'Validator',
    })
    const { contract: remigrationContract } = await getContractWithSigner({
      hre,
      contractName: 'RemigrationBackup',
    })

    console.log(`*** backup validators data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getValidatorsAll' })

    const validators: Array<any> = []
    let offset = 0
    const limit = 100

    const totalCount = await validatorContract.getValidatorCount()

    while (validators.length != totalCount) {
      if (validators.length > totalCount) {
        console.error(`Error: Validators count ${validators.length} is greater than total count`)
        break
      }
      const [validatorsTemp, count, err] = await remigrationContract.backupValidators(
        offset,
        limit,
        sigPrams.deadline,
        sigPrams.sig,
      )
      if (err != '') {
        console.log(`backup ${validators.length + 1} ~ ${validators.length + validatorsTemp.length} failed`)
        console.log('Error:', err)
        break
      } else {
        console.log(`backup ${validators.length + 1} ~ ${validators.length + validatorsTemp.length} items to local`)
        validators.push(...validatorsTemp)
      }
      offset++
    }

    saveBackupToJson({ data: validators, fileName: 'validators', networkName: network.name })
  },
)
