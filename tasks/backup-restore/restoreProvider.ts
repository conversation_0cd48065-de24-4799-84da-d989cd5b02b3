import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as Tools from '@tasks/common/tools'
import path from 'path'

const fs = require('fs')

wrappedTask('restoreProviders', 'restore all provider data', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    const { network } = hre

    let provider: Array<any> = []

    const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${network.name}/provider.json`)
    const isValid = md5Valid({ obj: data, item: 'provider', network: network.name })

    if (!isValid) {
      throw new Error('The data is invalid backup data.')
    }

    provider = JSON.parse(data)

    const { contract } = await getContractWithSigner({
      hre,
      contractName: 'RemigrationRestore',
    })

    console.log(`*** restore provider data...`)

    const setProviderSig = await getBackupSignature({ hre, salt: 'setProviderAll' })

    const receipt = await contract.restoreProviders(provider, setProviderSig.deadline, setProviderSig.sig)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  },
)
