import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as Tools from '@tasks/common/tools'

import path from 'path'
const fs = require('fs')

wrappedTask('restoreAccounts', 'restore all account data', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    const { network, getNamedAccounts, ethers } = hre

    let accounts: Array<any> = []

    const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${network.name}/accounts.json`)
    const isValid = md5Valid({ obj: data, item: 'accounts', network: network.name })

    if (!isValid) {
      throw new Error('The data is invalid backup data.')
    }

    accounts = JSON.parse(data)

    const { contract: remigrationContract } = await getContractWithSigner({
      hre,
      contractName: 'RemigrationRestore',
    })
    const { contract: accountContract } = await getContractWithSigner({
      hre,
      contractName: 'Account',
    })

    console.log(`*** restore accounts data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'setAccountsAll' })
    const limit = 1000

    while (accounts.length > limit) {
      const receipt = await remigrationContract.restoreAccounts(
        accounts.slice(0, limit),
        sigPrams.deadline,
        sigPrams.sig,
      )
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
      accounts = accounts.slice(limit)
    }

    if (accounts.length > 0) {
      const receipt = await remigrationContract.restoreAccounts(accounts, sigPrams.deadline, sigPrams.sig)
      await receipt
        .wait()
        .then((res) => {
          Tools.showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
  },
)
