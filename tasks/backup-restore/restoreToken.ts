import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as Tools from '@tasks/common/tools'
import path from 'path'

const fs = require('fs')

wrappedTask('restoreToken', 'restore all token data', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    const { network } = hre

    const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${network.name}/token.json`)
    const isValid = md5Valid({ obj: data, item: 'token', network: network.name })
    const token = JSON.parse(data)

    if (!isValid) {
      throw new Error('The data is invalid or empty data.')
    }

    const { contract } = await getContractWithSigner({
      hre,
      contractName: 'RemigrationRestore',
    })

    console.log(`*** restore token data...`)

    const sigPrams = await getBackupSignature({ hre, salt: 'setTokenAll' })
    const receipt = await contract.restoreToken(token, sigPrams.deadline, sigPrams.sig)
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  },
)
