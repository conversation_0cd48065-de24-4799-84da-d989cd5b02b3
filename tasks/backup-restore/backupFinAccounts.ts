import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('backupFinAccounts', 'backup all fin zone account data', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    const { network } = hre

    const { contract: accountContract } = await getContractWithSigner({ hre, contractName: 'Account' })
    const { contract: remigrationContract } = await getContractWithSigner({
      hre,
      contractName: 'RemigrationBackup',
    })

    console.log(`*** backup financial zone accounts data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getFinAccountsAll' })

    const finAccounts: Array<any> = []
    let offset = 0
    const limit = 1000

    const totalCount = await accountContract.getAccountCount()
    // console.log(`Total item: ${totalCount.toString()}`);

    while (finAccounts.length != totalCount) {
      if (finAccounts.length > totalCount) {
        console.error(`Error: Accounts count ${finAccounts.length} is greater than total count`)
        break
      }
      const [result, count, err] = await remigrationContract.backupFinancialZoneAccounts(
        offset,
        limit,
        sigPrams.deadline,
        sigPrams.sig,
      )
      if (err != '') {
        console.log(`backup ${finAccounts.length + 1} ~ ${finAccounts.length + result.length} failed`)
        console.log('Error:', err)
        break
      } else {
        console.log(`backup ${finAccounts.length + 1} ~ ${finAccounts.length + result.length} items to local`)
        for (const key of Object.keys(result)) {
          const finAccount: any = []
          finAccount.push(result[key].accountId)
          const accountLimits = result[key].financialZoneAccountData.map((value) => value)
          finAccount.push(accountLimits)
          finAccounts.push(finAccount)
        }
      }
      offset++
    }

    // console.log(`All ${totalCount} (equle to the total count) items have been successfully backed up.`);

    saveBackupToJson({ data: finAccounts, fileName: 'finaccounts', networkName: network.name })
  },
)
