import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('backupRETokens', 'backup all renewable token data', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    const { network } = hre

    const { contract } = await getContractWithSigner({
      hre,
      contractName: 'RenewableEnergyToken',
    })

    console.log(`*** backup renewable token data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getRETokensAll' })

    const tokens: Array<any> = []
    let offset = 0
    const limit = 1000

    const totalCount = await contract.getTokenCount()
    console.log(`Total item: ${totalCount.toString()}`)

    while (tokens.length != totalCount) {
      if (tokens.length > totalCount) {
        console.error(`Error: Accounts count ${tokens.length} is greater than total count`)
        break
      }

      const [result, count, err] = await contract.backupRenewableEnergyTokens(
        offset,
        limit,
        sigPrams.deadline,
        sigPrams.sig,
      )
      if (err != '') {
        console.log(`backup ${tokens.length + 1} ~ ${tokens.length + result.length} failed`)
        console.log('Error:', err)
        break
      } else {
        console.log(`backup ${tokens.length + 1} ~ ${tokens.length + result.length} items to local`)
        for (const key of Object.keys(result)) {
          const token = []
          for (const itemKey of Object.keys(result[key])) {
            const tokenItem = result[key][itemKey]
            if (typeof tokenItem === 'bigint') {
              token[itemKey] = tokenItem.toString()
            } else {
              token[itemKey] = tokenItem
            }
          }
          tokens.push(token)
        }
      }
      offset++
    }

    console.log(`All ${totalCount} (equle to the total count) items have been successfully backed up.`)

    saveBackupToJson({ data: tokens, fileName: 'retokens', networkName: network.name })
  },
)
