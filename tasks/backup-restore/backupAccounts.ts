import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('backupAccounts', 'backup all account data', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    const { network, getNamedAccounts, ethers } = hre

    const { contract: remigrationContract } = await getContractWithSigner({ hre, contractName: 'RemigrationBackup' })
    const { contract: accountContract } = await getContractWithSigner({ hre, contractName: 'Account' })

    console.log(`*** backup accounts data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getAccountsAll' })

    const accounts: Array<any> = []
    let offset = 0
    const limit = 1000

    const totalCount = await accountContract.getAccountCount()
    // console.log(`Total item: ${totalCount.toString()}`);

    while (accounts.length != totalCount) {
      if (accounts.length > totalCount) {
        console.error(`Error: Accounts count ${accounts.length} is greater than total count`)
        break
      }

      const [result, count, err] = await remigrationContract.backupAccounts(
        offset,
        limit,
        sigPrams.deadline,
        sigPrams.sig,
      )
      if (err != '') {
        console.log(`backup ${accounts.length + 1} ~ ${accounts.length + result.length} failed`)
        console.log('Error:', err)
        break
      } else {
        console.log(`backup ${accounts.length + 1} ~ ${accounts.length + result.length} items to local`)
        for (const key of Object.keys(result)) {
          const account = []
          for (const itemKey of Object.keys(result[key])) {
            const accountItem = result[key][itemKey]
            if (typeof accountItem === 'bigint') {
              account[itemKey] = accountItem.toString()
            } else {
              account[itemKey] = accountItem
            }
          }
          accounts.push(account)
        }
      }
      offset++
    }

    // console.log(`All ${totalCount} (equle to the total count) items have been successfully backed up.`);

    saveBackupToJson({ data: accounts, fileName: 'accounts', networkName: network.name })
  },
)
