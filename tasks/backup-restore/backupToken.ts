import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import path from 'path'

wrappedTask('backupToken', 'backup all token data', { filePath: path.basename(__filename) }).setAction(
  async (taskArguments, hre) => {
    const { network, getNamedAccounts, ethers } = hre

    const { contract: remigrationContract } = await getContractWithSigner({
      hre,
      contractName: 'RemigrationBackup',
    })

    console.log(`*** backup token data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getTokenAll' })

    const token = await remigrationContract.backupToken(sigPrams.deadline, sigPrams.sig)
    if (token.err != '') {
      console.log(token.err)
    } else {
      const tokenData = []
      for (const key of Object.keys(token.token)) {
        const item = token.token[key]
        if (typeof item === 'bigint') {
          tokenData[key] = item.toString()
        } else {
          tokenData[key] = item
        }
      }

      saveBackupToJson({ data: tokenData, fileName: 'token', networkName: network.name })
    }
  },
)
