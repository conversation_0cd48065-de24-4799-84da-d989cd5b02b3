# dcbg-dcjpy-contract

## Setup

### サブモジュールの取り込み

初回クローン時は、`git clone`に`--recurse-submodules`オプションを付けることでサブモジュールも一緒にクローンすることが可能。

```bash
git clone --recurse-submodules https://github.com/your/repository.git
```

リポジトリをクローンした後、またはサブモジュールに変更があった場合は以下のコマンドでサブモジュールの内容を取り込む必要がある。

```bash
git submodule update --init --recursive
```

### 依存パッケージのインストール

コントラクトに必要なpackageをインストールする。

```bash
npm i
```

## 本番環境へのデプロイ手順

[ローカルから各環境へのDCJPY-Conractのデプロイ手順](https://decurret.atlassian.net/wiki/spaces/DIG/pages/**********/DCJPY-Conract) を参照

## ローカル環境へのmigrate

詳細は[ローカルでのコントラクトデプロイ手順](https://decurret.atlassian.net/wiki/x/IQA1VQ)を参照

## テスト実行

[script/README.md](./scripts/README.md) を参照。

## .envについて

[sample.env](sample.env)を参照

* `NETWORK`: デプロイに使用するhardhat.config.tsのnetworks
* `PROVIDER`: デプロイするチェーンのRPC
* `NETWORK_ID`: デプロイするチェーンのネットワークID
* `KEY_ADMIN`: Admin権限の秘密鍵(先頭に`0x`をつけないこと)
* `IBCAPP_ADDR`: IBC/APPコントラクトアドレス(先頭に`0x`をつけること)(`setIBCApp.sh`でのみ使用)

## FinZone/BizZoneへのIBCコントラクトの登録手順

Zoneを跨いだやり取りを行うためにはIBCコントラクトアドレスをメインコントラクト側に登録する必要がある。
例えば`AccountSyncBridge`を登録する場合は下記を実行する。

```bash
./setIBCApp.sh [AccountSyncBridgeのデプロイアドレス] AccountSyncBridge [Adminキー情報]
```

Adminキー情報にはlocal環境の場合は`ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80`の固定値を利用し、本番環境の場合はデプロイ作業後に生成された`.env`に記載の`KEY_ADMIN`の値を利用する。

## カスタムコントラクト利用方法

下記のコマンドを順に実行する

前提：BizZoneのコントラクトデプロイが完了していること

```bash
npx hardhat setOracle --network [ローカル環境であればlocal, 本番環境であればmain]
npx hardhat registerInvoker --network [ローカル環境であればlocal, 本番環境であればmain]
npx hardhat manageRule --network [ローカル環境であればlocal, 本番環境であればmain] --rule add
```

## auto linter

huskyにより、コミット **後** に自動でlinterが起動します。  
lintの結果をコミットに取り入れたい場合は、以下のような操作でokです。

```bash
# 目的のコミット
git commit

# ここで自動的にlinterが起動

# linterによる変更を取り入れる
git add .
git commit --amend
```

## ツール

### ソースコード ドキュメント自動生成 `solidity-docgen`

#### コメントの編集と更新

開発者は、ソースコードの修正または機能の追加を行う際、対応するソースコード内のコメントも同時に更新すること。これには、各関数、変数の説明及びパラメータと戻り値の詳細が含まれる。

記述ルールは公式の[Natspecフォーマット](https://solidity-ja.readthedocs.io/ja/latest/natspec-format.html#natspec)を参照すること。

```solidity
/**
* @dev AccountID存在確認 本体(内部関数)。
*
* @param accountId accountId
* @return success true:未登録, false:登録済
* @return err エラーメッセージ
*/
```

#### ドキュメントの生成と更新

ソースコードの修正後、必ず以下のコマンドを用いてドキュメントを生成し、その結果をレビューすること。

生成されたドキュメントは、変更のコミット前に確認し、必要に応じて調整を行うこと。

```bash
./script/docs.sh
```

#### ドキュメントの配置

生成されたドキュメントファイルは`docs/`ディレクトリに配置される。このディレクトリ内のファイルはプロジェクトのドキュメントとして公開されるため、常に最新かつ正確な情報を含むよう維持すること。

### transactionReceipt取得

```bash
NETWORK=[main or local] tools/receipt.sh [0xで始まるtransactionHash]
```

### transactionReceiptからrevertReason取得

```bash
NETWORK=[main or local] tools/revertReason.sh [0xで始まるtransactionHash]
```

### transferガス計測

Confluence
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2101182593/Transfer

transferSingleの計測

```bash
./tools/transferSingleMesureGas.sh [送金数]
```

transferBatchの計測

```bash
./tools/transferBatchMesureGas.sh [送金数]
```

### その他

* build/contractsのコントラクトサイズ([参考](https://ethereum.org/ja/developers/tutorials/downsizing-contracts-to-fight-the-contract-size-limit/))

```bash
npx hardhat contract-size
```

## Caliperによるスループット計測

https://decurret.atlassian.net/wiki/spaces/DIG/pages/2126741700/Caliper

## ドキュメント

* [bin/](./bin/README.md)
* [tools/](./tools/README.md)
* [scripts/](./scripts/README.md)
* [API](./docs/interface/README.md)

### ※ husky による pre-commit でエラーが発生する場合は下記参照

https://decurret.atlassian.net/wiki/spaces/DIG/pages/2205352054/husky+pre-commit

## .vscode フォルダと共通設定の扱いについて

prettierの共通設定のために.vscodeのsettings.jsonをgitignoreから除外してる。
.vscodeには ***launch.json***, ***tasks.json*** などが含まれる可能性があるので、.vscodeフォルダ自体はgitignoreに含めている。

```gitignore
.vscode/
!.vscode/settings.json
```
