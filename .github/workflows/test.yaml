name: Unit Test

on:
  - pull_request

jobs:
  run_unit_test:
    name: Run unit test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout contract
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.ACCESS_TOKEN_FOR_GITOPS }}
          submodules: true
      - name: Set up node
        uses: actions/setup-node@v3
        with:
          node-version: '18.17.1'
      - uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-
      - name: Module install
        run: npm install
      - name: Grant execute permission for test script
        run: chmod +x ./scripts/test.sh
      - name: Run test script
        run: npx hardhat test
