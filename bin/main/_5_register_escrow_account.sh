#!/bin/bash

ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
SCRIPTNAME=$(basename "$0")
source $SCRIPTDIR/../common/utils.sh
source $SCRIPTDIR/_load_aws_prof.sh "$1"

# Load local AWS_PROFILE
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    source ${SCRIPTDIR}/env/."$AWS_PROFILE"
elif [ -f ${SCRIPTDIR}/env/."$PROJECT_ENV" ]; then
    source ${SCRIPTDIR}/env/."$PROJECT_ENV"
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

# Skip if ZONE_ID is 3001~3999
if [ "$ZONE_ID" -ge 3001 ] && [ "$ZONE_ID" -le 3999 ]; then
    message "info" "Skipping $SCRIPTNAME as ZONE_ID is 3001~3999."
    exit 0
fi

if [ $# -ne 2 ]; then
    message "err" "Please specify the NETWORK and NUMBER_OF_BIZZONE as the first argument."
    echo "./bin/main/$SCRIPTNAME [NETWORK name] [NUMBER_OF_BIZZONE]"
    exit 1
fi

SRC_ZONE_ID="3000"
ESCROW_ACCOUNT="61000e3ac0011d520cf4f8f8c33c4a71"

# Do REG_COMD as many times as the NUMBER_OF_BIZZONE. DST_ZONE_ID is incremented by 1 from 3001.
for i in $(seq 1 $2); do
    DST_ZONE_ID=$((3000 + i))

    message "info" "Registering escrow id to bridge for Zone:$DST_ZONE_ID"

    REG_COMD="npx hardhat registerEscrowAcc \
    --src-zone-id ${SRC_ZONE_ID} \
    --dst-zone-id ${DST_ZONE_ID} \
    --escrow-account ${ESCROW_ACCOUNT} \
    --network ${NETWORK}"
    $REG_COMD
    if [ $? -ne 0 ]; then
        message "err" "Failed to register the escrow account."
        exit 1
    fi
done

# Define the variables


message "success" "Successfully registered the escrow account for network: ${NETWORK} completed."