#!/bin/bash
# 
# description
# リリース後にabiファイルが正しく設定されているか確認するシェルです。
# _check_upload_abi_files.sh {環境名}
# 前提条件 
# awspが実施されていること
# 実行例 
# sh ./bin/main/_check_upload_abi_files.sh prod-fin-a

ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
SCRIPTNAME=$(basename "$0")
source $SCRIPTDIR/../common/utils.sh

source $SCRIPTDIR/_load_aws_prof.sh "$1"
# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    export $(cat ${SCRIPTDIR}/env/."$AWS_PROFILE" | grep -v "^#" | xargs)
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

if [ $ZONE_ID = "3000" ]; then
    # Finで確認するファイルのリスト
    FILES=("Account.json" "BusinessZoneAccount.json" "FinancialCheck.json" "FinancialZoneAccount.json" 
    "IBCToken.json" "Issuer.json" "Provider.json" "Token.json" "Validator.json")
else
    # Bizで確認するファイルのリスト
    FILES=("Account.json" "FinancialCheck.json" "IBCToken.json" "Issuer.json" "Provider.json" 
    "Token.json" "Validator.json")
fi
echo "Contract のリリース時のabiファイルが正しくアップロードされたか確認します。"

# 日付を取得します
TODAY=$(date +"%Y-%m-%d")

# 各ファイルをチェックします
for FILE_NAME in "${FILES[@]}"; do
    # 確認したいS3バケットファイルパスを設定します
    S3_FILE_PATH="s3://${BACKUP_S3}/${ZONE_ID}/${FILE_NAME}"

    # ファイルの存在を確認します
    if aws s3 ls "$S3_FILE_PATH" > /dev/null 2>&1; then
        # S3ファイルの詳細情報を取得します
        FILE_INFO=$(aws s3 ls "$S3_FILE_PATH" --recursive)

        # アップロード日付が本日か確認します
         UPLOAD_DATE=$(echo "$FILE_INFO" | awk '{print $1}')
        if [[ "$UPLOAD_DATE" == "$TODAY" ]]; then
            echo "OK：${FILE_NAME} のアップロード日付が本日であることを確認しました。"
        else
            echo "NG：${FILE_NAME} のアップロード日付が本日ではありません。"
        fi
    else
        echo "NG：${FILE_NAME} がアップロードされていません。"
    fi
done

echo "Contract 確認終了しました。"

# 確認するファイルのリスト
IBC_FILES=("AccountSyncBridge.json" "BalanceSyncBridge.json" "JPYTokenTransferBridge.json")
echo "Contract-Ibc のリリース時のabiファイルが正しくアップロードされたか確認します。"

# 日付を取得します
TODAY=$(date +"%Y-%m-%d")

# 各ファイルをチェックします
for FILE_NAME in "${IBC_FILES[@]}"; do
    # 確認したいS3バケットファイルパスを設定します
    S3_FILE_PATH="s3://${BACKUP_S3}/${ZONE_ID}/${FILE_NAME}"

    # ファイルの存在を確認します
    if aws s3 ls "$S3_FILE_PATH" > /dev/null 2>&1; then

        # S3ファイルの詳細情報を取得します
        FILE_INFO=$(aws s3 ls "$S3_FILE_PATH" --recursive)

        # アップロード日付が本日か確認します
        UPLOAD_DATE=$(echo "$FILE_INFO" | awk '{print $1}')
        if [[ "$UPLOAD_DATE" == "$TODAY" ]]; then
            echo "OK：${FILE_NAME} のアップロード日付が本日であることを確認しました。"
        else
            echo "NG：${FILE_NAME} のアップロード日付が本日ではありません。"
        fi
    else
        echo "NG：${FILE_NAME} がアップロードされていません。"
    fi
done

echo "Contract-Ibc 確認終了しました。"

echo "NGが出力されなければ問題ありません。"