#!/bin/bash

ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
SCRIPTNAME=$(basename "$0")
source $SCRIPTDIR/../common/utils.sh
source $SCRIPTDIR/_load_aws_prof.sh "$1"

# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    export $(cat ${SCRIPTDIR}/env/."$AWS_PROFILE" | grep -v "^#" | xargs)
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

if [ ! -f ${ROOTDIR}/.env_main_bk ]; then
    message "err" "File not found: dcbg-dcjpy-contract/.env"
    exit 1
fi

export $(cat ${ROOTDIR}/.env_main_bk | grep -v "^#" | xargs)

if [ $# -ne 2 ]; then
    message "err" "Please specify the NETWORK and BIZZONE_ID as the arguments."
    echo "./bin/main/$SCRIPTNAME [NETWORK name] [BIZONE_ID]"
    exit 1
fi

# check if the second argument is a number and 3001~3999
if ! [[ $2 =~ ^[0-9]+$ ]] || [ "$2" -lt 3001 ] || [ "$2" -gt 3999 ]; then
    message "err" "Please specify a valid BIZONE_ID between 3001 and 3999."
    exit 1
fi

SRC_ZONE_ID="3000"
DST_ZONE_ID=$2
ESCROW_ACCOUNT="61000e3ac0011d520cf4f8f8c33c4a71"

message "info" "Registering escrow id to bridge for Zone:$DST_ZONE_ID"

REG_COMD="npx hardhat registerEscrowAcc \
--src-zone-id ${SRC_ZONE_ID} \
--dst-zone-id ${DST_ZONE_ID} \
--escrow-account ${ESCROW_ACCOUNT} \
--network ${NETWORK}"
$REG_COMD
if [ $? -ne 0 ]; then
    message "err" "Failed to register the escrow account."
    exit 1
fi

message "success" "Successfully registered the escrow account for network: ${NETWORK} completed."