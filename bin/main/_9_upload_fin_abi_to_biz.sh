#!/bin/bash

ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
SCRIPTNAME=$(basename "$0")
source $SCRIPTDIR/../common/utils.sh

# Skip if NETWORK is local
if [[ "$NETWORK" == *"local"* ]]; then
    message "info" "Skipping $SCRIPTNAME for local environments."
    exit 0
fi

if [ $# -ne 1 ]; then
    message "err" "Please specify the NETWORK as the first argument."
    echo "./bin/main/$SCRIPTNAME [NETWORK name]"
    exit 1
fi

source $SCRIPTDIR/_load_aws_prof.sh "$1"
# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    export $(cat ${SCRIPTDIR}/env/."$AWS_PROFILE" | grep -v "^#" | xargs)
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

# Skip if ZONE_ID is 3000
if [ "$ZONE_ID" == "3000" ]; then
    message "info" "Skipping $SCRIPTNAME as ZONE_ID is 3000."
    exit 0
fi

# Ensure required environment variables are set
if [ -z "$BACKUP_S3_FIN" ] ||  [ -z "$AWS_PROFILE_FIN" ] ; then
    message "err" "Environment variables BACKUP_S3_FIN and AWS_PROFILE_FIN must be set."
    exit 1
fi

# Check if contract/3000 folder exists in the source S3 bucket
aws s3 ls "s3://$BACKUP_S3_FIN/3000/" --profile "$AWS_PROFILE_FIN" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    message "err" "The folder s3://$BACKUP_S3_FIN/3000/ does not exist. Please execute the FinZone deployment first."
    exit 1
fi

# Define the files to copy
fin_files=("Issuer.json" "FinancialCheck.json")

for f in "${fin_files[@]}"; do
    message "info" "Copying $f from $BACKUP_S3_FIN to $BACKUP_S3..."
    # Use AWS_PROFILE_FIN to download the file
    aws s3 cp "s3://$BACKUP_S3_FIN/3000/$f" "$ROOTDIR/tmp/$f" --profile "$AWS_PROFILE_FIN"
    if [ $? -ne 0 ]; then
        message "err" "Failed to download $f from $BACKUP_S3_FIN."
        exit 1
    fi
    # Use AWS_PROFILE_BIZ to upload the file
    aws s3 cp "$ROOTDIR/tmp/$f" "s3://$BACKUP_S3/$ZONE_ID/$f"
    if [ $? -ne 0 ]; then
        message "err" "Failed to upload $f to $BACKUP_S3."
        exit 1
    fi
done

# Remove the temporary files
rm -rf $ROOTDIR/tmp/

# List the uploaded files
aws s3 ls "s3://$BACKUP_S3/$ZONE_ID/"

message "success" "All files copied successfully."