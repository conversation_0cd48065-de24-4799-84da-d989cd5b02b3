#!/bin/bash

ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)

if [ $# == 1 ]; then
    export NETWORK=$1
fi
# Check if the network is local
if [[ "$NETWORK" == *"local"* ]]; then
    export AWS_CONFIG_FILE=$SCRIPTDIR/env/.aws/config-localstack
    if [[ "$NETWORK" == "localFin" ]]; then
        export AWS_PROFILE=localstack-fin
    else
        export AWS_PROFILE=localstack-biz
    fi
    # TODO: pacess check
fi
