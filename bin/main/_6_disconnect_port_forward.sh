#!/bin/bash

SCRIPTDIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
SCRIPTNAME=$(basename "$0")
source $SCRIPTDIR/../common/utils.sh

if [ $# -ne 1 ]; then
    message "err" "Please specify the NETWORK as the first argument."
    echo "./bin/main/$SCRIPTNAME [NETWORK name]"
    exit 1
fi

source $SCRIPTDIR/_load_aws_prof.sh "$1"
# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    export $(cat ${SCRIPTDIR}/env/."$AWS_PROFILE" | grep -v "^#" | xargs)
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi
# Skip if NETWORK is local
if [[ "$NETWORK" == *"local"* ]]; then
    message "info" "Skipping $SCRIPTNAME for local environments."
    exit 0
fi

# Check if PROVIDER is set and extract the port number
if [ -z "$PROVIDER" ]; then
    message "err" "PROVIDER environment variable is not set."
    exit 1
fi

PROVIDER_PORT=$(echo $PROVIDER | awk -F: '{print $3}')
if [ -z "$PROVIDER_PORT" ]; then
    message "err" "Failed to extract port number from PROVIDER environment variable."
    exit 1
fi
# Get the process ID listening on the PROVIDER_PORT
PID=$(lsof -t -i:$PROVIDER_PORT)

if [ -n "$PID" ]; then
    # Try to gracefully terminate the process
    message "info" "Terminating process $PID on port $PROVIDER_PORT"
    kill $PID
    
    # Wait for the process to terminate
    for i in {1..10}; do
        if ! kill -0 $PID 2>/dev/null; then
            message "success" "Process $PID terminated successfully."
            exit 0
        fi
        sleep 1
    done
    
    # If the process is still running, forcefully kill it
    message "warn" "Process $PID did not terminate, killing it forcefully."
    kill -9 $PID
    
    # Verify if the process has been killed
    if ! kill -0 $PID 2>/dev/null; then
        message "success" "Process $PID killed forcefully."
        exit 0
    else
        message "err" "Failed to kill process $PID."
        exit 1
    fi
else
    message "info" "No process found running on port $PROVIDER_PORT."
    exit 0
fi