#!/bin/bash

SCRIPTDIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
SCRIPTNAME=$(basename "$0")
source $SCRIPTDIR/../common/utils.sh

if [ $# -ne 1 ]; then
    message "err" "Please specify the NETWORK as the first argument."
    echo "./bin/main/$SCRIPTNAME [NETWORK name]"
    exit 1
fi

source $SCRIPTDIR/_load_aws_prof.sh "$1"
# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    export $(cat ${SCRIPTDIR}/env/."$AWS_PROFILE" | grep -v "^#" | xargs)
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

# Skip if NETWORK is local
if [[ "$NETWORK" == *"local"* ]]; then
    message "info" "Skipping $SCRIPTNAME for local environments."
    exit 0
fi

# Check if PROVIDER is set and extract the port number
if [ -z "$PROVIDER" ]; then
    message "err" "PROVIDER environment variable is not set."
    exit 1
fi

PROVIDER_PORT=$(echo $PROVIDER | awk -F: '{print $3}')
if [ -z "$PROVIDER_PORT" ]; then
    message "err" "Failed to extract port number from PROVIDER environment variable."
    exit 1
fi

# Get the instance ID of the BESU VALIDATOR
INSTANCE_ID=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=${BESU_INSTANCE}" --query "Reservations[].Instances[].InstanceId" | grep "i-" | sed 's/^.*"\(.*\)".*$/\1/')

message "info" "Starting port forwarding to BESU VALIDATOR..."
aws ssm start-session --target ${INSTANCE_ID::19} --document-name AWS-StartPortForwardingSession --parameters "portNumber=8451,localPortNumber=$PROVIDER_PORT" &

# Wait for the port forward connection to be established
message "info" "Waiting for port forwarding to be established..."
for i in {1..30}; do
    if curl -s "$PROVIDER/liveness" | grep -q "UP"; then
        message "success" "Port forwarding established successfully."
        exit 0
    fi
    sleep 1
done

message "err" "Failed to establish port forwarding after 30 seconds."
exit 1
