#!/bin/bash

SCRIPTDIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
SCRIPTNAME=$(basename "$0")
source $SCRIPTDIR/../common/utils.sh

# Skip if NETWORK is local
if [[ "$NETWORK" == *"local"* ]]; then
    message "info" "Skipping $SCRIPTNAME for local environments."
    exit 0
fi

if [ $# -lt 1 ]; then
    message "err" "Please specify the NETWORK as the first argument."
    echo "./bin/main/$SCRIPTNAME [NETWORK name]"
    exit 1
fi

source $SCRIPTDIR/_load_aws_prof.sh "$1"
# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    source ${SCRIPTDIR}/env/."$AWS_PROFILE"
elif [ -f ${SCRIPTDIR}/env/."$PROJECT_ENV" ]; then
    source ${SCRIPTDIR}/env/."$PROJECT_ENV"
    ZONE_ID=$2
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

NOW=`TZ=JST-9 date +%Y%m%d-%H%M%S`
BACKUP_PATH="$BACKUP/$ZONE_ID/contract/$NETWORK/$NOW"

message "info" "Backup hardhat deployments to s3://$BACKUP_PATH/"
declare -a files=("deployments" "hardhat.config.ts" ".env" ".env_main_bk" ".env_ibc_bk" ".kms")

for f in "${files[@]}"; do
    if [[ -d $f ]]; then
        # If it is a directory, upload recursively
        aws s3 cp "$f" "s3://$BACKUP_PATH/$f" --recursive
    else
        aws s3 cp "$f" "s3://$BACKUP_PATH/$f"
    fi
done

message "success" "Files have been successfully copied to s3://$BACKUP_PATH/"
# Verify the backed up files
aws s3 ls "s3://$BACKUP_PATH/"
message "success" "Verification of the backed up files is complete."
