#!/bin/bash
ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
export ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
SCRIPTNAME=$(basename "$0")
source ${SCRIPTDIR}/../common/utils.sh

# Ensure NETWORK environment variable is set
if [ -z "$NETWORK" ]; then
  message "err" "NETWORK environment variable is not set."
  exit 1
fi

message "info" "Starting set ibc app for network: ${NETWORK}"

# extract bridge contract address
export IBCAPP_ADDR_TOKEN_TRANSFER=$(./bin/main/_extract-bridge-address.sh)
export IBCAPP_ADDR_ACCOUNT_SYNC=$(./bin/main/_extract-account-bridge-address.sh)
export IBCAPP_ADDR_BALANCE_SYNC=$(./bin/main/_extract-balance-bridge-address.sh)

echo -e "$message"

message="以下のコントラクトアドレスをBridgeコントラクトとしてメインコントラクトに登録します\n"
message+="ADDR_TOKEN_TRANSFER=$IBCAPP_ADDR_TOKEN_TRANSFER\n"
message+="ADDR_ACCOUNT_SYNC=$IBCAPP_ADDR_ACCOUNT_SYNC\n"
message+="ADDR_BALANCE_SYNC=$IBCAPP_ADDR_BALANCE_SYNC"

echo -e "$message"
# set bridge contract to main contract
cd ${ROOTDIR}
./tools/setIBCApp.sh $IBCAPP_ADDR_TOKEN_TRANSFER JPYTokenTransferBridge &
wait

./tools/setIBCApp.sh $IBCAPP_ADDR_ACCOUNT_SYNC AccountSyncBridge &
wait

./tools/setIBCApp.sh $IBCAPP_ADDR_BALANCE_SYNC BalanceSyncBridge &
wait

message "success" "Registration for Bridge Contracts to Main Contracts: ${NETWORK} completed"