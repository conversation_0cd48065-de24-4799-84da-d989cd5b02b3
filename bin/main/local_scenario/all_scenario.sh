#!/bin/bash
export BIN_DIR=$(
  cd $(dirname $BASH_SOURCE)
  pwd
)

if [[ $# -eq 0 ]]; then
  NETWORK=$(basename $(
    cd $(dirname $BASH_SOURCE)
    pwd
  ))
else
  NETWORK=$1
fi

source "${BIN_DIR}"/utils.sh

message "info" "Registering Data for localFin"
"${BIN_DIR}"/register_data.sh localFin

message "info" "Registering Data for localBiz"
"${BIN_DIR}"/register_data.sh localBiz

message "info" "Start account/synchronous scenario"
"${BIN_DIR}"/12_synchronous.sh localBiz

message "info" "Start account/industry scenario"
"${BIN_DIR}"/13_industry.sh localFin

message "info" "Start token/charge scenario"
"${BIN_DIR}"/14_charge.sh localFin

message "info" "Start token/transfer scenario"
"${BIN_DIR}"/15_syncTransfer.sh localBiz

# Fin起点のBizディスチャージ
message "info" "Start token/discharge from FinZone scenario"
"${BIN_DIR}"/16_discharge_from_fin.sh localBiz

message "info" "Start token/discharge scenario"
"${BIN_DIR}"/16_discharge.sh localBiz

# 凍結→強制償却→凍結解除
message "info" "Start frozen/forceBurn scenario"
"${BIN_DIR}"/17_frozen_forceBurn.sh localFin

# 再申し込み
message "info" "Start account/synchronous scenario"
"${BIN_DIR}"/12_synchronous.sh localBiz

# 再申し込み確定
message "info" "Start account/industry scenario"
"${BIN_DIR}"/13_industry.sh localFin

message "info" "Start biz/terminate scenario"
"${BIN_DIR}"/18_biz_terminating.sh localBiz

message "info" "Start biz/terminated scenario"
"${BIN_DIR}"/19_biz_terminated.sh localFin

message "success" "All scenarios have been executed successfully"
