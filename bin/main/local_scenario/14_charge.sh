#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

source "${BIN_DIR}"/utils.sh
source "${BIN_DIR}"/_load_env.sh "$1"

charge() {
    local account_id=$1

    pushd . > /dev/null

    npx hardhat mintToken --network "${NETWORK}" \
        --issuer-id "${ISSUER_ID}" \
        --account-id "${account_id}" \
        --amount "${MINT_AMOUNT}" \
        --issuer-key "${KEY_ISSUER}"

    local output=$(npx hardhat checkExchange --network localFin \
        --account-id "${account_id}" \
        --to-zone-id "${BIZ_ZONE_ID}" \
        --from-zone-id "${ZONE_ID}" \
        --amount "${CHARGE_AMOUNT}")

    echo "$output"

    # Check the output of checkExchange
    if [[ $output == *"result | ok"* ]]; then
        message "info" "start charge to account3"

        npx hardhat registerEscrowAcc \
            --src-zone-id ${ZONE_ID} \
            --dst-zone-id ${BIZ_ZONE_ID} \
            --escrow-account ${ACCOUNT_ID_1} \
            --network ${NETWORK}

        npx hardhat transfer --network "${NETWORK}" \
            --account-id "${account_id}" \
            --from-zone-id "${ZONE_ID}" \
            --to-zone-id "${BIZ_ZONE_ID}" \
            --amount "${CHARGE_AMOUNT}" \
            --timeout-height 1000000

        popd > /dev/null
    else
        echo "failed checkExchange."
    fi
}

charge "${ACCOUNT_ID_3}"

sleep 5

show_accounts_status "${ACCOUNT_ID_3}"
