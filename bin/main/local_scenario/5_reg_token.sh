#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

pushd ${ROOT_DIR} > /dev/null

npx hardhat registerToken --network "${NETWORK}" \
  --prov-key "${KEY_PROV}" \
  --token-id "${TOKEN_ID}" \
  --prov-id "${PROV_ID}" \
  --token-name "${TOKEN_NAME}" \
  --symbol "${TOKEN_SYMBOL}"

popd > /dev/null
