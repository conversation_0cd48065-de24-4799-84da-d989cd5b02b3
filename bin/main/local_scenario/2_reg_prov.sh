#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

FLAG="11"

pushd ${ROOT_DIR} > /dev/null

npx hardhat registerProvider --network "${NETWORK}" \
  --prov-id "${PROV_ID}" \
  --zone-id "${ZONE_ID}" \
  --zone-name "${ZONE_NAME}" \
  --prov-key "${KEY_PROV}" \
  --flag "${FLAG}"

popd > /dev/null