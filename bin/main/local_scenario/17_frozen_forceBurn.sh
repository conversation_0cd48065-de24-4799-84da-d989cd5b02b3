#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

source "${BIN_DIR}"/utils.sh
source "${BIN_DIR}"/_load_env.sh "$1"

getBizZoneAccountStatus() {
    local account_id=$1

    pushd ${ROOT_DIR} > /dev/null
    npx hardhat getBizZoneAccountStatus --network localFin \
        --account-id "${account_id}" \
        --zone-id "3001"
}

frozenForceBurn() {
    local account_id=$1

    pushd ${ROOT_DIR} > /dev/null
    npx hardhat setAccountStatus --network localFin \
        --issuer-id "${ISSUER_ID}" \
        --account-id "${account_id}" \
        --account-status "frozen" \
        --reason-code "${REASON_CODE}" \
        --issuer-key "${KEY_ISSUER}"

    show_accounts_status "${account_id}"
    getBizZoneAccountStatus "${account_id}"

    # Check the output of checkExchange
    npx hardhat forceBurnToken --network localFin \
        --issuer-id "${ISSUER_ID}" \
        --account-id "${account_id}" \
        --issuer-key "${KEY_ISSUER}"

    show_accounts_status "${account_id}"
    getBizZoneAccountStatus "${account_id}"

        npx hardhat setAccountStatus --network localFin \
        --issuer-id "${ISSUER_ID}" \
        --account-id "${account_id}" \
        --account-status "active" \
        --reason-code "${REASON_CODE}" \
        --issuer-key "${KEY_ISSUER}"

    show_accounts_status "${account_id}"
    getBizZoneAccountStatus "${account_id}"
}

frozenForceBurn "${ACCOUNT_ID_3}"
frozenForceBurn "${ACCOUNT_ID_4}"



