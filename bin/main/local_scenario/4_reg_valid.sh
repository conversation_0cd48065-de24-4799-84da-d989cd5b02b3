#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

FLAG="11"

pushd ${ROOT_DIR} > /dev/null

npx hardhat registerValidator --network "${NETWORK}" \
  --valid-id "${VALID_ID}" \
  --issuer-id "${ISSUER_ID}" \
  --valid-name "${VALID_NAME}" \
  --valid-key "${KEY_VALID}" \
  --flag "${FLAG}"

popd > /dev/null
