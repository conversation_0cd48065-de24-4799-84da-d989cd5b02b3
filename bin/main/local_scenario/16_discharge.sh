#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

source "${BIN_DIR}"/utils.sh
source "${BIN_DIR}"/_load_env.sh "$1"

discharge() {
    local account_id=$1

    pushd ${ROOT_DIR} > /dev/null
    local output=$(npx hardhat checkExchange --network localFin \
        --account-id "${account_id}" \
        --to-zone-id "${FIN_ZONE_ID}" \
        --from-zone-id "${ZONE_ID}" \
        --amount "${CHARGE_AMOUNT}")

    echo "$output"

    # Check the output of checkExchange
    if [[ $output == *"result | ok"* ]]; then
        npx hardhat transfer --network "${NETWORK}" \
            --account-id "${account_id}" \
            --from-zone-id "${ZONE_ID}" \
            --to-zone-id "${FIN_ZONE_ID}" \
            --amount "${DISCHARGE_AMOUNT}" \
            --timeout-height 1000000
        popd > /dev/null
    else
        echo "failed exchange."
    fi
}

discharge "${ACCOUNT_ID_4}"

sleep 5

show_accounts_status "${ACCOUNT_ID_4}"
