#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

source "${BIN_DIR}"/utils.sh
source "${BIN_DIR}"/_load_env.sh "$1"

biz_terminating() {
    local account_id=$1

    pushd . > /dev/null
    local output=$(npx hardhat checkSyncAccount --network localFin \
        --account-id "${account_id}" \
        --valid-id "${VALID_ID}" \
        --zone-id "${ZONE_ID}" \
        --account-status "${STATUS_TERMINATING}")

    echo "$output"

    # Check the output of checkSyncAccount
    if [[ $output == *"result | ok"* ]]; then
        npx hardhat syncAccount --network "${NETWORK}" \
            --validator-id "${VALID_ID}" \
            --account-id "${account_id}" \
            --account-name "${ACCOUNT_NAME_3}" \
            --from-zone-id "${ZONE_ID}" \
            --zone-name "${ZONE_NAME}" \
            --account-status "${STATUS_TERMINATING}" \
            --approval-amount "${ZERO_AMOUNT}" \
            --trace-id "${TRACE_ID}" \
            --timeout-height 1000000 \
            --reason-code "${REASON_CODE}"
        popd > /dev/null
    else
        echo "failed checkSyncAccount."
    fi
}

biz_terminating "${ACCOUNT_ID_3}"
biz_terminating "${ACCOUNT_ID_4}"

sleep 10

show_accounts_status "${ACCOUNT_ID_3}"
show_accounts_status "${ACCOUNT_ID_4}"
