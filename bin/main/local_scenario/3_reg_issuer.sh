#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

FLAG="11"
pushd ${ROOT_DIR} > /dev/null

npx hardhat registerIssuer --network "${NETWORK}" \
--issuer-id "${ISSUER_ID}" \
--bank-code "${BANK_CODE}" \
--issuer-name "${ISSUER_NAME}" \
--issuer-key "${KEY_ISSUER}" \
--flag "${FLAG}"

popd > /dev/null
