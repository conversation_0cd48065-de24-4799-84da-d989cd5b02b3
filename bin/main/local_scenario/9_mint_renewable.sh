#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

pushd ${ROOT_DIR} > /dev/null

output=$(npx hardhat checkFinAccountStatus --network "${NETWORK}" \
    --account-id "${ACCOUNT_ID_2}")

echo "$output"

if [[ $output == *"accountStatus | active"* ]]; then
  npx hardhat mintRenewable --network "${NETWORK}" \
    --token-id "${RENEWABLE_ID_1}" \
    --metadata-id "${METADATA_ID}" \
    --metadata-hash "${METADATA_HASH}" \
    --mint-account-id "${ACCOUNT_ID_2}" \
    --owner-account-id  "${ACCOUNT_ID_2}"
  
  npx hardhat mintRenewable --network "${NETWORK}" \
    --token-id "${RENEWABLE_ID_2}" \
    --metadata-id "${METADATA_ID}" \
    --metadata-hash "${METADATA_HASH}" \
    --mint-account-id "${ACCOUNT_ID_2}" \
    --owner-account-id  "${ACCOUNT_ID_2}"
  
  npx hardhat mintRenewable --network "${NETWORK}" \
    --token-id "${RENEWABLE_ID_3}" \
    --metadata-id "${METADATA_ID}" \
    --metadata-hash "${METADATA_HASH}" \
    --mint-account-id "${ACCOUNT_ID_2}" \
    --owner-account-id  "${ACCOUNT_ID_2}"
else
    echo "failed checkFinAccountStatus for account ${ACCOUNT_ID_2}."
fi

popd > /dev/null