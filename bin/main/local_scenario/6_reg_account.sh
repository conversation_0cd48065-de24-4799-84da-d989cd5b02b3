#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

FLAG="11"

pushd ${ROOT_DIR} > /dev/null

npx hardhat registerAcc --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_1}" \
  --account-name "${ACCOUNT_NAME_1}" \
  --account-key "${KEY_ACCOUNT}" \
  --valid-id "${VALID_ID}" \
  --issuer-id "${ISSUER_ID}" \
  --issuer-key "${KEY_ISSUER}" \
  --flag "${FLAG}"

npx hardhat registerAcc --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_2}" \
  --account-name "${ACCOUNT_NAME_2}" \
  --account-key "${KEY_ACCOUNT}" \
  --valid-id "${VALID_ID}" \
  --issuer-id "${ISSUER_ID}" \
  --issuer-key "${KEY_ISSUER}" \
  --flag "${FLAG}"

# Bizアカウント申し込みシナリオ向けにACCOUNT4を利用したいため、Biz側には登録を行わない
if [ $NETWORK != "localBiz" ]; then
  npx hardhat registerAcc --network "${NETWORK}" \
    --account-id "${ACCOUNT_ID_3}" \
    --account-name "${ACCOUNT_NAME_3}" \
    --account-key "${KEY_ACCOUNT}" \
    --valid-id "${VALID_ID}" \
    --issuer-id "${ISSUER_ID}" \
    --issuer-key "${KEY_ISSUER}" \
    --flag "${FLAG}"

  npx hardhat registerAcc --network "${NETWORK}" \
    --account-id "${ACCOUNT_ID_4}" \
    --account-name "${ACCOUNT_NAME_4}" \
    --account-key "${KEY_ACCOUNT}" \
    --valid-id "${VALID_ID}" \
    --issuer-id "${ISSUER_ID}" \
    --issuer-key "${KEY_ISSUER}" \
    --flag "${FLAG}"
fi

popd > /dev/null
