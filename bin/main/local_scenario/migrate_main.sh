#!/bin/bash
export ROOT_DIR=$(
  cd $(dirname "$0")/../../.. || exit
  pwd
)
pushd "${ROOT_DIR}" >/dev/null || exit

export BIN_DIR=$(
  cd $(dirname $BASH_SOURCE)
  pwd
)

source "${BIN_DIR}"/utils.sh
source "${BIN_DIR}"/_load_env.sh

if [[ $# -eq 0 ]]; then
  export NETWORK=$(basename $(
    cd $(dirname $BASH_SOURCE)
    pwd
  ))
else
  export NETWORK=$1
fi

$ROOT_DIR/scripts/clean.sh $NETWORK

if [[ $NETWORK == "localFin" ]]; then
  message "info" "Deploying the contract for ${NETWORK}..."
  "${BIN_DIR}"/1_migrate_main.sh "${NETWORK}"
fi

if [[ $NETWORK == "local" || $NETWORK == "localBiz" ]]; then
  message "info" "Deploying the Renewable contract for ${NETWORK}..."
  "${BIN_DIR}"/8_migrate_biz.sh "${NETWORK}"
fi
