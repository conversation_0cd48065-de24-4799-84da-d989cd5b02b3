#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

pushd ${ROOT_DIR} > /dev/null

dvpOutput=$(npx hardhat checkTransactionRenewable --network "${NETWORK}" \
    --send-account-id "${ACCOUNT_ID_2}" \
    --from-account-id "${ACCOUNT_ID_2}" \
    --to-account-id "${ACCOUNT_ID_1}" \
    --misc2 "${RENEWABLE_ID_1}")

echo "$dvpOutput"

if [[ $dvpOutput == *"result | true"* ]]; then

    npx hardhat dvp --network "${NETWORK}" \
        --send-account-id "${ACCOUNT_ID_1}" \
        --from-account-id "${ACCOUNT_ID_1}" \
        --to-account-id "${ACCOUNT_ID_2}" \
        --amount "${TRANSFER_AMOUNT}" \
        --misc2 "${RENEWABLE_ID_1}" \
        --memo ""
else
    echo "failed checkTransactionRenewable."
fi

dvpMultiOutput=$(npx hardhat checkTransactionRenewable --network "${NETWORK}" \
    --send-account-id "${ACCOUNT_ID_2}" \
    --from-account-id "${ACCOUNT_ID_2}" \
    --to-account-id "${ACCOUNT_ID_1}" \
    --misc2 "${RENEWABLE_ID_2},${RENEWABLE_ID_3}")

echo "$dvpMultiOutput"

if [[ $dvpMultiOutput == *"result | true"* ]]; then
    npx hardhat dvpMulti --network "${NETWORK}" \
        --send-account-id "${ACCOUNT_ID_1}" \
        --from-account-id "${ACCOUNT_ID_1}" \
        --to-account-id "${ACCOUNT_ID_2}" \
        --amount "${TRANSFER_AMOUNT}" \
        --token-id-1 "${RENEWABLE_ID_2}" \
        --token-id-2 "${RENEWABLE_ID_3}" \
        --memo ""

else
    echo "failed checkTransactionRenewable."
fi

popd > /dev/null