#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

source "${BIN_DIR}"/utils.sh
source "${BIN_DIR}"/_load_env.sh "$1"

biz_terminated() {
    local account_id=$1

    pushd ${ROOT_DIR} > /dev/null
    local output=$(npx hardhat getAccountAll --network "${NETWORK}" \
        --account-id "${account_id}" \
        --valid-id "${VALID_ID}")
    popd > /dev/null

    # Check the output of getAccountAll
    if [[ $output != *"Not linked from Biz Zone."* ]]; then
        npx hardhat setBizZoneTerminated --network "${NETWORK}" \
            --account-id "${account_id}" \
            --zone-id "${BIZ_ZONE_ID}"
    else
        echo "BZ account is not linked."
    fi
}

biz_terminated "${ACCOUNT_ID_3}"
biz_terminated "${ACCOUNT_ID_4}"

show_accounts_status "${ACCOUNT_ID_3}"
show_accounts_status "${ACCOUNT_ID_4}"
