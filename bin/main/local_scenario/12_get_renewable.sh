#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

pushd ${ROOT_DIR} > /dev/null

npx hardhat getToken --network "${NETWORK}" \
    --token-id "${RENEWABLE_ID_1}"

npx hardhat getToken --network "${NETWORK}" \
    --token-id "${RENEWABLE_ID_2}"

npx hardhat getToken --network "${NETWORK}" \
    --token-id "${RENEWABLE_ID_3}"

popd > /dev/null