#!/bin/bash
export BIN_DIR=$(
  cd $(dirname $BASH_SOURCE)
  pwd
)

source "${BIN_DIR}"/utils.sh
source "${BIN_DIR}"/_load_env.sh

if [[ $# -eq 0 ]]; then
  export NETWORK=$(basename $(
    cd $(dirname $BASH_SOURCE)
    pwd
  ))
else
  export NETWORK=$1
fi

if [[ $NETWORK == "local" || $NETWORK == "localFin" ]]; then
  message "info" "Registering Provier for ${NETWORK}..."
  "${BIN_DIR}"/2_reg_prov.sh "${NETWORK}"

  message "info" "Registering Issuer for ${NETWORK}..."
  "${BIN_DIR}"/3_reg_issuer.sh "${NETWORK}"

  message "info" "Setting valid ID for ${NETWORK}..."
  "${BIN_DIR}"/4_reg_valid.sh "${NETWORK}"

  message "info" "Registering Token for ${NETWORK}..."
  "${BIN_DIR}"/5_reg_token.sh "${NETWORK}"

  message "info" "Setting Account ID for ${NETWORK}..."
  "${BIN_DIR}"/6_reg_account.sh "${NETWORK}"

  message "info" "Minting Tokens for ${NETWORK}..."
  "${BIN_DIR}"/7_mint_token.sh "${NETWORK}"

  message "info" "Burning Tokens for ${NETWORK}..."
  "${BIN_DIR}"/7_burn_token.sh "${NETWORK}"

  message "info" "Registering BisZone for ${NETWORK}..."
  "${BIN_DIR}"/11_reg_bizzone.sh  "${NETWORK}"
fi

if [[ $NETWORK == "localBiz" ]]; then
  message "info" "Registering Provier for ${NETWORK}..."
  "${BIN_DIR}"/2_reg_prov.sh "${NETWORK}"

  message "info" "Registering Issuer for ${NETWORK}..."
  "${BIN_DIR}"/3_reg_issuer.sh "${NETWORK}"

  message "info" "Setting valid ID for ${NETWORK}..."
  "${BIN_DIR}"/4_reg_valid.sh "${NETWORK}"

  message "info" "Registering Token for ${NETWORK}..."
  "${BIN_DIR}"/5_reg_token.sh "${NETWORK}"

  message "info" "Setting Account ID for ${NETWORK}..."
  "${BIN_DIR}"/6_reg_account.sh "${NETWORK}"

  message "info" "Minting Tokens for ${NETWORK}..."
  "${BIN_DIR}"/7_mint_token.sh "${NETWORK}"

  message "info" "Burning Tokens for ${NETWORK}..."
  "${BIN_DIR}"/7_burn_token.sh "${NETWORK}"

  message "info" "Approve for ${NETWORK}..."
  "${BIN_DIR}"/7_approve.sh "${NETWORK}"

  message "info" "Minting Renewable Tokens for ${NETWORK}..."
  "${BIN_DIR}"/9_mint_renewable.sh "${NETWORK}"

  message "info" "Get Renewable Tokens for ${NETWORK}..."
  "${BIN_DIR}"/12_get_renewable.sh "${NETWORK}"

  message "info" "DvP Renewable Tokens for ${NETWORK}..."
  "${BIN_DIR}"/11_dvp_renewable.sh "${NETWORK}"

  message "info" "Get Renewable Tokens for ${NETWORK}..."
  "${BIN_DIR}"/12_get_renewable.sh "${NETWORK}"
fi
