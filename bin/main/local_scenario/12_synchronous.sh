#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

source "${BIN_DIR}"/utils.sh
source "${BIN_DIR}"/_load_env.sh "$1"

sync_account() {
    local account_id=$1
    local account_name=$2

    pushd . > /dev/null
    local output=$(npx hardhat checkSyncAccount --network localFin \
        --account-id "${account_id}" \
        --valid-id "${VALID_ID}" \
        --zone-id "${ZONE_ID}" \
        --account-status "${STATUS_APPLYING}")

    echo "$output"

    if [[ $output == *"result | ok"* ]]; then
        message "info" "start syncAccount for account ${account_id}."

        npx hardhat syncAccount --network "${NETWORK}" \
            --validator-id "${VALID_ID}" \
            --account-id "${account_id}" \
            --account-name "${account_name}" \
            --from-zone-id "${ZONE_ID}" \
            --zone-name "${ZONE_NAME}" \
            --account-status "${STATUS_APPLYING}" \
            --approval-amount "${ZERO_AMOUNT}" \
            --trace-id "${TRACE_ID}" \
            --timeout-height 1000000 \
            --reason-code "${REASON_CODE}"

        message "success" "syncAccount for account ${account_id} is successful."

        popd > /dev/null
    else
        echo "failed checkSyncAccount for account ${account_id}."
    fi
}

sync_account "${ACCOUNT_ID_3}" "${ACCOUNT_NAME_3}"
sync_account "${ACCOUNT_ID_4}" "${ACCOUNT_NAME_4}"

sleep 10

show_accounts_status "${ACCOUNT_ID_3}"
show_accounts_status "${ACCOUNT_ID_4}"
