#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

pushd ${ROOT_DIR} > /dev/null

output=$(npx hardhat checkBurn --network "${NETWORK}" \
  --issuer-id "${ISSUER_ID}" \
  --account-id "${ACCOUNT_ID_1}" \
  --amount "${BURN_AMOUNT}" \
  --issuer-key "${KEY_ISSUER}")

echo "$output"

if [[ $output == *"result | ok"* ]]; then
  npx hardhat burnToken --network "${NETWORK}" \
    --issuer-id "${ISSUER_ID}" \
    --account-id "${ACCOUNT_ID_1}" \
    --amount "${BURN_AMOUNT}" \
    --issuer-key "${KEY_ISSUER}"
else
    echo "failed burnToken for account ${ACCOUNT_ID_1}."
fi

popd > /dev/null