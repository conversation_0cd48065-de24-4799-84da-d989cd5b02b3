#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

source "${BIN_DIR}"/utils.sh
source "${BIN_DIR}"/_load_env.sh "$1"

sync_transfer() {
    local from_account_id=$1
    local to_account_id=$2

    pushd . > /dev/null

    local output=$(npx hardhat checkTransaction --network localFin \
        --send-account-id "${from_account_id}" \
        --from-account-id "${from_account_id}" \
        --to-account-id "${to_account_id}" \
        --zone-id "${ZONE_ID}" \
        --valid-id "${VALID_ID}" \
        --amount "${CHARGE_AMOUNT}") 
    popd > /dev/null

    echo "$output"

    # Check the output of checkTransaction
    if [[ $output == *"result | ok"* ]]; then
        npx hardhat transferSingle --network "${NETWORK}" \
            --send-account-id "${from_account_id}" \
            --from-account-id "${from_account_id}" \
            --to-account-id "${to_account_id}" \
            --amount "${TRANSFER_AMOUNT}" \
            --misc-value-1 "${MISC_VALUE_1}" \
            --misc-value-2 "${MISC_VALUE_2}" \
            --memo "${MEMO}" \
            --trace-id "${TRACE_ID}"

        popd > /dev/null
    else
        echo "failed checkTransaction."
    fi
}

sync_transfer "${ACCOUNT_ID_3}" "${ACCOUNT_ID_4}"

sleep 5

show_accounts_status "${ACCOUNT_ID_3}"
show_accounts_status "${ACCOUNT_ID_4}"
