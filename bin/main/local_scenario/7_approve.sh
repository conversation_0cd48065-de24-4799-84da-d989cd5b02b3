#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

pushd ${ROOT_DIR} > /dev/null

output=$(npx hardhat checkApprove --network "${NETWORK}" \
  --valid-id "${VALID_ID}" \
  --owner-id "${ACCOUNT_ID_1}" \
  --spender-id "${ACCOUNT_ID_2}" \
  --amount "999999" \
  --valid-key "${KEY_VALID}")

echo "$output"

if [[ $output == *"result | ok"* ]]; then
  npx hardhat approve --network "${NETWORK}" \
    --valid-id "${VALID_ID}" \
    --owner-id "${ACCOUNT_ID_1}" \
    --spender-id "${ACCOUNT_ID_2}" \
    --amount "999999" 
else
    echo "failed approve for owner id ${ACCOUNT_ID_1} spender id ${ACCOUNT_ID_2}."
fi

popd > /dev/null