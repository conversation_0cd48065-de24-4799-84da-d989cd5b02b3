#!/bin/bash
export ROOT_DIR=$(
  cd $(dirname "$0")/../../.. || exit
  pwd
)
pushd "${ROOT_DIR}" >/dev/null || exit

export BIN_DIR=$(
  cd $(dirname $BASH_SOURCE)
  pwd
)

source "${BIN_DIR}"/utils.sh

pushd "${ROOT_DIR}" >/dev/null

if [[ $# -eq 0 ]]; then
  export NETWORK=$(basename $(
    cd $(dirname $BASH_SOURCE)
    pwd
  ))
else
  export NETWORK=$1
fi

source "${BIN_DIR}"/_load_env.sh $NETWORK

message "info" "Start Deploying the contract for ${NETWORK}..."
npx hardhat deploy --network "${NETWORK}" --tags ibc-contracts
if [ $? -eq 0 ]; then
  message "success" "Success deploying the contract for ${NETWORK}."
else
  message "err" "Failed Deploying the contract for ${NETWORK}..."
  exit 1
fi

popd >/dev/null
