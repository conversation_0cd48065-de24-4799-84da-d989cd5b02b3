#!/bin/bash
ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
source $SCRIPTDIR/../common/utils.sh

# Select network from "hardhat list-networks"
if [ -n "$1" ]; then
    CHOICE=$1
else
    # Hardhatからネットワーク一覧を取得
    DIRECTORIES=$(npx hardhat list-networks)
    menu "Select the network to create kms key:" "$DIRECTORIES"
    # menu 関数後の $CHOICE を確認
    if [ -z "$CHOICE" ]; then
        message "err" "Invalid selection. Please try again."
        exit 1
    fi
fi

# set NETWORK
export NETWORK=$CHOICE
message "info" "Using provided network: $NETWORK"

# Error handling function
handle_error() {
    message "err" "An unexpected error occurred. Exiting."
    exit 1
}
trap 'handle_error' ERR


# Load local AWS_PROFILE
source $SCRIPTDIR/_load_aws_prof.sh
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    message "info" "AWS_PROFILE is set to \"$AWS_PROFILE\"."
    choice "Do you want to proceed with the KMS key creation using this \"$AWS_PROFILE\"?"

    source ${SCRIPTDIR}/env/."$AWS_PROFILE"
elif [ -f ${SCRIPTDIR}/env/."$PROJECT_ENV" ]; then
    source ${SCRIPTDIR}/env/."$PROJECT_ENV"
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

# エイリアスがすでに存在するか確認
EXISTING_KEY_ID=$(aws kms list-aliases --query "Aliases[?AliasName=='alias/$KEY_ALIAS'].TargetKeyId" --output text)

if [ -n "$EXISTING_KEY_ID" ]; then
    echo "KMS key with alias '$KEY_ALIAS' already exists. Using existing key ID: $EXISTING_KEY_ID"
    KEY_ID=$EXISTING_KEY_ID
else
    # KMSキーの作成
    echo "Creating a new KMS key in region $REGION..."
    KEY_ID=$(aws kms create-key --description "KMS key for contract deployment" \
        --key-usage SIGN_VERIFY --key-spec ECC_SECG_P256K1 --region $REGION \
        --query KeyMetadata.KeyId --output text)

    # キーが作成されたか確認
    if [ -z "$KEY_ID" ]; then
        echo "Failed to create KMS key."
        exit 1
    fi

    echo "KMS key created with ID: $KEY_ID"

    # キーにエイリアスを設定
    echo "Creating alias for the KMS key..."
    aws kms create-alias --alias-name "alias/$KEY_ALIAS" --target-key-id "$KEY_ID" --region $REGION

    echo "Alias created: alias/$KEY_ALIAS"
fi

# KMSキーIDを.kmsファイルに保存
echo "Saving KMS Key ID to .kms ..."
if [[ $NETWORK == *"Fin"* ]]; then
    echo "KMS_KEY_ID_FIN=$KEY_ID" > $ROOTDIR/.kms
else 
    echo "KMS_KEY_ID_BIZ=$KEY_ID" > $ROOTDIR/.kms
fi

echo "KMS key setup complete. Key ID: $KEY_ID"
