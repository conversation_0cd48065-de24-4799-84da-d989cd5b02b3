#!/bin/bash
# 
# description
# リリース後にメインコントラクト、IBCコントラクトに双方で生成したアドレスが設定されているか確認するシェルです。
# sh ./bin/main/_check_set_address.sh {メインコントラクトで生成されたabiファイル格納path}
# 実行例 
# sh ./bin/main/_check_account.sh 61000e3ac0011d520cf4f8f8c33c4a71 805hmPoaeCfGXurFX5ndYVxZ0da4GZig

ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
SCRIPTDIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
SCRIPTNAME=$(basename "$0")
export MAIN_CONTRACT_DIR="${MAIN_CONTRACT_DIR:-$SCRIPTDIR/../../../dcbg-dcjpy-contract}"

export $(cat ${MAIN_CONTRACT_DIR}/.env | grep -v "^#" | xargs)
SET_ADDR_TOKEN_TRANSFER=$(./bin/main/_extract-bridge-address.sh)
SET_ADDR_ACCOUNT_SYNC=$(./bin/main/_extract-account-bridge-address.sh)
SET_ADDR_BALANCE_SYNC=$(./bin/main/_extract-balance-bridge-address.sh)

SET_IBC_TOKEN_ADDRESS=$(cat s3-restore/deployments/${NETWORK}/IBCToken.json | jq -r ".address")
SET_VALIDATOR_ADDRESS=$(cat s3-restore/deployments/${NETWORK}/Validator.json | jq -r ".address")
SET_ACCOUNT_ADDRESS=$(cat s3-restore/deployments/${NETWORK}/Account.json | jq -r ".address")
SET_ACCESS_CTRL_ADDRESS=$(cat s3-restore/deployments/${NETWORK}/AccessCtrl.json | jq -r ".address")

cd ./deployments/${NETWORK}
SET_IBC_ADDR_TOKEN_TRANSFER=$(cat ./JPYTokenTransferBridge.json  | jq -r ".address")
SET_IBC_ADDR_ACCOUNT_SYNC=$(cat ./AccountSyncBridge.json  | jq -r ".address")
SET_IBC_ADDR_BALANCE_SYNC=$(cat ./BalanceSyncBridge.json  | jq -r ".address")

echo "メインコントラクトにIBCコントラクトで生成されたアドレスが設定されたことを確認します。"
if [[ "$SET_ADDR_TOKEN_TRANSFER" == "$SET_IBC_ADDR_TOKEN_TRANSFER" ]]; then
    echo "OK：メインコントラクトに設定したJPYTokenTransferBridge.jsonのアドレスがIBCコントラクトで生成されたアドレスであることを確認しました。"
else
    echo "NG：メインコントラクトに設定したJPYTokenTransferBridge.jsonのアドレスがIBCコントラクトで生成されたアドレスではありません。"
fi

if [[ "$SET_ADDR_ACCOUNT_SYNC" == "$SET_IBC_ADDR_ACCOUNT_SYNC" ]]; then
    echo "OK：メインコントラクトに設定したAccountSyncBridge.jsonのアドレスがIBCコントラクトで生成されたアドレスであることを確認しました。"
else
    echo "NG：メインコントラクトに設定したAccountSyncBridge.jsonのアドレスがIBCコントラクトで生成されたアドレスではありません。"
fi

if [[ "$SET_ADDR_BALANCE_SYNC" == "$SET_IBC_ADDR_BALANCE_SYNC" ]]; then
    echo "OK：メインコントラクトに設定したBalanceSyncBridge.jsonのアドレスがIBCコントラクトで生成されたアドレスであることを確認しました。"
else
    echo "NG：メインコントラクトに設定したBalanceSyncBridge.jsonのアドレスがIBCコントラクトで生成されたアドレスではありません。"
fi
echo "メインコントラクトのアドレスの確認が完了しました。"

cd $1
SET_MAIN_IBC_TOKEN_ADDRESS=$(cat ./IBCToken.json  | jq -r ".address")
SET_MAIN_VALIDATOR_ADDRESS=$(cat ./Validator.json  | jq -r ".address")
SET_MAIN_ACCOUNT_ADDRESS=$(cat ./Account.json  | jq -r ".address")
SET_MAIN_ACCESS_CTRL_ADDRESS=$(cat ./AccessCtrl.json  | jq -r ".address")

echo "IBCコントラクトにメインコントラクトで生成されたアドレスが設定されたことを確認します。"
if [[ "$SET_IBC_TOKEN_ADDRESS" == "$SET_MAIN_IBC_TOKEN_ADDRESS" ]]; then
    echo "OK：IBCコントラクトに設定したIBCToken.jsonのアドレスがメインコントラクトで生成されたアドレスであることを確認しました。"
else
    echo "NG：IBCコントラクトに設定したIBCToken.jsonのアドレスがメインコントラクトで生成されたアドレスではありません。"
fi

if [[ "$SET_VALIDATOR_ADDRESS" == "$SET_MAIN_VALIDATOR_ADDRESS" ]]; then
    echo "OK：IBCコントラクトに設定したValidator.jsonのアドレスがメインコントラクトで生成されたアドレスであることを確認しました。"
else
    echo "NG：IBCコントラクトに設定したValidator.jsonのアドレスがメインコントラクトで生成されたアドレスではありません。"
fi

if [[ "$SET_ACCOUNT_ADDRESS" == "$SET_MAIN_ACCOUNT_ADDRESS" ]]; then
    echo "OK：IBCコントラクトに設定したAccount.jsonのアドレスがメインコントラクトで生成されたアドレスであることを確認しました。"
else
    echo "NG：IBCコントラクトに設定したAccount.jsonのアドレスがメインコントラクトで生成されたアドレスではありません。"
fi

if [[ "$SET_ACCESS_CTRL_ADDRESS" == "$SET_MAIN_ACCESS_CTRL_ADDRESS" ]]; then
    echo "OK：IBCコントラクトに設定したAccessCtrl.jsonのアドレスがメインコントラクトで生成されたアドレスであることを確認しました。"
else
    echo "NG：IBCコントラクトに設定したAccessCtrl.jsonのアドレスがメインコントラクトで生成されたアドレスではありません。"
fi
echo "IBCコントラクトのアドレスの確認が完了しました。"

echo "NGが出力されなければ問題ありません。"