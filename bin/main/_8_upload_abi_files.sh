#!/bin/bash

SCRIPTDIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
SCRIPTNAME=$(basename "$0")
source $SCRIPTDIR/../common/utils.sh

# Skip if NETWORK is local
if [[ "$NETWORK" == *"local"* ]]; then
    message "info" "Skipping $SCRIPTNAME for local environments."
    exit 0
fi

if [ $# -lt 1 ]; then
    message "err" "Please specify the NETWORK as the first argument."
    echo "./bin/main/$SCRIPTNAME [NETWORK name]"
    exit 1
fi

source $SCRIPTDIR/_load_aws_prof.sh "$1"
# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    source ${SCRIPTDIR}/env/."$AWS_PROFILE"
elif [ -f ${SCRIPTDIR}/env/."$PROJECT_ENV" ]; then
    source ${SCRIPTDIR}/env/."$PROJECT_ENV"
    ZONE_ID=$2
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)
pushd ${ROOTDIR}/deployments/${NETWORK} > /dev/null

message "info" "Upload ABI to s3://$BACKUP_S3/$ZONE_ID"

# Define the files to upload based on the ZONE_ID
case "$ZONE_ID" in
    3000)
        declare -a files=("Token.json" "Account.json" "Validator.json" "Provider.json" "Issuer.json" "IBCToken.json" "FinancialCheck.json" "BusinessZoneAccount.json" "FinancialZoneAccount.json" "JPYTokenTransferBridge.json" "AccountSyncBridge.json" "BalanceSyncBridge.json")
    ;;
    3001 | [3][0-9][0-9][1-9])
        declare -a files=("Token.json" "Account.json" "Validator.json" "Provider.json" "IBCToken.json" "RenewableEnergyToken.json" "JPYTokenTransferBridge.json" "AccountSyncBridge.json" "BalanceSyncBridge.json")
    ;;
    *)
        message "err" "Invalid ZONE_ID: ${ZONE_ID}. Please set ZONE_ID to 3000 ~ 3999."
        exit 1
    ;;
esac

# Upload the files to S3
for f in "${files[@]}"; do
    aws s3 cp "$f" "s3://$BACKUP_S3/$ZONE_ID/"
done
message "success" "Files have been successfully copied to s3://$BACKUP/$ZONE_ID/"

# List the uploaded files
aws s3 ls "s3://$BACKUP_S3/$ZONE_ID/"
message "success" "Verification of the backed up files is complete."

popd > /dev/null
