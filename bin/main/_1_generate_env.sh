#!/bin/bash

SCRIPTDIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
SCRIPTNAME=$(basename "$0")
source $SCRIPTDIR/../common/utils.sh

if [ $# -ne 1 ]; then
    message "err" "Please specify the NETWORK as the first argument."
    echo "./bin/main/$SCRIPTNAME [NETWORK name]"
    exit 1
fi

source $SCRIPTDIR/_load_aws_prof.sh "$1"
# Load environment variables
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    export $(cat ${SCRIPTDIR}/env/."$AWS_PROFILE" | grep -v "^#" | xargs)
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

# contract配下に移動
ENVDIR=$(cd $(dirname $BASH_SOURCE)/../..; pwd)

if [ -f $ENVDIR/.env ]; then
    # 指定した文字列を削除
    if [ "$(uname)" == 'Darwin' ]; then
        sed -i '' '/KEY_ADMIN/d' $ENVDIR/.env
        sed -i '' '/NETWORK_ID/d' $ENVDIR/.env
        sed -i '' '/PROVIDER/d' $ENVDIR/.env
        sed -i '' '/NETWORK/d' $ENVDIR/.env
    else
        # Linux(CodeBuild)
        sed -i '/KEY_ADMIN/d' $ENVDIR/.env
        sed -i '/NETWORK_ID/d' $ENVDIR/.env
        sed -i '/PROVIDER/d' $ENVDIR/.env
        sed -i '/NETWORK/d' $ENVDIR/.env
    fi
fi

# KEY_ADMINを再生成し.envに追加する
KEY=$(node ./tools/generateRandomHex.js|grep privateKey=|cut -d'x' -f2)
echo "KEY_ADMIN=${KEY}" >> $ENVDIR/.env
# NETWORK_IDを.envに追加する
echo "NETWORK_ID=${NETWORK_ID}" >> $ENVDIR/.env
# BESU_NAMESPACEを.envに追加する
echo "BESU_NAMESPACE"=${BESU_NAMESPACE} >> $ENVDIR/.env
# PROVIDERを.envに追加する
echo "PROVIDER=${PROVIDER}" >> $ENVDIR/.env
# NETWORKを.envに追加する
echo "NETWORK=${NETWORK}" >> $ENVDIR/.env
# ZONE_IDを.envに追加する
echo "ZONE_ID=${ZONE_ID}" >> $ENVDIR/.env