#!/bin/bash
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)
if [ -f ${SCRIPTDIR}/env/."$AWS_PROFILE" ]; then
    source ${SCRIPTDIR}/env/."$AWS_PROFILE"
elif [ -f ${SCRIPTDIR}/env/."$PROJECT_ENV" ]; then
    source ${SCRIPTDIR}/env/."$PROJECT_ENV"
else
    message "err" "Please specify an environment name that exists in ./bin/main/env/"
    exit 1
fi

if [ $1 = "main" ]; then
  npx hardhat deployConfirmation_main --network ${NETWORK}
else
  npx hardhat deployConfirmation_ibc --network ${NETWORK}
fi