#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

FLAG="11"

pushd ${ROOT_DIR} > /dev/null

export NETWORK=`basename $(cd $(dirname "${BIN_DIR}"); pwd)`

npx hardhat checkExchange --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_1}" \
  --to-zone-id "${BIZ_ZONE_ID}" \
  --from-zone-id "${ZONE_ID}" \
  --amount "${CHARGE_AMOUNT}"

popd > /dev/null
