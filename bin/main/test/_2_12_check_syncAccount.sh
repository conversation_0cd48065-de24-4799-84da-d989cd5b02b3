#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

FLAG="11"

pushd ${ROOT_DIR} > /dev/null

export NETWORK=`basename $(cd $(dirname "${BIN_DIR}"); pwd)`

npx hardhat checkSyncAccount --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_2}" \
  --valid-id "${VALID_ID}" \
  --zone-id "${ZONE_ID}" \
  --account-status "${STATUS_TERMINATING}"

popd > /dev/null
