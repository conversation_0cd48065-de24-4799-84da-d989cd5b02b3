#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

FLAG="11"

pushd ${ROOT_DIR} > /dev/null

export NETWORK=`basename $(cd $(dirname "${BIN_DIR}"); pwd)`

npx hardhat registerAcc --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_3}" \
  --account-name "${ACCOUNT_NAME_3}" \
  --account-key "${KEY_ACCOUNT}" \
  --valid-id "${VALID_ID}" \
  --issuer-id "${ISSUER_ID}" \
  --issuer-key "${KEY_ISSUER}" \
  --flag "${FLAG}"

popd > /dev/null
