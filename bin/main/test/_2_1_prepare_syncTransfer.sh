#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

FLAG="11"

pushd ${ROOT_DIR} > /dev/null

export NETWORK=`basename $(cd $(dirname "${BIN_DIR}"); pwd)`

npx hardhat mintToken --network "${NETWORK}" \
  --issuer-id "${ISSUER_ID}" \
  --account-id "${ACCOUNT_ID_1}" \
  --amount "${MINT_AMOUNT}" \
  --issuer-key "${KEY_ISSUER}"

npx hardhat getAccountAll --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_1}" \
  --valid-id "${VALID_ID}"

npx hardhat getAccountAll --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_2}" \
  --valid-id "${VALID_ID}"

npx hardhat checkTransaction --network "${NETWORK}" \
  --send-account-id "${ACCOUNT_ID_1}" \
  --from-account-id "${ACCOUNT_ID_1}" \
  --to-account-id "${ACCOUNT_ID_2}" \
  --zone-id "${ZONE_ID}" \
  --valid-id "${VALID_ID}" \
  --amount "${CHARGE_AMOUNT}"

popd > /dev/null
