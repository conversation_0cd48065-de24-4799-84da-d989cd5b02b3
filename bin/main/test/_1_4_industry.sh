#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

FLAG="11"

export NETWORK=`basename $(cd $(dirname "${BIN_DIR}"); pwd)`

pushd ${ROOT_DIR} > /dev/null
output=$(npx hardhat getAccountAll --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_1}" \
  --valid-id "${VALID_ID}")

echo "$output"

npx hardhat setActiveBusinessAccountWithZone --network "${NETWORK}" \
    --validator-id "${VALID_ID}" \
    --account-id "${ACCOUNT_ID_1}" \
    --zone-id "${BIZ_ZONE_ID}" \

npx hardhat getAccountAll --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_1}" \
  --valid-id "${VALID_ID}"

popd > /dev/null
