#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

FLAG="11"

export NETWORK=`basename $(cd $(dirname "${BIN_DIR}"); pwd)`

pushd ${ROOT_DIR} > /dev/null

npx hardhat registerProvider --network "${NETWORK}" \
  --prov-id "${PROV_ID}" \
  --zone-id "${ZONE_ID}" \
  --zone-name "${ZONE_NAME}" \
  --prov-key "${KEY_PROV}" \
  --flag "${FLAG}"

npx hardhat registerIssuer --network "${NETWORK}" \
  --issuer-id "${ISSUER_ID}" \
  --bank-code "${BANK_CODE}" \
  --issuer-name "${ISSUER_NAME}" \
  --issuer-key "${KEY_ISSUER}" \
  --flag "${FLAG}"

npx hardhat registerValidator --network "${NETWORK}" \
  --valid-id "${VALID_ID}" \
  --issuer-id "${ISSUER_ID}" \
  --valid-name "${VALID_NAME}" \
  --valid-key "${KEY_VALID}" \
  --flag "${FLAG}"

npx hardhat registerToken --network "${NETWORK}" \
  --prov-key "${KEY_PROV}" \
  --token-id "${TOKEN_ID}" \
  --prov-id "${PROV_ID}" \
  --token-name "${TOKEN_NAME}" \
  --symbol "${TOKEN_SYMBOL}"

npx hardhat registerAcc --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_1}" \
  --account-name "${ACCOUNT_NAME_1}" \
  --account-key "${KEY_ACCOUNT}" \
  --valid-id "${VALID_ID}" \
  --issuer-id "${ISSUER_ID}" \
  --issuer-key "${KEY_ISSUER}" \
  --flag "${FLAG}"

npx hardhat registerAcc --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_2}" \
  --account-name "${ACCOUNT_NAME_2}" \
  --account-key "${KEY_ACCOUNT}" \
  --valid-id "${VALID_ID}" \
  --issuer-id "${ISSUER_ID}" \
  --issuer-key "${KEY_ISSUER}" \
  --flag "${FLAG}"

npx hardhat getAccountAll --network "${NETWORK}" \
  --valid-id "${VALID_ID}" \
  --account-id "${ACCOUNT_ID_1}"

npx hardhat getAccountAll --network "${NETWORK}" \
  --valid-id "${VALID_ID}" \
  --account-id "${ACCOUNT_ID_2}"

popd > /dev/null
