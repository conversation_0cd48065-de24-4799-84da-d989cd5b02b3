#!/bin/bash

ROOTDIR=$(cd $(dirname $BASH_SOURCE)/../../..; pwd)
SCRIPTDIR=$(cd $(dirname $BASH_SOURCE); pwd)

# 環境を選択する
PS3="テスト対象環境を選択して下さい。qで終了します。 > "
envs=$(ls -A ${SCRIPTDIR}/../env/ | tr -d .)
ENV=""
select ENV in $envs
do
  if [ "${REPLY}" = "q" ]; then
      echo "終了します"
      exit 0
  fi
  if [ -n "${ENV}" ]; then
    break
  else
    echo "環境を選択してください"
  fi
done

# 1.envファイルを作成する
${SCRIPTDIR}/../_1_generate_env.sh ${ENV}

# 2.ポートフォワード接続する
${SCRIPTDIR}/../_2_port_forward.sh ${ENV}
sleep 5
wait

source "${SCRIPTDIR}"/_load_env_test_main.sh

# 1_4.BZ口座開設
${SCRIPTDIR}/_1_4_industry.sh

# 5. ポートフォワードを切断する
${SCRIPTDIR}/../_5_disconnect_port_forward.sh