#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

FLAG="11"

export NETWORK=`basename $(cd $(dirname "${BIN_DIR}"); pwd)`

pushd ${ROOT_DIR} > /dev/null
output=$(npx hardhat getAccountAll --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_2}" \
  --valid-id "${VALID_ID}")

# Check the output of getAccountAll
if [[ $output != *"Not linked from Biz Zone."* ]]; then
  npx hardhat setActiveBusinessAccountWithZone --network "${NETWORK}" \
    --validator-id "${VALID_ID}" \
    --account-id "${ACCOUNT_ID_2}" \
    --zone-id "${BIZ_ZONE_ID}" \
else
  echo "BZ account is not linked."
fi

npx hardhat getAccountAll --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_2}" \
  --valid-id "${VALID_ID}"

popd > /dev/null
