#!/bin/bash
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)

FLAG="11"

pushd ${ROOT_DIR} > /dev/null

export NETWORK=`basename $(cd $(dirname "${BIN_DIR}"); pwd)`

npx hardhat checkSyncAccount --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_3}" \
  --valid-id "${VALID_ID}" \
  --zone-id "${BIZ_ZONE_ID}" \
  --account-status "${STATUS_APPLYING}"  

npx hardhat checkTransaction --network "${NETWORK}" \
  --send-account-id "${ACCOUNT_ID_1}" \
  --from-account-id "${ACCOUNT_ID_4}" \
  --to-account-id "${ACCOUNT_ID_2}" \
  --zone-id "${ZONE_ID}" \
  --valid-id "${VALID_ID}" \
  --amount "${CHARGE_AMOUNT}"

npx hardhat checkTransaction --network "${NETWORK}" \
  --send-account-id "${ACCOUNT_ID_4}" \
  --from-account-id "${ACCOUNT_ID_1}" \
  --to-account-id "${ACCOUNT_ID_2}" \
  --zone-id "${ZONE_ID}" \
  --valid-id "${VALID_ID}" \
  --amount "${CHARGE_AMOUNT}"

npx hardhat checkTransaction --network "${NETWORK}" \
  --send-account-id "${ACCOUNT_ID_1}" \
  --from-account-id "${ACCOUNT_ID_1}" \
  --to-account-id "${ACCOUNT_ID_4}" \
  --zone-id "${ZONE_ID}" \
  --valid-id "${VALID_ID}" \
  --amount "${CHARGE_AMOUNT}"

npx hardhat checkTransaction --network "${NETWORK}" \
  --send-account-id "${ACCOUNT_ID_1}" \
  --from-account-id "${ACCOUNT_ID_1}" \
  --to-account-id "${ACCOUNT_ID_2}" \
  --zone-id "${ZONE_ID}" \
  --valid-id "${VALID_ID}" \
  --amount "${DAILY_LIMIT_AMOUNT}"

npx hardhat checkTransaction --network "${NETWORK}" \
  --send-account-id "${ACCOUNT_ID_1}" \
  --from-account-id "${ACCOUNT_ID_1}" \
  --to-account-id "${ACCOUNT_ID_2}" \
  --zone-id "${ZONE_ID}" \
  --valid-id "${VALID_ID}" \
  --amount "${LIMIT_AMOUNT}"

npx hardhat checkExchange --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_4}" \
  --to-zone-id "${BIZ_ZONE_ID}" \
  --from-zone-id "${ZONE_ID}" \
  --amount "${CHARGE_AMOUNT}"

npx hardhat checkExchange --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_3}" \
  --to-zone-id "${ZONE_ID}" \
  --from-zone-id "${BIZ_ZONE_ID}" \
  --amount "${MINT_AMOUNT}"

npx hardhat checkExchange --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_1}" \
  --to-zone-id "${BIZ_ZONE_ID}" \
  --from-zone-id "${ZONE_ID}" \
  --amount "${LIMIT_AMOUNT}"

npx hardhat checkExchange --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_1}" \
  --to-zone-id "${BIZ_ZONE_ID}" \
  --from-zone-id "${ZONE_ID}" \
  --amount "${DAILY_LIMIT_AMOUNT}"

npx hardhat checkSyncAccount --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_2}" \
  --valid-id "${VALID_ID}" \
  --zone-id "${BIZ_ZONE_ID}" \
  --account-status "${STATUS_TERMINATING}"  

npx hardhat setBizZoneTerminated --network "${NETWORK}" \
    --account-id "${ACCOUNT_ID_2}" \
    --zone-id "${BIZ_ZONE_ID}"

npx hardhat checkSyncAccount --network "${NETWORK}" \
  --account-id "${ACCOUNT_ID_2}" \
  --valid-id "${VALID_ID}" \
  --zone-id "${BIZ_ZONE_ID}" \
  --account-status "${STATUS_TERMINATING}"  

popd > /dev/null
