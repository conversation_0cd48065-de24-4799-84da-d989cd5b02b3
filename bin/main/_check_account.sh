#!/bin/bash
# 
# description
# リリース後にEscrowアカウントが登録されているか確認するシェルです。
# sh ./bin/main/_check_account.sh {EscrowアカウントID} {バリデーターID}
# 前提条件 
# _2_port_forward.shが実施されていること
# 実行例 
# sh ./bin/main/_check_set_address.sh /XXXXXX/XXXXXX/XXXXXX/dcbg-dcjpy-contract/deployments/mainFin
ROOT_DIR=$(
    cd $(dirname "${BASH_SOURCE[0]}")/../.. || exit
    pwd
)
BIN_DIR=$(cd $(dirname ${BASH_SOURCE[0]}); pwd)
source "${BIN_DIR}"/_load_env.sh "$1"

account_id=$1
valid_id=$2

pushd ${ROOT_DIR} > /dev/null
output=$(npx hardhat getAccountAll --network mainFin \
    --account-id "${account_id}" \
    --valid-id "${valid_id}")
popd > /dev/null
echo "OK：Escrowアカウントが登録されていることを確認します。"
if [[ $output != *"accountStatus  : active"* ]]; then
    echo "OK：Escrowアカウントが登録されていることを確認しました。"
else
    echo "NG：Escrowアカウントが登録されていません。"
fi