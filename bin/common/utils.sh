#!/bin/bash
# ANSIエスケープコードの簡略化
blue=$(tput setaf 4)    # 青
yellow=$(tput setaf 3)  # 黄色
red=$(tput setaf 1)     # 赤
green=$(tput setaf 2)   # 緑
magenta=$(tput setaf 5) # 紫
bold=$(tput bold)       # 太字
reset=$(tput sgr0)      # リセット

export TERM=xterm-256color
export PARENT_SCRIPT=$(basename "$(ps -o args= -p $PPID)" 2>/dev/null)

# 呼び出し元スクリプトのディレクトリを取得。直接呼び出しは呼び出し元のディレクトリを取得。
export BASE_DIR=$(
  cd "$(dirname "${BASH_SOURCE[1]}")" || exit
  pwd
)
# gitリポジトリのルートディレクトリを取得
DIR="$BASE_DIR"
while [ "$DIR" != "/" ]; do
  if [ -d "$DIR/.git" ]; then
    export REPO_DIR="$DIR"
    break
  fi
  DIR=$(dirname "$DIR")
done

print_double_line_title() {
  local title="$1"

  # 引数の改行文字を実際の改行として処理
  title=$(echo -e "$title")

  # 最長行の長さを取得
  local max_length=0
  while IFS= read -r line; do
    local line_length=${#line}
    if ((line_length > max_length)); then
      max_length=$line_length
    fi
  done <<<"$title"

  local length=$((max_length + 4))
  local top_border=$(printf '\xE2\x95\x90%.0s' $(seq 1 $length)) # ═ (U+2550)
  local side_border='\xE2\x95\x91'                               # ║ (U+2551)

  echo
  echo -e "${blue}\xE2\x95\x94$top_border\xE2\x95\x97${reset}" # 青い枠線 (╔ and ╗)

  # 各行を出力
  while IFS= read -r line; do
    printf "${blue}$side_border${reset}  ${bold}%-${max_length}s${reset}  ${blue}$side_border${reset}\n" "$line"
  done <<<"$title"

  echo -e "${blue}\xE2\x95\x9A$top_border\xE2\x95\x9D${reset}" # 青い枠線 (╚ and ╝)
  echo
}

message() {\
  case $1 in
  "i" | "info")
    # ✔️ (U+2714) Heavy Check Mark
    echo -e "\xE2\x9C\x94\xEF\xB8\x8F ${blue}${bold}$2${reset}"
    ;;
  "w" | "warn")
    # 🚧 (U+1F6A7) Construction
    echo -e "\xF0\x9F\x9A\xA7 ${yellow}${bold}$2${reset}"
    ;;
  "e" | "err")
    # 🔥 (U+1F525) Fire
    echo -e "\xF0\x9F\x94\xA5 ${red}${bold}$2${reset}"
    ;;
  "s" | "success")
    # ✅ (U+2705) Check Mark Button
    echo -e "\xE2\x9C\x85 ${green}${bold}$2${reset}"
    ;;
  "q" | "question")
    # 💭 (U+1F4AD) Thought Balloon
    echo -e "\xF0\x9F\x92\xAD ${magenta}${bold}$2${reset}"
    ;;
  esac
}

menu() {
  choice=0
  local TITLE=$1
  shift 1
  local menu=($@)
  tail=$(expr ${#menu[@]} - 1)
  printf "\e[32m$TITLE\e[m\n" >&2
  for _ in $(seq 0 "$tail"); do echo ""; done
  while true; do
    printf "\e[${#menu[@]}A\e[m" >&2
    for i in $(seq 0 "$tail"); do
      if [ $choice = "$i" ]; then
        printf "\e[1;31m>\e[m \e[1;4m" >&2
      else
        printf "  " >&2
      fi
      printf "${menu[$i]}\e[m\n" >&2
    done

    read -srn1 key
    if [ "$key" == $'\x1b' ]; then read -srn2 key; fi

    case $key in
    "j" | $'\x5b\x42')
      if [ "$choice" -lt "$tail" ]; then choice=$(expr $choice + 1); fi
      ;;
    "k" | $'\x5b\x41')
      if [ "$choice" -gt 0 ]; then choice=$(expr "$choice" - 1); fi
      ;;
    "")
      CHOICE="${menu[$choice]}"
      return
      ;;
    esac
  done
}
choice() {
  read -p "$(message "info" "$1 (y/N):")" yn
  case "$yn" in [yY]*) ;; *)
    message "e" "canceled."
    exit
    ;;
  esac
}
