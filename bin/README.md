# ./bin

## 概要

本ディレクトリには、コントラクトのデプロイと特定シナリオを実施するスクリプトが設置されている。

## 構成

### ディレクトリ

直下のディレクトリは、hardhat.config.tsの`networks` に対応。  
スクリプトは`NETWORK環境変数をhardhatの`--network`オプションに指定するよう作っている。

#### `main`ディレクトリ

本番環境及び、ローカルIT向けのコントラクトデプロイ用のスクリプトが格納されている。envディレクトリにmigration対象の環境情報が入っている事を確認して実行する必要がある。

* `release.sh`: releaseに纏わる一連の流れ(_1_generate_env 〜 _7_upload_abi_files)を行なうシェル
* `_load_env.sh`: .env環境設定
* `_load_aws_prof.sh`: local実行用のaws profile環境設定
* `_1_generate_env.sh`: デプロイに必要なenvファイルを作成
* `_1a_generate_kms_key.sh`: デプロイ用のKMSキーを作成し、KeyIDを読み込む。すでに同一のエイリアスで生成されている場合は、既存のKeyIDを取得する
* `_2_port_forward.sh`: BESUのインスタンスIDを取得し、ポートフォワードを行なう
* `_3_migrate.sh`: コントラクトをデプロイする
* `_4_deployed_confirmation.sh`: デプロイされたコントラクトのバージョン(v1など)を取得する
* `_5_disconnect_port_forward.sh`: ポートフォワードを切断する
* `_6_backup_abi_files.sh`: デプロイに使用した環境ファイルをS3にバックアップする。
* `_6a_restore_api_files.sh`: バックアップ済みのデプロイ環境ファイルをローカルに復元する。
* `_7_upload_abi_files.sh`: 他コンポーネントが利用するABI.JSONをS3にアップロードする
* `_8_upload_fin_abi_to_biz.sh`: 他コンポーネントがBizZoneで利用するABI.JSONをFinZoneからコピーする。

以下はCodeBuild用に用意したシェル。

* `release_codebuild.sh`: CodeBuildを利用したreleaseに纏わる一連の流れを行なうシェル(プライベートIPから直接デプロイするため、ポートフォワード関連のシェルは実施していない)

> ローカル環境で `main` スクリプトを実行する場合は、ローカルIT環境を実行しておく必要がある。
> 手順書：https://decurret.atlassian.net/wiki/spaces/DIG/pages/3074654285/IT#IBC%E3%82%92%E8%B5%B7%E5%8B%95%E3%81%99%E3%82%8B

#### `local`ディレクトリ

スマートコントラクト開発者がEthereumクライアントを起動させてデプロイすることを想定している。  
2~18については業務に沿った特定のシナリオを実行するシェルとして用意されている。

`all_scenario.sh`を実行することで、Fin/Bizの初期データ構築から下記スクリプト1~18までをまとめて実行でき、2面環境での初期データ構築からRelayerを跨いだ一連の処理を実施できる。

* `_load_env.sh`: 環境設定ファイル
* `all_scenario.sh`: 下記スクリプトの2~18を実行する
* `1_migrate.sh`: デプロイ
* `2_reg_prov.sh`: Provider登録
* `3_reg_issuer.sh`: Issuer登録
* `4_reg_valid.sh`: Validator登録
* `5_reg_token.sh`: Token登録
* `6_reg_account.sh`: Account登録
* `7_mint_token.sh`: Mintを行なう
* `8_migrate_biz.sh`: BizZone向けデプロイ
* `9_mint_renewable.sh`: NFT(RenewableEnergyToken)をmintする
* `10_dvp_renewable.sh`: NFT(RenewableEnergyToken)とTokenのDvP移転を行う
* `11_reg_bizzone.sh`: FinZoneにBizZone情報の登録を行う
* `12_synchronous.sh`: BizZone利用申し込みを行う(Relayer利用)
* `13_industry.sh`: BizZone利用確定を行う
* `14_charge.sh`: BizZoneへのチャージを行う(Relayer利用)
* `15_syncTransfer.sh`: BizZone内送金を行う(Relayer利用)
* `16_discharge.sh`: FinZoneへのディスチャージを行う(Relayer利用)
* `17_biz_terminating.sh`: BizZone解約申し込みを行う(Relayer利用)
* `18_biz_terminatied.sh`: BizZone解約確定を行う

