const BaseChecker = require("solhint/lib/rules/base-checker");

const ruleId = "interface-no-struct";
const meta = {
  type: "best-practises",

  docs: {
    description: "",
    category: "",
  },

  isDefault: true,
  recommended: true,
  defaultSetup: ["error"],
  schema: null,
};

class InterfaceNoStruct extends BaseChecker {
  constructor(reporter) {
    super(reporter, ruleId, meta);
  }

  ContractDefinition(node) {
    this._findStructDefinition(node);
  }

  _findStructDefinition(node) {
    const children = node.subNodes;
    if (children.length === 0) {
      return;
    }
    children.forEach((child) => {
      if (child.type === "StructDefinition" && node.kind === "interface") {
        this.error(node, "struct in interface");
      }
    });
  }
}

module.exports = InterfaceNoStruct;
