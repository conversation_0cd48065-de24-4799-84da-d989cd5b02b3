const { forEach } = require("lodash");
const BaseChecker = require("solhint/lib/rules/base-checker");

const ruleId = "no-comment-format";
const meta = {
  type: "best-practises",

  docs: {
    description: "The comment of contract is not the correct format.",
    category: "syntax",
  },

  isDefault: true,
  recommended: true,
  defaultSetup: ["error"],
  schema: null,
};
const commentPattern = /\/\*\*|\*[^/]|\*\//;

class NoCommentFormat extends BaseChecker {
  constructor(reporter, _config, inputSrc) {
    super(reporter, ruleId, meta);
    this.inputSrc = inputSrc;
    this.comments = new Array();
  }

  SourceUnit(node) {
    const lineBreakPattern = /\r\n|[\r\n\u2028\u2029]/u;
    const lines = this.inputSrc.split(lineBreakPattern);
    this.comments = lines
      .map((line, index) => ({ line, index }))
      .filter(({ line }) => commentPattern.test(line));
  }

  FunctionDefinition(node) {
    this._findNodes(node);
  }

  _findNodes(node) {
    const start = node.loc.start.line;
    const item = this.comments.findIndex((item) => item.index === start - 2);
    if (this.comments[item] === undefined) {
      this.error(node, "Missing comment");
    }
  }
}

module.exports = NoCommentFormat;
