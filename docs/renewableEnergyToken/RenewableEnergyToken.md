# Solidity API

## RenewableEnergyToken

### initialize

```solidity
function initialize(address contractManager, contract ITransferable token) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | address | ContractManagerアドレス |
| token | contract ITransferable |  |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### mint

```solidity
function mint(bytes32 tokenId, bytes32 metadataId, bytes32 metadataHash, bytes32 mintAccountId, bytes32 ownerAccountId, bool isLocked, bytes32 traceId) external
```

_トークンを発行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |
| metadataId | bytes32 | メタデータID |
| metadataHash | bytes32 | メタデータハッシュ |
| mintAccountId | bytes32 | ミントアカウントID |
| ownerAccountId | bytes32 | オーナーアカウントID |
| isLocked | bool | ロック状態 |
| traceId | bytes32 | トレースID |

### transfer

```solidity
function transfer(bytes32 fromAccountId, bytes32 toAccountId, bytes32 tokenId, bytes32 traceId) external
```

_トークンを移転する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| fromAccountId | bytes32 | 移転元アカウントID |
| toAccountId | bytes32 | 移転先アカウントID |
| tokenId | bytes32 | トークンID |
| traceId | bytes32 | トレースID |

### customTransfer

```solidity
function customTransfer(bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes32 miscValue1, bytes32 miscValue2, string memo, bytes32 traceId) external returns (bool result)
```

_トークンを移転する(カスタムトランスファー)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| sendAccountId | bytes32 | SendAccount |
| fromAccountId | bytes32 | FromAccount |
| toAccountId | bytes32 | toAccount |
| amount | uint256 | 金額 |
| miscValue1 | bytes32 | カスタムコントラクト用パラメータ1 |
| miscValue2 | bytes32 | カスタムコントラクト用パラメータ2 |
| memo | string |  |
| traceId | bytes32 | トレースID |

### setRenewableEnergyTokenAll

```solidity
function setRenewableEnergyTokenAll(struct RenewableEnergyTokenAll[] renewableEnergytokens, uint256 deadline, bytes signature) external
```

_指定されたtokenIdに紐づくToken情報を登録、もしくは上書きする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergytokens | struct RenewableEnergyTokenAll[] | Tokenの情報 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### setTokenIdsByAccountIdAll

```solidity
function setTokenIdsByAccountIdAll(struct TokenIdsByAccountIdAll[] tokenIdsByAccountId, uint256 deadline, bytes signature) external
```

_指定されたaccountIdに紐づくTokenIdを登録、もしくは上書きする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenIdsByAccountId | struct TokenIdsByAccountIdAll[] | accountIdに紐づくTokenId |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### checkTransaction

```solidity
function checkTransaction(bytes32 validatorId, bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, bytes32 tokenId) external view returns (bool success, string err)
```

_トークンの移転チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 |  |
| sendAccountId | bytes32 |  |
| fromAccountId | bytes32 | 移転元アカウントID |
| toAccountId | bytes32 | 移転先アカウントID |
| tokenId | bytes32 | トークンID |

### getTokenList

```solidity
function getTokenList(bytes32 validatorId, bytes32 accountId, uint256 offset, uint256 limit, string sortOrder) external view returns (struct RenewableEnergyTokenListData[] renewableEnergyTokenList, uint256 totalCount, string err)
```

_アカウントに紐づくトークンの一覧を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 |  |
| accountId | bytes32 | アカウントID |
| offset | uint256 | offset |
| limit | uint256 | limit |
| sortOrder | string | sortOrder(true: 降順, false: 昇順) |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenList | struct RenewableEnergyTokenListData[] | トークンデータ一覧 |
| totalCount | uint256 |  |
| err | string |  |

### getToken

```solidity
function getToken(bytes32 tokenId) external view returns (struct RenewableEnergyTokenData renewableEnergyTokenData, string err)
```

_トークンの詳細情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenData | struct RenewableEnergyTokenData | トークンデータ |
| err | string |  |

### hasToken

```solidity
function hasToken(bytes32 tokenId, bytes32 accountId) external view returns (bool success, string err)
```

_引数のアカウントIDが、引数のトークンIDのNFTを所有しているか確認する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | トークンID |
| accountId | bytes32 | アカウントID |

### getTokenCount

```solidity
function getTokenCount() external view returns (uint256 count)
```

_Tokenの数を返却する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |

### getRenewableEnergyTokenAll

```solidity
function getRenewableEnergyTokenAll(uint256 offset, uint256 limit, uint256 deadline, bytes signature) external view returns (struct RenewableEnergyTokenAll[] renewableEnergyToken, uint256 totalCount, string err)
```

_RenewableEnergyToken全情報取得
     既に登録されているRenewableEnergyToken全情報取得を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| offset | uint256 | オフセット |
| limit | uint256 | 取得件数 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyToken | struct RenewableEnergyTokenAll[] | 全Tokenの情報 |
| totalCount | uint256 | Token数 |
| err | string | エラー |

### getTokenIdsByAccountIdAll

```solidity
function getTokenIdsByAccountIdAll(bytes32[] accountIds, uint256 deadline, bytes signature) external view returns (struct TokenIdsByAccountIdAll[] tokenIdsByAccountId, string err)
```

_指定されたaccountIdに紐づくTokenIdを取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountIds | bytes32[] | accountIds |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

