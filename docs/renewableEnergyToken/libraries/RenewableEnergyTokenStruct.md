# Solidity API

## RenewableEnergyTokenData

```solidity
struct RenewableEnergyTokenData {
  enum TokenStatus tokenStatus;
  bytes32 metadataId;
  bytes32 metadataHash;
  bytes32 mintAccountId;
  bytes32 ownerAccountId;
  bytes32 previousAccountId;
  bool isLocked;
}
```

## RenewableEnergyTokenListData

```solidity
struct RenewableEnergyTokenListData {
  bytes32 tokenId;
  enum TokenStatus tokenStatus;
  bytes32 metadataId;
  bytes32 metadataHash;
  bytes32 mintAccountId;
  bytes32 ownerAccountId;
  bytes32 previousAccountId;
  bool isLocked;
}
```

## RenewableEnergyTokenAll

```solidity
struct RenewableEnergyTokenAll {
  bytes32 tokenId;
  struct RenewableEnergyTokenData renewableEnergyTokenData;
}
```

## TokenIdsByAccountIdAll

```solidity
struct TokenIdsByAccountIdAll {
  bytes32 accountId;
  bytes32[] tokenIds;
}
```

## TokenStatus

```solidity
enum TokenStatus {
  Empty,
  Active,
  Inactive,
  Cancelled,
  Frozen,
  Pending,
  Minted,
  Burned
}
```

