# Solidity API

## RenewableEnergyTokenLib

### getTokenList

```solidity
function getTokenList(mapping(bytes32 => struct RenewableEnergyTokenData) renewableEnergyTokenData, bytes32[] tokenIds, bytes32 accountId, uint256 offset, uint256 limit) external view returns (struct RenewableEnergyTokenListData[] tokenDataList, uint256, string)
```

_renewableEnergyTokenDataMappingのマッピング内から、指定されたaccountIdがownerAccountId、mintAccountIdとなっているトークンのリストを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenData | mapping(bytes32 &#x3D;&gt; struct RenewableEnergyTokenData) | renewableEnergyTokenDataMapping |
| tokenIds | bytes32[] | 全トークンIDのリスト |
| accountId | bytes32 | 取得対象のアカウントID |
| offset | uint256 | 取得開始位置 |
| limit | uint256 | 取得件数 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenDataList | struct RenewableEnergyTokenListData[] | 取得したトークンのリスト |
| [1] | uint256 | totalCount 取得対象のトークン数 |
| [2] | string | err エラーメッセージ |

### getToken

```solidity
function getToken(mapping(bytes32 => struct RenewableEnergyTokenData) renewableEnergyTokenDataMapping, bytes32 key) external view returns (struct RenewableEnergyTokenData renewableEnergyTokenData, string err)
```

_トークンIDの詳細情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenDataMapping | mapping(bytes32 &#x3D;&gt; struct RenewableEnergyTokenData) | 再生可能エネルギートークンのデータを保存するマッピング |
| key | bytes32 | キーとなるトークンID |

### exists

```solidity
function exists(mapping(bytes32 => struct RenewableEnergyTokenData) data, bytes32 tokenId) public view returns (bool)
```

_トークンが存在するかどうかを確認する関数_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| data | mapping(bytes32 &#x3D;&gt; struct RenewableEnergyTokenData) | 再生可能エネルギートークンのデータを保存するマッピング |
| tokenId | bytes32 | 確認するトークンのID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | bool トークンが存在する場合はtrue、存在しない場合はfalseを返す |

### addTokenId

```solidity
function addTokenId(bytes32[] tokenIds, bytes32 tokenId) external
```

### addToken

```solidity
function addToken(mapping(bytes32 => struct RenewableEnergyTokenData) renewableEnergyTokenDataMapping, bytes32 key, bytes32 metadataId, bytes32 metadataHash, bytes32 mintAccountId, bytes32 ownerAccountId, bool isLocked) external
```

_トークンを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenDataMapping | mapping(bytes32 &#x3D;&gt; struct RenewableEnergyTokenData) | 再生可能エネルギートークンのデータを保存するマッピング |
| key | bytes32 | キーとなるトークンID |
| metadataId | bytes32 | メタデータID |
| metadataHash | bytes32 | メタデータハッシュ |
| mintAccountId | bytes32 | 発行するアカウントID |
| ownerAccountId | bytes32 | 所有者のアカウントID |
| isLocked | bool | トークンのロック状態 |

### addTokenIdToAccountId

```solidity
function addTokenIdToAccountId(mapping(bytes32 => bytes32[]) tokenIdsByAccountIdMapping, bytes32 accountId, bytes32 tokenId) external
```

_AccountIdに紐づくトークンIDを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenIdsByAccountIdMapping | mapping(bytes32 &#x3D;&gt; bytes32[]) | 追加するトークンIDを保存するマッピング |
| accountId | bytes32 | キーとなるアカウントID |
| tokenId | bytes32 | 追加するトークンID |

### transferToken

```solidity
function transferToken(mapping(bytes32 => struct RenewableEnergyTokenData) renewableEnergyTokenDataMapping, mapping(bytes32 => bytes32[]) tokenIdsByAccountIdMapping, bytes32 tokenId, bytes32 fromAccountId, bytes32 toAccountId) external
```

_トークンを移転する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenDataMapping | mapping(bytes32 &#x3D;&gt; struct RenewableEnergyTokenData) | 再生可能エネルギートークンのデータを保存するマッピング |
| tokenIdsByAccountIdMapping | mapping(bytes32 &#x3D;&gt; bytes32[]) |  |
| tokenId | bytes32 | キーとなるトークンID |
| fromAccountId | bytes32 | 移転もとのアカウントID |
| toAccountId | bytes32 | 移転先のアカウントID |

### checkTransaction

```solidity
function checkTransaction(contract IContractManager contractManager, mapping(bytes32 => struct RenewableEnergyTokenData) renewableEnergyTokenData, bytes32 validatorId, bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, bytes32 tokenId) external view returns (bool success, string err)
```

_トークンが移転可能かどうかチェックする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | コントラクトマネージャーアドレス |
| renewableEnergyTokenData | mapping(bytes32 &#x3D;&gt; struct RenewableEnergyTokenData) | 再生可能エネルギートークンのデータを保存するマッピング |
| validatorId | bytes32 | validatorId |
| sendAccountId | bytes32 | 送信者のアカウントID |
| fromAccountId | bytes32 | 移転元のアカウントID |
| toAccountId | bytes32 | 移転先のアカウントID |
| tokenId | bytes32 | トークンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:OK,false:NG |
| err | string | エラーメッセージ |

### setRenewableEnergyTokenAll

```solidity
function setRenewableEnergyTokenAll(mapping(bytes32 => struct RenewableEnergyTokenData) renewableEnergyTokenDataMapping, bytes32[] tokenIdsArray, struct RenewableEnergyTokenAll[] tokens) external
```

_指定されたtokenIdに紐づくToken情報を登録、もしくは上書きする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenDataMapping | mapping(bytes32 &#x3D;&gt; struct RenewableEnergyTokenData) | Tokenデータを保存するマッピング |
| tokenIdsArray | bytes32[] | TokenIDを保存するArray |
| tokens | struct RenewableEnergyTokenAll[] | Token情報 |

### getRenewableEnergyTokenAll

```solidity
function getRenewableEnergyTokenAll(mapping(bytes32 => struct RenewableEnergyTokenData) renewableEnergyTokenDataMapping, bytes32[] tokenIdsArray, uint256 size, uint256 index) external view returns (struct RenewableEnergyTokenAll[] renewableEnergyToken, uint256 totalCount, string err)
```

_指定されたTokenIdに紐づくToken情報を登録、もしくは上書きする
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| renewableEnergyTokenDataMapping | mapping(bytes32 &#x3D;&gt; struct RenewableEnergyTokenData) | Tokenデータを保存するマッピング |
| tokenIdsArray | bytes32[] | TokenIDを保存するArray |
| size | uint256 | 取得tokens件数 |
| index | uint256 | 取得tokensのindex |

### getTokenIdsByAccountIdAll

```solidity
function getTokenIdsByAccountIdAll(mapping(bytes32 => bytes32[]) tokenIdsByAccountIdMapping, bytes32[] accountIds, bool has, string _err) external view returns (struct TokenIdsByAccountIdAll[] tokenIdsByAccountId, string err)
```

_指定されたaccountIdに紐づくTokenIdを取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenIdsByAccountIdMapping | mapping(bytes32 &#x3D;&gt; bytes32[]) | accountIdに紐づくTokenIdマッピング |
| accountIds | bytes32[] | accountIds |
| has | bool | 署名確認結果 |
| _err | string | 署名確認結果エラー |

### setTokenIdsByAccountIdAll

```solidity
function setTokenIdsByAccountIdAll(mapping(bytes32 => bytes32[]) tokenIdsByAccountIdMapping, struct TokenIdsByAccountIdAll[] tokenIdsByAccountId) external
```

_指定されたaccountIdに紐づくTokenIdを登録、もしくは上書きする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenIdsByAccountIdMapping | mapping(bytes32 &#x3D;&gt; bytes32[]) | accountIdに紐づくTokenIdのマッピング |
| tokenIdsByAccountId | struct TokenIdsByAccountIdAll[] | accountIdに紐づくTokenId |

