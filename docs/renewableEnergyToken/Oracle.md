# Solidity API

## Oracle

_Oracleコントラクト
　　　 カスタムコントラクトで使用したい外部データを管理するコントラクト_

### initialize

```solidity
function initialize() public
```

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### addOracle

```solidity
function addOracle(uint256 oracleId, address invoker) external
```

_Oracleの登録を行う。_

### deleteOracle

```solidity
function deleteOracle(uint256 oracleId) external
```

_Oracleの削除を行う。残高管理DLTにてValidatorの権限チェックを行った後に実行される。_

### set

```solidity
function set(uint256 oracleId, bytes32 key, bytes32 value) external
```

_OracleValueのセットを行う。値が変更された場合はtrueを返す。_

### setBatch

```solidity
function setBatch(uint256 oracleId, bytes32[] keys, bytes32[] values) external
```

_OracleValueを複数件セットを行う。値が変更された場合はtrueを返す。_

### get

```solidity
function get(uint256 oracleId, bytes32 key) external view returns (bytes32 value, string err)
```

_OracleValueを取得する。_

### getBatch

```solidity
function getBatch(uint256 oracleId, bytes32[] keys) external view returns (bytes32[] values, string err)
```

_OracleValueを複数件取得する。_

