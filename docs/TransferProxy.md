# Solidity API

## TransferProxy

### initialize

```solidity
function initialize(contract IContractManager contractManager, contract ITransferable token) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |
| token | contract ITransferable |  |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### customTransfer

```solidity
function customTransfer(bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes32 miscValue1, bytes32 miscValue2, string memo, bytes32 traceId) external
```

_カスタムコントラクトのTransferを実行する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| sendAccountId | bytes32 | SendAccount |
| fromAccountId | bytes32 | FromAccount |
| toAccountId | bytes32 | toAccount |
| amount | uint256 | 金額 |
| miscValue1 | bytes32 | カスタムコントラクト用パラメータ1 |
| miscValue2 | bytes32 | カスタムコントラクト用パラメータ2 |
| memo | string |  |
| traceId | bytes32 | トレースID |

### isRegistered

```solidity
function isRegistered(address rule) external view returns (bool result)
```

_Rule(Address)が登録されているか確認を行う_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| rule | address | 追加するRule(Address) |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| result | bool | 登録の有無 |

### addRule

```solidity
function addRule(address rule, uint256 position) external
```

_ルールを登録する。
        @param rule コントラクトアドレス
        @param position ルールを配列に追加する。
        その位置を配列の先頭からの位置で指定する。
        0 は先頭を意味する。_

### deleteRule

```solidity
function deleteRule(address rule) external
```

_ルールを削除する。
        @param rule 削除するルール_

### clearRule

```solidity
function clearRule() external
```

_全てのルールを削除する。_

### findAll

```solidity
function findAll() external view returns (address[] rules)
```

_全てのルールを取得_

