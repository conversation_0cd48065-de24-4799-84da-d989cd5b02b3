# Solidity API

## ContractManager

_ContractNameContractコントラクト_

### Contracts

```solidity
struct Contracts {
  contract IAccessCtrl ctrlAddress;
  contract IProvider providerAddress;
  contract IIssuer issuerAddress;
  contract IValidator validatorAddress;
  contract IAccount accountAddress;
  contract IFinancialZoneAccount financialZoneAccountAddress;
  contract IBusinessZoneAccount businessZoneAccountAddress;
  contract IToken tokenAddress;
  contract ITokenService tokenServiceAddress;
  contract IFinancialCheck financialCheckAddress;
  contract ITransferProxy transferProxyAddress;
}
```

### initialize

```solidity
function initialize() public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### setContracts

```solidity
function setContracts(struct ContractManager.Contracts contracts, uint256 deadline, bytes signature) external
```

_コントラクトを設定する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contracts | struct ContractManager.Contracts | contracts  ctrlAddress ctrlAddress  providerAddress providerAddress  issuerAddress issuerAddress  validatorAddress validatorAddress  accountAddress accountAddress  financialZoneAccountAddress financialZoneAccountAddress  businessZoneAccountAddress businessZoneAccountAddress  tokenAddress tokenAddress  financialCheckAddress financialCheckAddress  transferProxyAddress transferProxyAddress |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

### setIbcApp

```solidity
function setIbcApp(address ibcAppAddress, string ibcAppName, uint256 deadline, bytes signature) external
```

_IBCAPPのアドレスを設定する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ibcAppAddress | address | ibcAppAddress |
| ibcAppName | string |  |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

### accessCtrl

```solidity
function accessCtrl() external view returns (contract IAccessCtrl)
```

_accessCtrl取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract IAccessCtrl | accessCtrl accessCtrl |

### provider

```solidity
function provider() external view returns (contract IProvider)
```

_provider取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract IProvider | provider provider |

### account

```solidity
function account() external view returns (contract IAccount)
```

_account取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract IAccount | account account |

### financialZoneAccount

```solidity
function financialZoneAccount() external view returns (contract IFinancialZoneAccount)
```

_financialZoneAccount取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract IFinancialZoneAccount | financialZoneAccount financialZoneAccount |

### businessZoneAccount

```solidity
function businessZoneAccount() external view returns (contract IBusinessZoneAccount)
```

_businessZoneAccount取得_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract IBusinessZoneAccount | businessZoneAccount businessZoneAccount |

### validator

```solidity
function validator() external view returns (contract IValidator)
```

_validator取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract IValidator | validator validator |

### issuer

```solidity
function issuer() external view returns (contract IIssuer)
```

_issuer取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract IIssuer | issuer issuer |

### token

```solidity
function token() external view returns (contract IToken)
```

_token取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract IToken | token token |

### tokenService

```solidity
function tokenService() external view returns (contract ITokenService)
```

_tokenPrivateService取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract ITokenService | tokenPrivateService tokenPrivateService |

### financialCheck

```solidity
function financialCheck() external view returns (contract IFinancialCheck)
```

_FinancialCheck取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract IFinancialCheck | token token |

### transferProxy

```solidity
function transferProxy() external view returns (contract ITransferProxy)
```

_transferProxy取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract ITransferProxy | transferProxy transferProxy |

### balanceSyncBridge

```solidity
function balanceSyncBridge() external view returns (contract IBalanceSyncBridge)
```

_balanceSyncBridge取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | contract IBalanceSyncBridge | balanceSyncBridge balanceSyncBridge |

### ibcApp

```solidity
function ibcApp(string ibcAppName) external view returns (address)
```

_ibcApp取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | address | ibcApp ibcApp |

