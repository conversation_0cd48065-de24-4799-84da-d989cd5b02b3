# Solidity API

## Token

### initialize

```solidity
function initialize(contract IContractManager contractManager) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### addToken

```solidity
function addToken(bytes32 tokenId, bytes32 name, bytes32 symbol, bytes32 traceId) external
```

_Providerから呼ばれるaddToken。

```
emit event: AddToken()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenId |
| name | bytes32 | Tokenの名前 |
| symbol | bytes32 | symbol |
| traceId | bytes32 | トレースID |

### modToken

```solidity
function modToken(bytes32 tokenId, bytes32 name, bytes32 symbol, bytes32 traceId) external
```

_Tokenの修正。名前、simbol、peg通貨の種類を修正できる。

```
emit event: ModToken()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenId |
| name | bytes32 | Tokenの名前 |
| symbol | bytes32 | zoneId |
| traceId | bytes32 | トレースID |

### setTokenEnabled

```solidity
function setTokenEnabled(bytes32 providerId, bytes32 tokenId, bool enabled, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Tokenのステータスを変更する。

```
emit event: SetEnabledToken()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerID |
| tokenId | bytes32 | tokenId |
| enabled | bool | Tokenの有効性.true:有効,false:無効 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

### approve

```solidity
function approve(bytes32 validatorId, bytes32 ownerId, bytes32 spenderId, uint256 amount, bytes32 traceId, uint256 deadline, bytes signature) external
```

_送金許可。

```
emit event: Approval()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | ownerId |
| ownerId | bytes32 | ownerId |
| spenderId | bytes32 | spenderId |
| amount | uint256 | 送金数量 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

### mint

```solidity
function mint(bytes32 issuerId, bytes32 accountId, uint256 amount, bytes32 traceId, uint256 deadline, bytes signature) external
```

_発行。

```
emit event: Mint()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |
| amount | uint256 | Mintする数量 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

### burn

```solidity
function burn(bytes32 issuerId, bytes32 accountId, uint256 amount, bytes32 traceId, uint256 deadline, bytes signature) external
```

_償却。

```
emit event: Burn()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |
| amount | uint256 | Burnする数量 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

### burnCancel

```solidity
function burnCancel(bytes32 issuerId, bytes32 accountId, uint256 amount, uint256 blockTimestamp, bytes32 traceId, uint256 deadline, bytes signature) external
```

_償却取り消し。

```
emit event: BurnCancel()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |
| accountName | string | アカウント名 |
| amount | uint256 | Burnを取り消す数量 |
| blockTimestamp | uint256 | 取り消し対象のBurnの日時 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

### getAllowance

```solidity
function getAllowance(bytes32 validatorId, bytes32 ownerId, bytes32 spenderId) external view returns (uint256 allowance, uint256 approvedAt, string err)
```

_送金許可設定の取得 Callで呼ばれる時にValidatorの紐付き検証を行う。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | validatorId |
| ownerId | bytes32 | ownerId |
| spenderId | bytes32 | spenderId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| allowance | uint256 | 許容額 |
| approvedAt | uint256 | 許可日付 |
| err | string | エラーメッセージ |

### getAllowanceList

```solidity
function getAllowanceList(bytes32 ownerAccountId, uint256 offset, uint256 limit) external view returns (struct AccountApprovalAll[] approvalData, uint256 totalCount, string err)
```

_送金許可一覧照会 TODO:Core APIとのマッピング時に作成_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| ownerAccountId | bytes32 | 送金許可元ID |
| offset | uint256 | オフセット |
| limit | uint256 | リミット |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| approvalData | struct AccountApprovalAll[] | 送金許可設定一覧 |
| totalCount | uint256 | 総数 |
| err | string | エラーメッセージ |

### transferSingle

```solidity
function transferSingle(bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes32 miscValue1, bytes32 miscValue2, string memo, bytes32 traceId) external
```

_単数のTransferを行う。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| sendAccountId | bytes32 | sendAccountId |
| fromAccountId | bytes32 | fromAccountId |
| toAccountId | bytes32 | toAccountId |
| amount | uint256 | 送金額 |
| miscValue1 | bytes32 | カスタムコントラクト用パラメータ1 |
| miscValue2 | bytes32 | カスタムコントラクト用パラメータ2 |
| memo | string |  |
| traceId | bytes32 | トレースID |

### _transfer

```solidity
function _transfer(bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes32 miscValue1, bytes32 miscValue2, string memo, bytes32 traceId) internal
```

_Transfer本体(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| sendAccountId | bytes32 | sendAccountId |
| fromAccountId | bytes32 | fromAccountId |
| toAccountId | bytes32 | toAccountId |
| amount | uint256 | 送金額 |
| miscValue1 | bytes32 | カスタムコントラクト用パラメータ1 |
| miscValue2 | bytes32 | カスタムコントラクト用パラメータ2 |
| memo | string |  |
| traceId | bytes32 | トレースID |

### customTransfer

```solidity
function customTransfer(bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes32 miscValue1, bytes32 miscValue2, string memo, bytes32 traceId) external returns (bool result)
```

_カスタムコントラクトから呼び出す為のCustomTransfer。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| sendAccountId | bytes32 | sendAccountId |
| fromAccountId | bytes32 | fromAccountId |
| toAccountId | bytes32 | toAccountId |
| amount | uint256 | 送金額 |
| miscValue1 | bytes32 | カスタムコントラクト用パラメータ1 |
| miscValue2 | bytes32 | カスタムコントラクト用パラメータ2 |
| memo | string |  |
| traceId | bytes32 | トレースID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| result | bool | true:成功,false:失敗 |

### _localTransfer

```solidity
function _localTransfer(struct TransferData data, bytes32 traceId) internal
```

_送金(内部関数)。

```
emit event: Transfer()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| data | struct TransferData | TransferData |
| traceId | bytes32 |  |

### redeemVoucher

```solidity
function redeemVoucher(bytes32 accountId, uint256 amount, bytes32 traceId) external
```

_Voucherを償却する。

```
emit event: RedeemVoucher()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| amount | uint256 | 償却額 |
| traceId | bytes32 | トレースID |

### issueVoucher

```solidity
function issueVoucher(bytes32 accountId, uint256 amount, bytes32 traceId) external
```

_Voucherを発行する。

```
emit event: IssueVoucher()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |
| amount | uint256 | 発行額 |
| traceId | bytes32 | トレースID |

### hasToken

```solidity
function hasToken(bytes32 tokenId, bool chkEnabled) external view returns (bool success, string err)
```

_TokenId確認用。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenId |
| chkEnabled | bool | true:有効性確認を行う,false:有効性確認を行わない |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:Tokenが存在し,false:Tokenが存在しない |
| err | string | エラーメッセージ |

### getToken

```solidity
function getToken() external view returns (bytes32 tokenId, bytes32 name, bytes32 symbol, uint256 totalSupply, bool enabled, string err)
```

_Token情報の取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenId |
| name | bytes32 | token名 |
| symbol | bytes32 | symbol |
| totalSupply | uint256 | tokenの総供給量 |
| enabled | bool | ture:有効,false:無効 |
| err | string | エラーメッセージ |

### getBalanceList

```solidity
function getBalanceList(bytes32 accountId) external view returns (uint16[] zoneIds, string[] zoneNames, uint256[] balances, string[] accountNames, bytes32[] accountStatus, uint256 totalBalance, string err)
```

_ビジネスゾーンの全残高情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneIds | uint16[] | ビジネスゾーンID |
| zoneNames | string[] | ゾーン名 |
| balances | uint256[] | 残高 |
| accountNames | string[] | アカウント名 |
| accountStatus | bytes32[] | アカウントステータス |
| totalBalance | uint256 | 合計残高 |
| err | string | エラーメッセージ |

### _hasToken

```solidity
function _hasToken(bytes32 tokenId, bool chkEnabled) internal view returns (bool success, string err)
```

_Token有効性確認(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenId |
| chkEnabled | bool | true:有効性確認を行う,false:有効性確認を行わない |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:Tokenが有効,false:Tokenが無効 |
| err | string | エラーメッセージ |

### checkApprove

```solidity
function checkApprove(bytes32 validatorId, bytes32 ownerId, bytes32 spenderId, uint256 amount, bytes accountSignature, bytes info, uint256 deadline, bytes signature) external view returns (bool success, string err)
```

