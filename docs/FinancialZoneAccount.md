# Solidity API

## FinancialZoneAccount

_Account限度額情報管理コントラクト(FinZone用)_

### initialize

```solidity
function initialize(contract IContractManager contractManager) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | contractManager |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | version コントラクトバージョン |

### addAccountLimit

```solidity
function addAccountLimit(bytes32 accountId, uint256[] limitAmounts, bytes32 traceId) external
```

^

_アカウント限度額追加 TODO:CoreAPIマッピングとの整合性確認時に作成_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| limitAmounts | uint256[] | 限度額設定値一覧 |
| traceId | bytes32 | トレースID |

### modAccountLimit

```solidity
function modAccountLimit(bytes32 accountId, bool[] itemFlgs, uint256[] limitAmounts) external returns (uint256[])
```

_アカウント限度額更新(Issuerコントラクト側で判定処理は完了している前提)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| itemFlgs | bool[] | 更新フラグ |
| limitAmounts | uint256[] |  |

### cumulativeReset

```solidity
function cumulativeReset(bytes32 accountId) external
```

_Account累積限度額初期化(Issuerコントラクト側で判定処理は完了している前提)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |

### syncCumulativeReset

```solidity
function syncCumulativeReset(bytes32 accountId, bytes32 traceId) external
```

_cumulative amount初期化_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| traceId | bytes32 | トレースID |

### addCumlativeAmount

```solidity
function addCumlativeAmount(bytes32 accountId, uint256 amount, bytes32 traceId) external returns (uint256 cumulativeDate, uint256 cumulativeAmount)
```

_累積限度額更新 TODO calcにするかaddにするか_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |
| traceId | bytes32 | トレースID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| cumulativeDate | uint256 | 累積限度額更新日時 |
| cumulativeAmount | uint256 | 累積限度額 |

### subtractCumulativeAmount

```solidity
function subtractCumulativeAmount(bytes32 accountId, uint256 amount, bytes32 traceId) external returns (uint256 cumulativeDate, uint256 cumulativeAmount)
```

_累積限度額減額_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |
| traceId | bytes32 | トレースID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| cumulativeDate | uint256 | 累積限度額更新日時 |
| cumulativeAmount | uint256 | 累積限度額 |

### syncMint

```solidity
function syncMint(bytes32 accountId, uint256 amount, bytes32 traceId) external
```

_mintされた額のamountを限度額に反映させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |
| traceId | bytes32 | トレースID |

### syncBurn

```solidity
function syncBurn(bytes32 accountId, uint256 amount, bytes32 traceId) external
```

_burnされた額のamountを限度額に反映させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |
| traceId | bytes32 | トレースID |

### syncCharge

```solidity
function syncCharge(bytes32 accountId, uint256 amount, bytes32 traceId) external
```

_chargeされた額のamountを限度額に反映させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |
| traceId | bytes32 | トレースID |

### syncTransfer

```solidity
function syncTransfer(bytes32 accountId, uint256 amount, bytes32 traceId) external
```

_tranferされた額のamountを限度額に反映させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |
| traceId | bytes32 | トレースID |

### setFinAccountsAll

```solidity
function setFinAccountsAll(struct FinancialZoneAccountsAll[] finAccounts, uint256 deadline, bytes signature) external
```

_指定されたAcountIdsに紐づくFinancialZoneAccounts情報を登録、もしくは上書きする
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| finAccounts | struct FinancialZoneAccountsAll[] | finAccountsInfo |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### hasAccount

```solidity
function hasAccount(bytes32 accountId) external pure returns (bool success, string err)
```

_限度額データ存在確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:OK, false:NG |
| err | string | エラーメッセージ |

### getAccountLimitData

```solidity
function getAccountLimitData(bytes32 accountId) external view returns (struct FinancialZoneAccountData accountLimitData, string err)
```

_アカウントの限度額を取得する(stack too deep回避のためreturnをオブジェクト化する)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitData | struct FinancialZoneAccountData |  |
| err | string |  |

### getJSTDay

```solidity
function getJSTDay() external view returns (uint256)
```

_UTCからJSTの0時へ変換_

### convertJSTDay

```solidity
function convertJSTDay(uint256 timestamp) external pure returns (uint256)
```

_タイムスタンプをJSTの0時へ変換_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| timestamp | uint256 | タイムスタンプ |

### checkCumulativeLimit

```solidity
function checkCumulativeLimit(bytes32 accountId, uint256 amount) external view returns (bool success, string err)
```

_1日の限度額チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:OK / false:NG |
| err | string | エラーメッセージ |

### checkMint

```solidity
function checkMint(bytes32 accountId, uint256 amount) external view returns (bool success, string err)
```

_発行限度額チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:OK / false:NG |
| err | string | エラーメッセージ |

### checkBurn

```solidity
function checkBurn(bytes32 accountId, uint256 amount) external view returns (bool success, string err)
```

_償却限度額チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:OK / false:NG |
| err | string | エラーメッセージ |

### checkCharge

```solidity
function checkCharge(bytes32 accountId, uint256 amount) external view returns (bool success, string err)
```

_チャージ/ディスチャージ限度額チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:OK / false:NG |
| err | string | エラーメッセージ |

### checkTransfer

```solidity
function checkTransfer(bytes32 accountId, uint256 amount) external view returns (bool success, string err)
```

_移転限度額チェック_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 金額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:OK / false:NG |
| err | string | エラーメッセージ |

### getFinAccountsAll

```solidity
function getFinAccountsAll(uint256 offset, uint256 limit, uint256 deadline, bytes signature) external view returns (struct FinancialZoneAccountsAll[] finAccounts, uint256 totalCount, string err)
```

_limitとoffsetで指定したFinancialZoneAccountsを一括取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| offset | uint256 | オフセット |
| limit | uint256 | 取得件数 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| finAccounts | struct FinancialZoneAccountsAll[] | 全FinancialZoneAccountsの情報 |
| totalCount | uint256 | 取得件数 |
| err | string | エラーメッセージ |

