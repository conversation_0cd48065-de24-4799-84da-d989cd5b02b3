# Solidity API

## IssuerLib

### getIssuerRole

```solidity
function getIssuerRole(mapping(bytes32 => struct IssuerData) issuerMapping, bytes32 key) external view returns (bytes32 role)
```

_発行者の権限を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerMapping | mapping(bytes32 &#x3D;&gt; struct IssuerData) | 発行者データを保存するマッピング |
| key | bytes32 | マッピングのキーとなる発行者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| role | bytes32 | 発行者IDに紐づく権限 |

### getIssuerId

```solidity
function getIssuerId(bytes32[] issuerIdMapping, uint256 key) external view returns (bytes32 issuerId, string err)
```

_発行者IDを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerIdMapping | bytes32[] | 発行者IDリストを保存するマッピング |
| key | uint256 | マッピングのキーとなる配列のindex |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | 発行者ID |
| err | string | エラーメッセージ |

### addIssuer

```solidity
function addIssuer(mapping(bytes32 => struct IssuerData) issuerMapping, bytes32 key, bytes32 role, string name, uint16 bankCode) external
```

_発行者データを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerMapping | mapping(bytes32 &#x3D;&gt; struct IssuerData) | 発行者データを保存するマッピング |
| key | bytes32 | マッピングのキーとなる発行者ID |
| role | bytes32 | 発行者の権限 |
| name | string | 発行者の名前 |
| bankCode | uint16 |  |

### addIssuerId

```solidity
function addIssuerId(bytes32[] issuerIdMapping, bytes32 key, bool issuerIdExistence) external
```

_発行者IDの追加_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerIdMapping | bytes32[] | 発行者データを保存するマッピング |
| key | bytes32 | 発行者ID |
| issuerIdExistence | bool | 発行者IDが登録済フラグ |

### addAccountId

```solidity
function addAccountId(mapping(bytes32 => struct IssuerData) issuerMapping, bytes32 key, bytes32 accountId) external
```

_アカウントIDを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerMapping | mapping(bytes32 &#x3D;&gt; struct IssuerData) | 発行者データを保存するマッピング |
| key | bytes32 | マッピングのキーとなる発行者ID |
| accountId | bytes32 | アカウントのID |

### modIssuer

```solidity
function modIssuer(mapping(bytes32 => struct IssuerData) issuerMapping, bytes32 key, string name) external
```

_発行者データを変更する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerMapping | mapping(bytes32 &#x3D;&gt; struct IssuerData) | 発行者データを保存するマッピング |
| key | bytes32 | マッピングのキーとなる発行者ID |
| name | string | 発行者の名前 |

### hasIssuer

```solidity
function hasIssuer(bytes32 issuerId, bool issuerIdExistence) external pure returns (bool success, string err)
```

_発行者の存在確認。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | チェック対象となる発行者Id |
| issuerIdExistence | bool | 発行者IDが登録済フラグ |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:存在する,false:存在しない |
| err | string | エラーメッセージ |

### hasAccount

```solidity
function hasAccount(bytes32 accountId, bool accountIdExistenceByIssuerId) internal pure returns (bool success, string err)
```

_指定されたIssuerIDにAccountが紐付いているか確認する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | チェック対象となるアカウントID |
| accountIdExistenceByIssuerId | bool | アカウントIDが発行者IDに紐付き済フラグ |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:紐づいている,false:紐づいていない |
| err | string | エラーメッセージ |

