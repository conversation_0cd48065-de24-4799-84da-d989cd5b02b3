# Solidity API

## ProviderData

```solidity
struct ProviderData {
  bytes32 role;
  bytes32 name;
  uint16 zoneId;
  bool enabled;
}
```

## ZoneData

```solidity
struct ZoneData {
  uint16 zoneId;
  string zoneName;
}
```

## ProviderAll

```solidity
struct ProviderAll {
  bytes32 providerId;
  bytes32 role;
  bytes32 name;
  uint16 zoneId;
  bool enabled;
  address providerEoa;
}
```

## AccountData

```solidity
struct AccountData {
  string accountName;
  bytes32 accountStatus;
  uint16[] zoneIds;
  uint256 balance;
  bytes32 reasonCode;
  uint256 appliedAt;
  uint256 registeredAt;
  uint256 terminatingAt;
  uint256 terminatedAt;
  bytes32 validatorId;
}
```

## AccountDataWithoutZoneId

```solidity
struct AccountDataWithoutZoneId {
  string accountName;
  bytes32 accountStatus;
  uint256 balance;
  bytes32 reasonCode;
  uint256 appliedAt;
  uint256 registeredAt;
  uint256 terminatingAt;
  uint256 terminatedAt;
}
```

## AccountDataWithLimitData

```solidity
struct AccountDataWithLimitData {
  string accountName;
  bytes32 accountStatus;
  uint256 balance;
  bytes32 reasonCode;
  uint256 appliedAt;
  uint256 registeredAt;
  uint256 terminatingAt;
  uint256 terminatedAt;
  uint256 mintLimit;
  uint256 burnLimit;
  uint256 chargeLimit;
  uint256 transferLimit;
  uint256 cumulativeLimit;
  uint256 cumulativeAmount;
  uint256 cumulativeDate;
}
```

## AccountDataAll

```solidity
struct AccountDataAll {
  string accountName;
  bytes32 accountStatus;
  uint256 balance;
  bytes32 reasonCode;
  uint16 zoneId;
  string zoneName;
  uint256 appliedAt;
  uint256 registeredAt;
  uint256 terminatingAt;
  uint256 terminatedAt;
  uint256 mintLimit;
  uint256 burnLimit;
  uint256 chargeLimit;
  uint256 transferLimit;
  uint256 cumulativeLimit;
  uint256 cumulativeAmount;
  uint256 cumulativeDate;
  struct BusinessZoneAccountDataWithZoneId[] businessZoneAccounts;
}
```

## FinancialZoneAccountData

```solidity
struct FinancialZoneAccountData {
  uint256 mintLimit;
  uint256 burnLimit;
  uint256 chargeLimit;
  uint256 transferLimit;
  uint256 cumulativeLimit;
  uint256 cumulativeAmount;
  uint256 cumulativeDate;
}
```

## FinancialZoneAccountsAll

```solidity
struct FinancialZoneAccountsAll {
  bytes32 accountId;
  struct FinancialZoneAccountData financialZoneAccountData;
}
```

## BusinessZoneAccountData

```solidity
struct BusinessZoneAccountData {
  string accountName;
  uint256 balance;
  bytes32 accountStatus;
  uint256 appliedAt;
  uint256 registeredAt;
  uint256 terminatingAt;
  uint256 terminatedAt;
}
```

## BusinessZoneAccountDataWithZoneId

```solidity
struct BusinessZoneAccountDataWithZoneId {
  string accountName;
  uint16 zoneId;
  string zoneName;
  uint256 balance;
  bytes32 accountStatus;
  uint256 appliedAt;
  uint256 registeredAt;
  uint256 terminatingAt;
  uint256 terminatedAt;
}
```

## BizAccountsAll

```solidity
struct BizAccountsAll {
  bytes32 accountId;
  struct BizAccountsByZoneId[] bizAccountsByZoneId;
}
```

## BizAccountsByZoneId

```solidity
struct BizAccountsByZoneId {
  uint16 zoneId;
  bool accountIdExistenceByZoneId;
  string accountName;
  uint256 balance;
  bytes32 accountStatus;
}
```

## AllowanceList

```solidity
struct AllowanceList {
  bytes32[] spender;
  mapping(bytes32 => struct AccountApproval) accountApprovalData;
}
```

## AccountApproval

```solidity
struct AccountApproval {
  string spenderAccountName;
  uint256 approvedAmount;
  uint256 approvedAt;
}
```

## AccountsAll

```solidity
struct AccountsAll {
  bytes32 accountId;
  string accountName;
  bytes32 accountStatus;
  uint16[] zoneIds;
  uint256 balance;
  bytes32 reasonCode;
  uint256 appliedAt;
  uint256 registeredAt;
  uint256 terminatingAt;
  uint256 terminatedAt;
  bool accountIdExistence;
  address accountEoa;
  struct AccountApprovalAll[] accountApprovalAll;
}
```

## AccountApprovalAll

```solidity
struct AccountApprovalAll {
  bytes32 spanderId;
  string spenderAccountName;
  uint256 allowanceAmount;
  uint256 approvedAt;
}
```

## IssuerData

```solidity
struct IssuerData {
  bytes32 role;
  string name;
  uint16 bankCode;
  bytes32[] accountIds;
}
```

## IssuerAccountsData

```solidity
struct IssuerAccountsData {
  bytes32 accountId;
  uint256 balance;
  bytes32 accountStatus;
  bytes32 reasonCode;
}
```

## IssuerListData

```solidity
struct IssuerListData {
  bytes32 issuerId;
  string name;
  uint16 bankCode;
}
```

## IssuersAll

```solidity
struct IssuersAll {
  bytes32 issuerId;
  bytes32 role;
  string name;
  uint16 bankCode;
  bool enabled;
  bool issuerIdExistence;
  address issuerEoa;
  struct IssuerAccountsExistence[] issuerAccountExistence;
}
```

## IssuerAccountsExistence

```solidity
struct IssuerAccountsExistence {
  bytes32 accountId;
  bool accountIdExistenceByIssuerId;
}
```

## ValidatorData

```solidity
struct ValidatorData {
  bytes32 name;
  bytes32[] accountIds;
  bytes32 issuerId;
  bytes32 role;
  bool enabled;
  bytes32 validatorAccountId;
}
```

## ValidatorAccountsData

```solidity
struct ValidatorAccountsData {
  bytes32 accountId;
  string accountName;
  uint256 balance;
  bytes32 accountStatus;
  bytes32 reasonCode;
  uint256 appliedAt;
  uint256 registeredAt;
  uint256 terminatingAt;
  uint256 terminatedAt;
}
```

## ValidatorListData

```solidity
struct ValidatorListData {
  bytes32 validatorId;
  bytes32 name;
  bytes32 issuerId;
}
```

## ValidatorsAll

```solidity
struct ValidatorsAll {
  bytes32 validatorId;
  bytes32 name;
  bytes32 issuerId;
  bytes32 role;
  bytes32 validatorAccountId;
  bool enabled;
  bool validatorIdExistence;
  bool issuerIdLinkedFlag;
  address validatorEoa;
  struct ValidatorAccountsExistence[] validAccountExistence;
}
```

## ValidatorAccountsExistence

```solidity
struct ValidatorAccountsExistence {
  bytes32 accountId;
  bool accountIdExistenceByValidatorId;
}
```

## TokenData

```solidity
struct TokenData {
  bytes32 name;
  bytes32 symbol;
  uint256 totalSupply;
  bool enabled;
}
```

## TokenAll

```solidity
struct TokenAll {
  bytes32 tokenId;
  bytes32 name;
  bytes32 symbol;
  uint256 totalSupply;
  bool enabled;
}
```

## TransferData

```solidity
struct TransferData {
  bytes32 transferType;
  uint16 zoneId;
  bytes32 fromValidatorId;
  bytes32 toValidatorId;
  uint256 fromAccountBalance;
  uint256 toAccountBalance;
  uint256 businessZoneBalance;
  uint16 bizZoneId;
  bytes32 sendAccountId;
  bytes32 fromAccountId;
  string fromAccountName;
  bytes32 toAccountId;
  string toAccountName;
  uint256 amount;
  bytes32 miscValue1;
  bytes32 miscValue2;
  string memo;
}
```

## BalanceByZone

```solidity
struct BalanceByZone {
  uint256 balance;
  bool enabled;
  uint256 stateCode;
}
```

## SyncBuisinessZoneBlanaceParams

```solidity
struct SyncBuisinessZoneBlanaceParams {
  bytes32 fromAccountId;
  string fromAccountName;
  bytes32 toAccountId;
  string toAccountName;
  uint16 fromZoneId;
  uint256 amount;
  bytes32 traceId;
}
```

## Constant

### _ACCOUNT_SYNC

```solidity
string _ACCOUNT_SYNC
```

_IBCApp名(AccountSyncBridge)_

### _JPY_TOKEN_TRANSFER

```solidity
string _JPY_TOKEN_TRANSFER
```

_IBCApp名(JPYTokenTransferBridge)_

### _BALANCE_SYNC

```solidity
string _BALANCE_SYNC
```

_IBCApp名(BalanceSyncBridge)_

### _TRANSFER

```solidity
bytes32 _TRANSFER
```

_通常transfer識別用文字列 *_

### _CUSTOM_TRANSFER

```solidity
bytes32 _CUSTOM_TRANSFER
```

_カスタムtransfer識別用文字列 *_

### _CHARGE

```solidity
bytes32 _CHARGE
```

_チャージ識別用文字列 *_

### _DISCHARGE

```solidity
bytes32 _DISCHARGE
```

_ディスチャージ識別用文字列 *_

### _STATUS_APPLIYNG

```solidity
bytes32 _STATUS_APPLIYNG
```

_AccountStatus判定用の定数(口座申し込み)_

### _STATUS_TERMINATING

```solidity
bytes32 _STATUS_TERMINATING
```

_AccountStatus判定用の定数(口座解約申し込み)_

### _STATUS_ACTIVE

```solidity
bytes32 _STATUS_ACTIVE
```

_バリデーション用のアカウントステータス値(アクティブ)_

### _STATUS_FROZEN

```solidity
bytes32 _STATUS_FROZEN
```

_バリデーション用のアカウントステータス値(凍結)_

### _STATUS_TERMINATED

```solidity
bytes32 _STATUS_TERMINATED
```

_バリデーション用のアカウントステータス値(解約済)_

### _FINANCIAL_ZONE

```solidity
uint16 _FINANCIAL_ZONE
```

_FinZoneのID_

### _EMPTY_VALUE

```solidity
bytes32 _EMPTY_VALUE
```

_未登録の場合にて返す空の値 *_

### _EMPTY_LENGTH

```solidity
uint256 _EMPTY_LENGTH
```

_未登録の場合にて返す空の件数 *_

### _EMPTY_UINT16

```solidity
uint16 _EMPTY_UINT16
```

### _MAX_ALLOWANCE_VALUE

```solidity
uint256 _MAX_ALLOWANCE_VALUE
```

_無制限の送金指示を許可する許可額の値_

