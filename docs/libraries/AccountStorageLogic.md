# Solidity API

## AccountLib

### getAccountData

```solidity
function getAccountData(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key, bool success, string errTmp) external view returns (struct AccountDataWithoutZoneId accountDataWithoutZoneId, string err)
```

_アカウント情報を取得する TODO: CoreAPIとのマッピング対応時に詳細作成_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象のアカウントデータが格納されているマッピング |
| key | bytes32 | マッピングのキーとなるアカウントID |
| success | bool |  |
| errTmp | string |  |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataWithoutZoneId | struct AccountDataWithoutZoneId | アカウントデータ(zoneIdなし) |
| err | string | エラーメッセージ |

### getAccountDataAll

```solidity
function getAccountDataAll(contract IContractManager _contractManager, mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 accountId) external view returns (struct AccountDataAll accountDataAll)
```

_アカウントの全情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| _contractManager | contract IContractManager | コントラクトマネージャーのアドレス |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) |  |
| accountId | bytes32 | アカウントID |

### getAccountIdExistence

```solidity
function getAccountIdExistence(mapping(bytes32 => bool) accountExistence, bytes32 key) external view returns (bool isExist)
```

_アカウントの存在情報登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountExistence | mapping(bytes32 &#x3D;&gt; bool) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| isExist | bool | アカウント存在状態 |

### getAllowance

```solidity
function getAllowance(mapping(bytes32 => struct AllowanceList) accountApprovalMapping, bytes32 key, bytes32 index) external view returns (uint256 allowance, uint256 approvedAt)
```

_アカウントの許可額を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountApprovalMapping | mapping(bytes32 &#x3D;&gt; struct AllowanceList) | 取得対象となるアカウントの許可額設定のマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| index | bytes32 | 許可対象のアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| allowance | uint256 |  |
| approvedAt | uint256 |  |

### getAllowanceList

```solidity
function getAllowanceList(mapping(bytes32 => struct AllowanceList) accountApprovalDataMapping, contract IContractManager contractManager, bytes32 ownerAccountId, uint256 offset, uint256 limit) external view returns (struct AccountApprovalAll[], uint256, string)
```

_アカウントの許可額リストを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountApprovalDataMapping | mapping(bytes32 &#x3D;&gt; struct AllowanceList) | 取得対象となるアカウントの許可額設定のマッピングデータ |
| contractManager | contract IContractManager | コントラクトマネージャ |
| ownerAccountId | bytes32 | マッピングのキーとなるアカウントID |
| offset | uint256 | offset |
| limit | uint256 | limit |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct AccountApprovalAll[] | allowanceList |
| [1] | uint256 | totalCount |
| [2] | string |  |

### getAccountBalance

```solidity
function getAccountBalance(mapping(bytes32 => struct AccountData) accountData, bytes32 key) external view returns (uint256 balance)
```

_アカウントの残高の取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountData | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| balance | uint256 |  |

### getAccountZoneIdList

```solidity
function getAccountZoneIdList(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key) external view returns (uint16[] zoneIdList)
```

_アカウントに連携済みのzoneIdの取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneIdList | uint16[] | アカウントに連携済みのzoneIdのリスト |

### getValidatorIdByAccountId

```solidity
function getValidatorIdByAccountId(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key) external view returns (bytes32 validatorId)
```

アカウントに連携されているvalidatorIdを取得

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 |  |

### isActivated

```solidity
function isActivated(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key) external view returns (bool success, string err)
```

_アカウントがアクティブかどうかを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool |  |
| err | string |  |

### isFrozen

```solidity
function isFrozen(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key) external view returns (bool frozen)
```

_アカウントが凍結状態かどうかを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

### forceBurn

```solidity
function forceBurn(mapping(bytes32 => struct AccountData) accountDataMapping, contract IContractManager contractManager, bytes32 accountId) external returns (uint256)
```

_アカウントを強制償却させる_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 償却対象となるアカウントのマッピングデータ |
| contractManager | contract IContractManager |  |
| accountId | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | burnedAmount 償却額 |
| [1] | string |  |

### isTerminated

```solidity
function isTerminated(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key) external view returns (bool terminated)
```

_アカウントが解約状態かどうかを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 取得対象となるアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| terminated | bool |  |

### addAccountData

```solidity
function addAccountData(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key, string accountName) external
```

_アカウントの登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| accountName | string | アカウント名 |

### addValidatorId

```solidity
function addValidatorId(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key, bytes32 validatorId) external
```

_アカウントに紐づくvalidatorIdの登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| validatorId | bytes32 | バリデータID |

### addAccountId

```solidity
function addAccountId(bytes32[] accountIdList, bytes32 key) external
```

_アカウントID登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountIdList | bytes32[] | アカウントIDの配列 |
| key | bytes32 | 追加対象のアカウントID |

### modAccount

```solidity
function modAccount(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key, string accountName) external
```

_アカウント名の変更_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| accountName | string | アカウント名 |

### addAccountIdExistence

```solidity
function addAccountIdExistence(mapping(bytes32 => bool) accountIdExistenceMapping, bytes32 key, bool isExist) external
```

_アカウント存在確認フラグ登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountIdExistenceMapping | mapping(bytes32 &#x3D;&gt; bool) | アカウント存在確認対象のマッピング |
| key | bytes32 | 追加対象のアカウントID |
| isExist | bool | true:存在 / false:削除 |

### setAccountStatus

```solidity
function setAccountStatus(mapping(bytes32 => struct AccountData) accountDataMapping, contract IContractManager contractManager, address msgSender, bytes32 key, bytes32 accountStatus, bytes32 reasonCode) external
```

_Accountの有効性を更新する(凍結 or アクティブ)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| contractManager | contract IContractManager | コントラクトマネージャー |
| msgSender | address |  |
| key | bytes32 | マッピングのキーとなるアカウントID |
| accountStatus | bytes32 | アカウントステータス |
| reasonCode | bytes32 | 理由コード |

### setApproval

```solidity
function setApproval(mapping(bytes32 => struct AllowanceList) accountApprovalMapping, bytes32 ownerId, bytes32 spenderId, string spenderName, uint256 approvedAt, uint256 amount) external
```

_アカウント許可額設定_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountApprovalMapping | mapping(bytes32 &#x3D;&gt; struct AllowanceList) | 登録先のアカウント許可額データマッピング |
| ownerId | bytes32 | マッピングのキーとなる所有者ID |
| spenderId | bytes32 | 支払い許可対象となるアカウントID |
| spenderName | string | 支払い許可対象となるアカウント名 |
| approvedAt | uint256 | 支払い許可日時 |
| amount | uint256 | 支払い許可額 |

### addZone

```solidity
function addZone(mapping(bytes32 => struct AccountData) accountDataMaping, bytes32 key, uint16 zoneId) external
```

_連携済みゾーン情報の追加_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMaping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| zoneId | uint16 | ゾーンID |

### setTerminated

```solidity
function setTerminated(mapping(bytes32 => struct AccountData) accountDataMaping, contract IContractManager contractManager, address msgSender, bytes32 key, bytes32 reasonCode) external
```

_アカウントのステータスを解約済みに更新する　TODO:他関数と統合する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMaping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| contractManager | contract IContractManager | コントラクトマネージャー |
| msgSender | address |  |
| key | bytes32 | マッピングのキーとなるアカウントID |
| reasonCode | bytes32 | 理由コード |

### setBalance

```solidity
function setBalance(mapping(bytes32 => struct AccountData) accountDataMapping, bytes32 key, uint256 amount, bool isAddition) external returns (uint256 balance)
```

_残高編集_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountDataMapping | mapping(bytes32 &#x3D;&gt; struct AccountData) | 登録先のアカウントのマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| amount | uint256 | 発行額 |
| isAddition | bool | true:加算 / false:減産 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| balance | uint256 | 発行後の残高 |

### setAllowance

```solidity
function setAllowance(mapping(bytes32 => struct AllowanceList) accountApprovalDataMapping, bytes32 key, bytes32 spenderId, uint256 amount) external
```

_送金許可額の減額を行う_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountApprovalDataMapping | mapping(bytes32 &#x3D;&gt; struct AllowanceList) | 登録先のアカウント送金許可額のマッピングデータ |
| key | bytes32 | マッピングのキーとなる所有者ID |
| spenderId | bytes32 | 送金許可対象者のID |
| amount | uint256 | 送金許可額 |

