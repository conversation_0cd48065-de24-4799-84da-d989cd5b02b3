# Solidity API

## FinancialZoneAccountLib

### getAccountLimitData

```solidity
function getAccountLimitData(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitMapping, bytes32 key) external view returns (struct FinancialZoneAccountData accountLimitData)
```

_アカウントの限度額を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 取得対象となるアカウント限度額設定のマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitData | struct FinancialZoneAccountData |  |

### getFinAccountsAll

```solidity
function getFinAccountsAll(mapping(bytes32 => struct FinancialZoneAccountData) finAccountDataMapping, contract IContractManager contractManager, uint256 length, uint256 size, uint256 index) external view returns (struct FinancialZoneAccountsAll[] finAccounts, uint256 totalCount, string err)
```

_limitとoffsetで指定したFinancialZoneAccountsを一括取得する
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| finAccountDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | アカウントデータを保存するマッピング |
| contractManager | contract IContractManager | コントラクトマネージャー |
| length | uint256 | 全アカウント数 |
| size | uint256 | 取得accounts件数 |
| index | uint256 | 取得accountsのindex |

### addAccountLimitData

```solidity
function addAccountLimitData(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, uint256[] limitAmounts) external
```

_アカウント限度額登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額のマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| limitAmounts | uint256[] | 登録対象の限度額データ |

### modAccountLimitData

```solidity
function modAccountLimitData(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, bool[] itemFlgs, uint256[] limitAmounts) external returns (uint256[])
```

_アカウント限度額登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額のマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| itemFlgs | bool[] |  |
| limitAmounts | uint256[] | 登録対象の限度額データ |

### resetCumulative

```solidity
function resetCumulative(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, uint256 jstDay) external
```

_アカウント限度額初期化_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額のマッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| jstDay | uint256 | 現在の日付 |

### addAmount

```solidity
function addAmount(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, uint256 amount, uint256 currentDay) external returns (uint256 cumulativeDate, uint256 cumulativeAmount)
```

_累積限度額の加算を行う TODO:減算処理と統合する？_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額マッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| amount | uint256 | 金額 |
| currentDay | uint256 | 現在の日付 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| cumulativeDate | uint256 |  |
| cumulativeAmount | uint256 |  |

### subtractAmount

```solidity
function subtractAmount(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, uint256 amount) external returns (uint256 cumulativeDate, uint256 cumulativeAmount)
```

_累積限度額の減算を行う_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額マッピングデータ |
| key | bytes32 | マッピングのキーとなるアカウントID |
| amount | uint256 | 金額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| cumulativeDate | uint256 |  |
| cumulativeAmount | uint256 |  |

### syncLimitAmount

```solidity
function syncLimitAmount(mapping(bytes32 => struct FinancialZoneAccountData) accountLimitDataMapping, bytes32 key, uint256 amount, uint256 currentDay) external
```

_操作額のamountを累積限度額に反映させる TODO:mintLimitの更新は本当に不要かどうか精査する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | 登録先のアカウント限度額マッピングデータ |
| key | bytes32 | アカウントID |
| amount | uint256 | 金額 |
| currentDay | uint256 | 現在の日付 |

### setFinAccountsAll

```solidity
function setFinAccountsAll(mapping(bytes32 => struct FinancialZoneAccountData) finAccountDataMapping, struct FinancialZoneAccountsAll[] finAccounts) external
```

_FinancialZoneAccountsを一括登録する
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| finAccountDataMapping | mapping(bytes32 &#x3D;&gt; struct FinancialZoneAccountData) | アカウントデータを保存するマッピング |
| finAccounts | struct FinancialZoneAccountsAll[] | 登録するFinancialZoneAccounts |

