# Solidity API

## TokenLib

### getToken

```solidity
function getToken(mapping(bytes32 => struct TokenData) tokenMapping, bytes32 key) external view returns (bytes32 name, bytes32 symbol, uint256 totalSupply, bool enabled, string err)
```

_Token情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenMapping | mapping(bytes32 &#x3D;&gt; struct TokenData) | トークンデータを保存するマッピング |
| key | bytes32 | マッピングのキーとなるトークンID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| name | bytes32 | トークンIDに紐づくトークンの名 |
| symbol | bytes32 | トークンIDに紐づくトークンのsymbol |
| totalSupply | uint256 | トークンIDに紐づくトークンの総供給量 |
| enabled | bool | トークンIDに紐づくture:有効,false:無効 |
| err | string | エラーメッセージ |

### getBalanceList

```solidity
function getBalanceList(contract IContractManager contractManager, bytes32 accountId, struct ZoneData[] zones) external view returns (uint16[] zoneIds, string[] zoneNames, uint256[] balances, string[] accountNames, bytes32[] accountStatus, uint256 totalBalance)
```

_ビジネスゾーンの全残高情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager |  |
| accountId | bytes32 | accountId |
| zones | struct ZoneData[] |  |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneIds | uint16[] | ビジネスゾーンID |
| zoneNames | string[] | ゾーン名 |
| balances | uint256[] | 残高 |
| accountNames | string[] | アカウント名 |
| accountStatus | bytes32[] | アカウントステータス |
| totalBalance | uint256 | 合計残高 |

### addToken

```solidity
function addToken(mapping(bytes32 => struct TokenData) tokenMapping, bytes32 tokenId, bytes32 storedTokenId, bytes32 name, bytes32 symbol) external
```

_トークンデータを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenMapping | mapping(bytes32 &#x3D;&gt; struct TokenData) | トークンデータを保存するマッピング |
| tokenId | bytes32 | マッピングのキーとなる追加対象のトークンID |
| storedTokenId | bytes32 | _tokenIdとして宣言されているトークンID |
| name | bytes32 | トークンの名前 |
| symbol | bytes32 | トークンのsymbol |

### addTotalSupply

```solidity
function addTotalSupply(mapping(bytes32 => struct TokenData) tokenMapping, bytes32 key, uint256 amount) external
```

_TotalSupplyを増額する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenMapping | mapping(bytes32 &#x3D;&gt; struct TokenData) | トークンデータを保存するマッピング |
| key | bytes32 | マッピングのキーとなるトークンID |
| amount | uint256 | Mintする数量 |

### modToken

```solidity
function modToken(mapping(bytes32 => struct TokenData) tokenMapping, bytes32 key, bytes32 tokenId, bytes32 name, bytes32 symbol) external
```

_トークンデータを変更する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenMapping | mapping(bytes32 &#x3D;&gt; struct TokenData) | トークンデータを保存するマッピング |
| key | bytes32 | マッピングのキーとなるトークンID |
| tokenId | bytes32 | トークンId |
| name | bytes32 | トークンの名前 |
| symbol | bytes32 | トークンのsymbol |

### setTokenEnabled

```solidity
function setTokenEnabled(mapping(bytes32 => struct TokenData) tokenMapping, bytes32 key, bytes32 tokenId, bool enabled) external
```

_Tokenのステータスを変更する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenMapping | mapping(bytes32 &#x3D;&gt; struct TokenData) | トークンデータを保存するマッピング |
| key | bytes32 | マッピングのキーとなるトークンID |
| tokenId | bytes32 | トークンId |
| enabled | bool | Tokenの有効性.true:有効,false:無効 |

### subTotalSupply

```solidity
function subTotalSupply(mapping(bytes32 => struct TokenData) tokenMapping, bytes32 key, uint256 amount) external
```

_TotalSupply減額_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenMapping | mapping(bytes32 &#x3D;&gt; struct TokenData) | トークンデータを保存するマッピング |
| key | bytes32 | マッピングのキーとなるトークンID |
| amount | uint256 | Burnする数量 |

### hasToken

```solidity
function hasToken(mapping(bytes32 => struct TokenData) tokenMapping, bytes32 tokenId, bytes32 chkTokenId, bool chkEnabled) internal view returns (bool success, string err)
```

_Token有効性確認。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenMapping | mapping(bytes32 &#x3D;&gt; struct TokenData) | トークンデータを保存するマッピング |
| tokenId | bytes32 | コントラクトが保存するトークンID |
| chkTokenId | bytes32 | 比較対象のトークンID |
| chkEnabled | bool | true:有効性確認を行う,false:有効性確認を行わない |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:Tokenが有効,false:Tokenが無効 |
| err | string | エラーメッセージ |

### mint

```solidity
function mint(contract IContractManager _contractManager, mapping(bytes32 => struct TokenData) tokenData, bytes32 tokenId, bytes32 issuerId, bytes32 accountId, uint256 amount, uint256 deadline, bytes signature) external returns (uint16 zoneId, bytes32 validatorId, string accountName, uint256 balance)
```

_発行_

### burn

```solidity
function burn(contract IContractManager _contractManager, mapping(bytes32 => struct TokenData) tokenData, bytes32 tokenId, bytes32 issuerId, bytes32 accountId, uint256 amount, uint256 deadline, bytes signature) external returns (uint16 zoneId, bytes32 validatorId, uint256 balance)
```

_償却_

### burnCancel

```solidity
function burnCancel(contract IContractManager _contractManager, mapping(bytes32 => struct TokenData) tokenData, bytes32 tokenId, bytes32 issuerId, bytes32 accountId, uint256 amount, uint256 blockTimestamp, uint256 deadline, bytes signature) external returns (uint16 zoneId, bytes32 validatorId, uint256 balance)
```

_償却取り消し_

### transfer

```solidity
function transfer(contract IContractManager _contractManager, struct TransferData data) external returns (struct TransferData)
```

_transferの実行_

### redeemVoucher

```solidity
function redeemVoucher(contract IContractManager _contractManager, mapping(bytes32 => struct TokenData) tokenData, address msgSender, bytes32 tokenId, bytes32 accountId, uint256 amount) external returns (uint16 zoneId, bytes32 validatorId, string accountName, uint256 balance)
```

_Voucherを償却する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| _contractManager | contract IContractManager | ContractManagerのアドレス |
| tokenData | mapping(bytes32 &#x3D;&gt; struct TokenData) | トークンデータを保存するマッピング |
| msgSender | address |  |
| tokenId | bytes32 | トークンID |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 償却額 |

### issueVoucher

```solidity
function issueVoucher(contract IContractManager _contractManager, mapping(bytes32 => struct TokenData) tokenData, address msgSender, bytes32 tokenId, bytes32 accountId, uint256 amount) external returns (uint16 zoneId, bytes32 validatorId, string accountName, uint256 balance)
```

_Voucherを発行する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| _contractManager | contract IContractManager | ContractManagerのアドレス |
| tokenData | mapping(bytes32 &#x3D;&gt; struct TokenData) | トークンデータを保存するマッピング |
| msgSender | address |  |
| tokenId | bytes32 | トークンID |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 発行額 |

