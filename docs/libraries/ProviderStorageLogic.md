# Solidity API

## ProviderLib

### getZone

```solidity
function getZone(mapping(bytes32 => struct ProviderData) providerMapping, mapping(uint16 => struct ZoneData) zoneDataMapping, bytes32 providerId) external view returns (uint16 zoneId, string zoneName, string err)
```

_ゾーン情報の取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerMapping | mapping(bytes32 &#x3D;&gt; struct ProviderData) | プロバイダデータを保存するマッピング |
| zoneDataMapping | mapping(uint16 &#x3D;&gt; struct ZoneData) | ゾーンデータを保存するマッピング |
| providerId | bytes32 | マッピングのキーとなるproviderId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | providerが属するZoneのId |
| zoneName | string | Zoneの名称 |
| err | string | エラーメッセージ |

### addProvider

```solidity
function addProvider(mapping(bytes32 => struct ProviderData) providerMapping, mapping(uint16 => struct ZoneData) zoneDataMapping, bytes32 providerId, bytes32 role, uint16 zoneId, string zoneName) external
```

_マッピングデータへのProviderDataの追加。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerMapping | mapping(bytes32 &#x3D;&gt; struct ProviderData) | プロバイダデータを保存するマッピング |
| zoneDataMapping | mapping(uint16 &#x3D;&gt; struct ZoneData) | ゾーンデータを保存するマッピング |
| providerId | bytes32 | マッピングのキーとなるproviderId |
| role | bytes32 | providerのロール |
| zoneId | uint16 | providerが属するZoneのId |
| zoneName | string | providerが属するZoneの名称 |

### modProviderName

```solidity
function modProviderName(mapping(bytes32 => struct ProviderData) providerMapping, bytes32 providerId, bytes32 name) external
```

_マッピングデータのproviderの名前の編集。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerMapping | mapping(bytes32 &#x3D;&gt; struct ProviderData) | プロバイダデータを保存するマッピング |
| providerId | bytes32 | マッピングのキーとなるproviderId |
| name | bytes32 | providerの名前 |

### modZoneName

```solidity
function modZoneName(mapping(bytes32 => struct ProviderData) providerMapping, mapping(uint16 => struct ZoneData) zoneDataMapping, bytes32 providerId, string zoneName) external
```

_Zone名の更新。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerMapping | mapping(bytes32 &#x3D;&gt; struct ProviderData) | プロバイダデータを保存するマッピング |
| zoneDataMapping | mapping(uint16 &#x3D;&gt; struct ZoneData) |  |
| providerId | bytes32 | マッピングのキーとなるproviderId |
| zoneName | string | 更新後のzone名 |

### hasProvider

```solidity
function hasProvider(bytes32 storedProviderId, bytes32 inputProviderId) external pure returns (bool success, string err)
```

_Providerの存在確認。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| storedProviderId | bytes32 | マッピングのキーとなるproviderId |
| inputProviderId | bytes32 | チェック対象のproviderId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:Providerが存在する,false:Providerが存在しない |
| err | string | エラーメッセージ |

### getProviderAll

```solidity
function getProviderAll(mapping(bytes32 => struct ProviderData) providerMapping, bytes32 providerId, address providerEoa) external view returns (struct ProviderAll provider)
```

_プロバイダ全情報取得
     既に登録されているプロバイダの全情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerMapping | mapping(bytes32 &#x3D;&gt; struct ProviderData) | プロバイダデータを保存するマッピング |
| providerId | bytes32 | プロバイダID |
| providerEoa | address | プロバイダ外部所有アカウント |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| provider | struct ProviderAll | 全プロバイダの情報 |

### setProviderAll

```solidity
function setProviderAll(mapping(bytes32 => struct ProviderData) providerMapping, struct ProviderAll provider) external
```

_プロバイダ全情報登録
     指定されたプロバイダIDに紐づくプロバイダ情報を登録、もしくは上書きする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerMapping | mapping(bytes32 &#x3D;&gt; struct ProviderData) | プロバイダデータを保存するマッピング |
| provider | struct ProviderAll | 登録データ |

