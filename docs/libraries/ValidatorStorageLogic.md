# Solidity API

## ValidatorLib

### getValidator

```solidity
function getValidator(mapping(bytes32 => struct ValidatorData) validatorMapping, contract IContractManager contractManager, bytes32 validatorId) external view returns (bytes32 name, bytes32 issuerId, string err)
```

_検証者データを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | 検証者データを保存するマッピング |
| contractManager | contract IContractManager | コントラクトマネージャアドレス |
| validatorId | bytes32 | マッピングのキーとなる検証者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| name | bytes32 | 検証者IDに紐づく検証者名 |
| issuerId | bytes32 | 検証者IDに紐づく発行者ID |
| err | string |  |

### getValidatorList

```solidity
function getValidatorList(mapping(bytes32 => struct ValidatorData) validatorMapping, bytes32[] validatorIds, uint256 limit, uint256 offset) external view returns (struct ValidatorListData[] validators, uint256 totalCount, string err)
```

_検証者データを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | 検証者データを保存するマッピング |
| validatorIds | bytes32[] | マッピングのキーとなる検証者ID |
| limit | uint256 | マッピングのキーとなる検証者ID |
| offset | uint256 | マッピングのキーとなる検証者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validators | struct ValidatorListData[] | 検証者IDに紐づく検証者名 |
| totalCount | uint256 | 検証者IDに紐づく発行者ID |
| err | string | 検証者IDに紐づく発行者ID |

### getAccountData

```solidity
function getAccountData(contract IContractManager contractManager, bytes32 validatorId, bytes32 accountId) external view returns (struct AccountDataWithLimitData accountData, string err)
```

### getValidatorAccountId

```solidity
function getValidatorAccountId(mapping(bytes32 => struct ValidatorData) validatorMapping, contract IContractManager contractManager, bytes32 validatorId) external view returns (bytes32 accountId, string err)
```

_バリデータが直接管理するアカウントIDを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | 検証者データを保存するマッピング |
| contractManager | contract IContractManager | コントラクトマネージャーアドレス |
| validatorId | bytes32 | バリデータID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| err | string | エラー |

### getAccountList

```solidity
function getAccountList(contract IContractManager contractManager, bytes32[] inAccountIds, uint256 offset, uint256 limit) external view returns (struct ValidatorAccountsData[] accounts, uint256 totalCount, string err)
```

_該当ValidatorIDに紐づくAccountの情報を取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager |  |
| inAccountIds | bytes32[] | 情報を取得したいaccountのIdの配列 |
| offset | uint256 |  |
| limit | uint256 |  |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accounts | struct ValidatorAccountsData[] |  |
| totalCount | uint256 |  |
| err | string | エラーメッセージ |

### getAccountIdList

```solidity
function getAccountIdList(mapping(bytes32 => struct ValidatorData) validatorMapping, bytes32 validatorId, string sortOrder) external view returns (bytes32[] accountIds)
```

_バリデータに紐づくAccountIdを返却する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | 検証者情報を保存するマッピング |
| validatorId | bytes32 | マッピングのキーとなる検証者ID |
| sortOrder | string | ソート順(desc: 降順, asc: 昇順) |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountIds | bytes32[] | アカウント情報の配列 |

### getValidatorRole

```solidity
function getValidatorRole(mapping(bytes32 => struct ValidatorData) validatorMapping, bytes32 validatorId) external view returns (bytes32 role)
```

_検証者の権限を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | 検証者データを保存するマッピング |
| validatorId | bytes32 | マッピングのキーとなる検証者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| role | bytes32 | 検証者IDに紐づく検証者の権限 |

### getValidatorIds

```solidity
function getValidatorIds(bytes32[] validatorIdMapping, uint256 key) external view returns (bytes32 validatorId)
```

_検証者IDを取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorIdMapping | bytes32[] | 検証者IDを保存するマッピング |
| key | uint256 | 配列のindex |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | 検証者ID |

### addValidator

```solidity
function addValidator(mapping(bytes32 => struct ValidatorData) validatorMapping, mapping(bytes32 => bool) validatorIdExistenceMapping, mapping(bytes32 => bool) issuerIdLinkedFlagMapping, contract IContractManager contractManager, bytes32 validatorId, bytes32 name, bytes32 issuerId) external
```

_検証者データを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | 検証者データを保存するマッピング |
| validatorIdExistenceMapping | mapping(bytes32 &#x3D;&gt; bool) | マッピングのキーとなる検証者ID |
| issuerIdLinkedFlagMapping | mapping(bytes32 &#x3D;&gt; bool) | 検証者の名前 |
| contractManager | contract IContractManager | 検証者ID |
| validatorId | bytes32 | 検証者ID |
| name | bytes32 | 検証者ID |
| issuerId | bytes32 | 検証者ID |

### addValidatorRole

```solidity
function addValidatorRole(mapping(bytes32 => struct ValidatorData) validatorMapping, bytes32 validatorId, bytes32 role, bool enabled) external
```

_検証者の権限を追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | 検証者データを保存するマッピング |
| validatorId | bytes32 | マッピングのキーとなる検証者ID |
| role | bytes32 | 検証者の権限 |
| enabled | bool | 検証者の有効性.true:有効,false:無効 |

### addAccount

```solidity
function addAccount(mapping(bytes32 => struct ValidatorData) validatorDataMapping, mapping(bytes32 => mapping(bytes32 => bool)) accountIdExistenceByValidatorId, contract IContractManager contractManager, bytes32 validatorId, bytes32 accountId, string accountName, uint256[] limitAmounts, bytes32 traceId) external
```

_Accountを登録する(共通領域)。

```
emit event: AddAccount()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorDataMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | validatorId |
| accountIdExistenceByValidatorId | mapping(bytes32 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; bool)) | accountId |
| contractManager | contract IContractManager | account名 |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |
| accountName | string | account名 |
| limitAmounts | uint256[] | 限度額の配列 |
| traceId | bytes32 | トレースID |

### addValidatorAccountId

```solidity
function addValidatorAccountId(mapping(bytes32 => struct ValidatorData) validatorMapping, mapping(bytes32 => mapping(bytes32 => bool)) accountIdExistenceByValidatorId, contract IContractManager contractManager, bytes32 validatorId, bytes32 accountId) external
```

_バリデータが直接管理するアカウントIDを追加する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | 検証者データを保存するマッピング |
| accountIdExistenceByValidatorId | mapping(bytes32 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; bool)) |  |
| contractManager | contract IContractManager | コントラクトマネージャーアドレス |
| validatorId | bytes32 | バリデータID |
| accountId | bytes32 | アカウントID |

### syncAccount

```solidity
function syncAccount(mapping(bytes32 => struct ValidatorData) validatorDataMapping, mapping(bytes32 => mapping(bytes32 => bool)) accountIdExistenceByValidatorId, contract IContractManager contractManager, bytes32 validatorId, bytes32 accountId, string accountName, bytes32 accountStatus, bytes32 reasonCode, bytes32 traceId) external
```

_Account登録（付加領域用)。

```
emit event: SyncAccount()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorDataMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | validatorId |
| accountIdExistenceByValidatorId | mapping(bytes32 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; bool)) | accountId |
| contractManager | contract IContractManager | account名 |
| validatorId | bytes32 | validatorId |
| accountId | bytes32 | accountId |
| accountName | string |  |
| accountStatus | bytes32 | 口座のステータス |
| reasonCode | bytes32 |  |
| traceId | bytes32 |  |

### modValidator

```solidity
function modValidator(mapping(bytes32 => struct ValidatorData) validatorMapping, bytes32 validatorId, bytes32 name) external
```

_検証者データを変更する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorMapping | mapping(bytes32 &#x3D;&gt; struct ValidatorData) | 検証者データを保存するマッピング |
| validatorId | bytes32 | マッピングのキーとなる検証者ID |
| name | bytes32 | 検証者の名前 |

### hasValidator

```solidity
function hasValidator(mapping(bytes32 => bool) validatorIdExistenceMapping, bytes32 validatorId) internal view returns (bool success, string err)
```

_検証者の存在確認。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorIdExistenceMapping | mapping(bytes32 &#x3D;&gt; bool) | 検証者データを保存するマッピング |
| validatorId | bytes32 | チェック対象となる検証者ID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:存在する,false:存在しない |
| err | string | エラーメッセージ |

### hasAccount

```solidity
function hasAccount(mapping(bytes32 => mapping(bytes32 => bool)) accountIdExistenceByValidatorId, bytes32 validatorId, bytes32 accountId) internal view returns (bool success, string err)
```

_指定されたValidatorIDにAccountが紐付いているか確認する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountIdExistenceByValidatorId | mapping(bytes32 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; bool)) | 検証者データを保存するマッピング |
| validatorId | bytes32 | チェック対象となるアカウントID |
| accountId | bytes32 | アカウントIDが検証者IDに紐付き済フラグ |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:紐づいている,false:紐づいていない |
| err | string | エラーメッセージ |

