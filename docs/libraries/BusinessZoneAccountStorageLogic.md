# Solidity API

## BusinessZoneAccountLib

### getBusinessZoneAccountData

```solidity
function getBusinessZoneAccountData(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) businessZoneaccountDataMapping, uint16 key, bytes32 accountId) external view returns (struct BusinessZoneAccountData)
```

_BizZoneのアカウント情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| businessZoneaccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) | 取得対象のBizZoneのアカウントデータが格納されているマッピング |
| key | uint16 | マッピングのキーとなゾーンID |
| accountId | bytes32 | マッピングのキーとなるアカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct BusinessZoneAccountData | businessZoneAccountData |

### getBizAccountsAll

```solidity
function getBizAccountsAll(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) bizAccountDataMapping, mapping(uint16 => mapping(bytes32 => bool)) accountIdExistenceByZoneIdMapping, contract IContractManager contractManager, uint256 length, uint256 size, uint256 index) external view returns (struct BizAccountsAll[] bizAccounts, uint256 totalCount, string err)
```

_limitとoffsetで指定したBuisinessZoneAccountsを一括取得する
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bizAccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) | アカウントデータを保存するマッピング |
| accountIdExistenceByZoneIdMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; bool)) | アカウント存在マッピング |
| contractManager | contract IContractManager | コントラクトマネージャー |
| length | uint256 | 全アカウント数 |
| size | uint256 | 取得accounts件数 |
| index | uint256 | 取得accountsのindex |

### setActivateAccount

```solidity
function setActivateAccount(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) bizZoneaccountDataMapping, uint16 key, bytes32 accountId) external
```

_ビジネスゾーンアカウントの登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bizZoneaccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) | 登録先のアカウントのマッピングデータ |
| key | uint16 | マッピングのキーとなるアカウントID |
| accountId | bytes32 | アカウントID |

### setBizZoneTerminated

```solidity
function setBizZoneTerminated(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) bizZoneaccountDataMapping, uint16 key, bytes32 accountId) external
```

_ビジネスゾーンアカウントの解約_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bizZoneaccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) | 登録先のアカウントのマッピングデータ |
| key | uint16 | マッピングのキーとなるアカウントID |
| accountId | bytes32 | アカウントID |

### syncBusinessZoneStatus

```solidity
function syncBusinessZoneStatus(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) businessZoneAccountDataMapping, mapping(uint16 => mapping(bytes32 => bool)) businessZoneAccountExistenceMapping, contract IContractManager contractMananger, uint16 zoneId, bytes32 accountId, string accountName, bytes32 accountStatus, bytes32 traceId) external
```

_ビジネスゾーンアカウントステータス更新(口座申し込み, 解約申し込み実行)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| businessZoneAccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) | 更新先のビジネスゾーンアカウントデータのマッピング |
| businessZoneAccountExistenceMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; bool)) |  |
| contractMananger | contract IContractManager |  |
| zoneId | uint16 | zoneId |
| accountId | bytes32 | アカウントID |
| accountName | string |  |
| accountStatus | bytes32 | アカウントステータス |
| traceId | bytes32 |  |

### syncBusinessZoneBalance

```solidity
function syncBusinessZoneBalance(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) businessZoneAccountDataMapping, uint16 zoneId, bytes32 toAccountId, bytes32 fromAccountId, uint256 balance) external
```

_ビジネスゾーンアカウントステータス更新_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| businessZoneAccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) | 更新先のビジネスゾーンアカウントデータのマッピング |
| zoneId | uint16 | zoneId |
| toAccountId | bytes32 | 送り先アカウントId |
| fromAccountId | bytes32 | 送り元アカウントID |
| balance | uint256 | アカウント残高 |

### addBusinessZoneBalance

```solidity
function addBusinessZoneBalance(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) businessZoneAccountDataMapping, uint16 zoneId, bytes32 accountId, uint256 amount) external
```

_ビジネスゾーンアカウント残高チャージ_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| businessZoneAccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) | 更新先のビジネスゾーンアカウントデータのマッピング |
| zoneId | uint16 | zoneId |
| accountId | bytes32 | アカウントId |
| amount | uint256 | チャージ額 |

### subtractBusinessZoneBalance

```solidity
function subtractBusinessZoneBalance(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) businessZoneAccountDataMapping, uint16 zoneId, bytes32 accountId, uint256 amount) external
```

_ビジネスゾーンアカウント残高ディスチャージ_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| businessZoneAccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) | 更新先のビジネスゾーンアカウントデータのマッピング |
| zoneId | uint16 | zoneId |
| accountId | bytes32 | アカウントId |
| amount | uint256 | ディスチャージ額 |

### balanceUpdateByRedeemVoucher

```solidity
function balanceUpdateByRedeemVoucher(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) businessZoneAccountDataMapping, uint16 key, bytes32 accountId, uint256 amount) external
```

_残高を更新(償却)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| businessZoneAccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) |  |
| key | uint16 | zoneId |
| accountId | bytes32 | accountId |
| amount | uint256 | Burnする数量 |

### balanceUpdateByIssueVoucher

```solidity
function balanceUpdateByIssueVoucher(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) businessZoneAccountDataMapping, uint16 key, bytes32 accountId, uint256 amount) external
```

_残高を更新(発行)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| businessZoneAccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) |  |
| key | uint16 | zoneId |
| accountId | bytes32 | accountId |
| amount | uint256 | Burnする数量 |

### balanceUpdateByForceBurn

```solidity
function balanceUpdateByForceBurn(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) businessZoneAccountDataMapping, uint16 key, bytes32 accountId) external returns (uint256 burnedAmount)
```

_対象のビジネスゾーンの残高を0にし、ステータスを"terminated"に更新する(強制償却)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| businessZoneAccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) | 更新先のビジネスゾーンアカウントデータのマッピング |
| key | uint16 | zoneId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| burnedAmount | uint256 | Burnした数量 |

### setBizAccountsAll

```solidity
function setBizAccountsAll(mapping(uint16 => mapping(bytes32 => struct BusinessZoneAccountData)) bizAccountDataMapping, mapping(uint16 => mapping(bytes32 => bool)) accountIdExistenceByZoneIdMapping, struct BizAccountsAll[] bizAccounts) external
```

_BusinessZoneAccountsを一括登録する
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bizAccountDataMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; struct BusinessZoneAccountData)) | アカウントデータを保存するマッピング |
| accountIdExistenceByZoneIdMapping | mapping(uint16 &#x3D;&gt; mapping(bytes32 &#x3D;&gt; bool)) | アカウント存在確認マッピング |
| bizAccounts | struct BizAccountsAll[] | 登録するBusinessZoneAccounts |

