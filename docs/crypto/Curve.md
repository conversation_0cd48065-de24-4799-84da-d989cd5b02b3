# Solidity API

## Curve

### onCurve

```solidity
function onCurve(uint256[2] P) external view returns (bool)
```

_Check whether the input point is on the curve.
SEC 1: 3.2.3.1_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| P | uint256[2] | The point. |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | True if the point is on the curve. |

### isPubKey

```solidity
function isPubKey(uint256[2] P) external view returns (bool)
```

_Check if the given point is a valid public key.
SEC 1: 3.2.2.1_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| P | uint256[2] | The point. |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | True if the point is valid public key |

### validateSignature

```solidity
function validateSignature(bytes32 h, uint256[2] rs, uint256[2] Q) external view returns (bool)
```

_Validate the signature 'rs' of 'h = H(message)' against the public key Q.
SEC 1: 4.1.4_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| h | bytes32 | The hash of the message. |
| rs | uint256[2] | The signature (r, s) |
| Q | uint256[2] | The public key to validate against. |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bool | True if the given signature is a valid signature. |

### compress

```solidity
function compress(uint256[2] P) external view returns (uint8 yBit, uint256 x)
```

_compress a point 'P = (Px, Py)' on the curve, giving 'C(P) = (yBit, Px)'
SEC 1: 2.3.3 - but only the curve-dependent code._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| P | uint256[2] | The point. |

### decompress

```solidity
function decompress(uint8 yBit, uint256 Px) external view returns (uint256[2] Q)
```

_decompress a point 'Px', giving 'Py' for 'P = (Px, Py)'
'yBit' is 1 if 'Qy' is odd, otherwise 0.
SEC 1: 2.3.4 - but only the curve-dependent code._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| yBit | uint8 | The compressed y-coordinate (One bit) |
| Px | uint256 | The x-coordinate. |

