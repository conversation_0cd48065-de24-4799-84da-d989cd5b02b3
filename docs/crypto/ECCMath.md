# Solidity API

## ECCMath

### invmod

```solidity
function invmod(uint256 _x, uint256 _pp) internal pure returns (uint256)
```

_Modular euclidean inverse of a number (mod p)._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| _x | uint256 | The number |
| _pp | uint256 | The modulus |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | uint256 | q such that x*q = 1 (mod _pp) |

### expmod

```solidity
function expmod(uint256 b, uint256 e, uint256 m) internal pure returns (uint256 r)
```

_Modular exponentiation, b^e % m
Basically the same as can be found here:
https://github.com/ethereum/serpent/blob/develop/examples/ecc/modexp.se_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| b | uint256 | The base. |
| e | uint256 | The exponent. |
| m | uint256 | The modulus. |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| r | uint256 | such that r = b**e (mod m) |

### toZ1

```solidity
function toZ1(uint256[3] P, uint256 zInv, uint256 z2Inv, uint256 prime) internal pure
```

_Converts a point (Px, Py, Pz) expressed in Jacobian coordinates to (Px', Py', 1).
Mutates P._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| P | uint256[3] | The point. |
| zInv | uint256 | The modular inverse of 'Pz'. |
| z2Inv | uint256 | The square of zInv |
| prime | uint256 | The prime modulus. |

### toZ1

```solidity
function toZ1(uint256[3] PJ, uint256 prime) internal pure
```

_See _toZ1(uint[3], uint, uint).
Warning: Computes a modular inverse._

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| PJ | uint256[3] | The point. |
| prime | uint256 | The prime modulus. |

