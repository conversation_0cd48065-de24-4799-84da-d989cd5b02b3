# Solidity API

## Secp256k1

### pp

```solidity
uint256 pp
```

### Gx

```solidity
uint256 Gx
```

### Gy

```solidity
uint256 Gy
```

### nn

```solidity
uint256 nn
```

### lowSmax

```solidity
uint256 lowSmax
```

### onCurve

```solidity
function onCurve(uint256[2] P) internal pure returns (bool)
```

_See Curve.onCurve_

### isPubKey

```solidity
function isPubKey(uint256[2] P) internal pure returns (bool isPK)
```

_See Curve.isPubKey_

### validateSignature

```solidity
function validateSignature(bytes32 message, uint256[2] rs, uint256[2] Q) internal pure returns (bool)
```

_See Curve.validateSignature_

### compress

```solidity
function compress(uint256[2] P) internal pure returns (uint8 yBit, uint256 x)
```

_See Curve.compress_

### decompress

```solidity
function decompress(uint8 yBit, uint256 x) internal pure returns (uint256[2] P)
```

_See Curve.decompress_

### _add

```solidity
function _add(uint256[3] P, uint256[3] Q) internal pure returns (uint256[3] R)
```

### _addMixed

```solidity
function _addMixed(uint256[3] P, uint256[2] Q) internal pure returns (uint256[3] R)
```

### _addMixedM

```solidity
function _addMixedM(uint256[3] P, uint256[2] Q) internal pure
```

### _double

```solidity
function _double(uint256[3] P) internal pure returns (uint256[3] Q)
```

### _doubleM

```solidity
function _doubleM(uint256[3] P) internal pure
```

### _mul

```solidity
function _mul(uint256 d, uint256[2] P) internal pure returns (uint256[3] Q)
```

