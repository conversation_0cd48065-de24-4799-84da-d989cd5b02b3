# Solidity API

## Provider

_Providerコントラクト_

### ROLE_PREFIX_PROV

```solidity
bytes32 ROLE_PREFIX_PROV
```

_Providerロール計算用(calcRole()prefix用文字列(Provider権限))_

### initialize

```solidity
function initialize(contract IContractManager contractManager) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### _adminOnly

```solidity
function _adminOnly(bytes32 hash, uint256 deadline, bytes signature) internal view
```

_Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 権限チェック対象の署名 |

### addProvider

```solidity
function addProvider(bytes32 providerId, uint16 zoneId, string zoneName, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Providerの追加。Adminの権限が必要。

```
emit event: AddProvider()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerID |
| zoneId | uint16 | zoneId |
| zoneName | string | zoneName |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### addBizZone

```solidity
function addBizZone(uint16 zoneId, string zoneName, uint256 deadline, bytes signature) external
```

_(Fin専用)Finで管理を行うZone情報の追加。Adminの権限が必要。

```
emit event: AddBizZone()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | zoneId |
| zoneName | string | zoneName |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### addProviderRole

```solidity
function addProviderRole(bytes32 providerId, address providerEoa, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Provider権限の追加。Adminの権限が必要。

```
emit event: AddProviderRole()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerID |
| providerEoa | address | providerEoa |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### modProvider

```solidity
function modProvider(bytes32 providerId, bytes32 name, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Provider名の更新。Adminの権限が必要。

```
emit event: ModProvider()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerID |
| name | bytes32 | 更新後のproviderの名前 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### modZone

```solidity
function modZone(bytes32 providerId, string zoneName, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Zone名の更新。Adminの権限が必要。

```
emit event: ModProvider()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerID |
| zoneName | string | 更新後のzone名 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### addToken

```solidity
function addToken(bytes32 providerId, bytes32 tokenId, bytes32 name, bytes32 symbol, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Tokenの追加。

```
emit event: AddTokenByProvider()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerID |
| tokenId | bytes32 | tokenId |
| name | bytes32 | トークン名 |
| symbol | bytes32 | symbol |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### modToken

```solidity
function modToken(bytes32 tokenId, bytes32 name, bytes32 symbol, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Tokenのステータス変更。name, symbolのみ変更を許可し、空を許容しない。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenId |
| name | bytes32 | トークン名 |
| symbol | bytes32 | symbol |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### setProviderAll

```solidity
function setProviderAll(struct ProviderAll provider, uint256 deadline, bytes signature) external
```

_プロバイダ全情報登録_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| provider | struct ProviderAll | 全プロバイダの情報 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### hasProvider

```solidity
function hasProvider(bytes32 providerId) external view returns (bool success, string err)
```

_Providerの存在確認。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:Providerが存在する,false:Providerが存在しない |
| err | string | エラーメッセージ |

### _hasProvider

```solidity
function _hasProvider(bytes32 providerId) internal view returns (bool success, string err)
```

_Providerの存在確認(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:Providerが存在する,false:Providerが存在しない |
| err | string | エラーメッセージ |

### hasToken

```solidity
function hasToken(bytes32 tokenId, bytes32 providerId, bool chkEnabled) external view returns (bool success, string err)
```

_Tokenの存在確認。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenId |
| providerId | bytes32 | providerId |
| chkEnabled | bool | true:有効性確認を行う,false:有効性確認を行わない |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:Tokenが存在し、有効,false:Tokenが存在しない、あるいは無効 |
| err | string | エラーメッセージ |

### _hasToken

```solidity
function _hasToken(bytes32 tokenId, bool chkEnabled) internal view returns (bool success, string err)
```

_Tokenの存在確認(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenId |
| chkEnabled | bool | true:有効性確認を行う,false:有効性確認を行わない |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:Tokenが存在し、有効,false:Tokenが存在しない、あるいは無効 |
| err | string | エラーメッセージ |

### getProvider

```solidity
function getProvider() external view returns (bytes32 providerId, uint16 zoneId, string zoneName, string err)
```

_Provider情報の取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerId |
| zoneId | uint16 | zoneId |
| zoneName | string | zoneName |
| err | string | エラーメッセージ |

### getZone

```solidity
function getZone() external view returns (uint16 zoneId, string zoneName, string err)
```

_zoneIDの取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | zoneID |
| zoneName | string | zoneName |
| err | string | エラーメッセージ |

### getZoneName

```solidity
function getZoneName(uint16 zoneId) external view returns (string zoneName)
```

_zone名称の取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneName | string | zoneName |

### getTokenId

```solidity
function getTokenId() external view returns (bytes32 tokenId, string err)
```

_TokenIDの取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenID |
| err | string | エラーメッセージ |

### getToken

```solidity
function getToken(bytes32 providerId) external view returns (bytes32 tokenId, bytes32 name, bytes32 symbol, uint256 totalSupply, bool enabled, string err)
```

_Token情報の取得。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| tokenId | bytes32 | tokenId |
| name | bytes32 | トークン名 |
| symbol | bytes32 | symbol |
| totalSupply | uint256 | 合計供給量 |
| enabled | bool | true:Tokenが有効, false:Tokenが無効 |
| err | string | エラーメッセージ |

### getProviderCount

```solidity
function getProviderCount() external view returns (uint256 count)
```

_Provider数の取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| count | uint256 | providerの数 |

### checkRole

```solidity
function checkRole(bytes32 providerId, bytes32 hash, uint256 deadline, bytes signature) external view returns (bool has, string err)
```

_権限の確認。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | providerID |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| has | bool | true:ロールを所持,false:ロールを不所持 |
| err | string | エラーメッセージ |

### getProviderAll

```solidity
function getProviderAll(uint256 deadline, bytes signature) external view returns (struct ProviderAll provider, string err)
```

_プロバイダ全情報取得
     既に登録されているプロバイダの全情報を取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| provider | struct ProviderAll | 全プロバイダの情報 |
| err | string | エラー |

