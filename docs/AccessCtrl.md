# Solidity API

## AccessCtrl

_AccessCtrlコントラクト_

### ROLE_PREFIX_PROV

```solidity
bytes32 ROLE_PREFIX_PROV
```

_Providerロール計算用(calcRole()prefix用文字列(Provider権限))_

### ROLE_PREFIX_ISSUER

```solidity
bytes32 ROLE_PREFIX_ISSUER
```

_Issuerロール計算用(calcRole()のprefix用文字列(Issuer権限))_

### ROLE_PREFIX_VALIDATOR

```solidity
bytes32 ROLE_PREFIX_VALIDATOR
```

_Validatorロール計算用(calcRole()のprefix用文字列(Validator権限))_

### ROLE_PREFIX_ACCOUNT

```solidity
bytes32 ROLE_PREFIX_ACCOUNT
```

_Validatorロール計算用(calcRole()のprefix用文字列(Validator権限))_

### initialize

```solidity
function initialize(contract IContractManager contractManager, address eoaAdmin) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |
| eoaAdmin | address | ADMIN権限を持たせるEOA |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### addAdminRole

```solidity
function addAdminRole(address eoaNew, uint256 deadline, bytes signature) external
```

_権限アドレス追加(Admin)。Adminの権限が必要。
```
emit event: RoleGranted()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| eoaNew | address | 権限を付与するEOA |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### addRole

```solidity
function addRole(bytes32 role, address eoaNew, uint256 deadline, bytes signature) external
```

_権限アドレス追加(Admin以外)。<br/>
     Admin以外の権限値はcalcRole()で計算した値を使用し、
     各Providerや各Issuerごとに権限値が用意される想定である。<br/>
     Adminの権限が必要。
```
emit event: RoleGranted()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| role | bytes32 | 権限 |
| eoaNew | address | 権限を付与するEOA |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### addRoleByProv

```solidity
function addRoleByProv(bytes32 providerId, bytes32 role, address eoaNew) external
```

_権限アドレス追加(Admin以外)(Providerコントラクト専用)。<br/>
     addRole()参照
```
emit event: RoleGranted()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | ProviderID |
| role | bytes32 | 権限 |
| eoaNew | address | 権限を付与するEOA |

### addRoleByIssuer

```solidity
function addRoleByIssuer(bytes32 issuerId, bytes32 role, address eoaNew) external
```

_権限アドレス追加(Admin以外)(Issuerコントラクト専用)。<br/>
     addRole()参照
```
emit event: RoleGranted()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | 発行者ID |
| role | bytes32 | 権限 |
| eoaNew | address | 権限を付与するEOA |

### addRoleByValidator

```solidity
function addRoleByValidator(bytes32 validatorId, bytes32 role, address eoaNew) external
```

_権限アドレス追加(Admin以外)(Validatorコントラクト専用)。<br/>
     addRole()参照
```
emit event: RoleGranted()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 |  |
| role | bytes32 | 権限 |
| eoaNew | address | 権限を付与するEOA |

### addAccountEoa

```solidity
function addAccountEoa(bytes32 accountId, address eoa) external
```

_AccountのEoaを追加_

### delAdminRole

```solidity
function delAdminRole(address eoaDel) external
```

@dev
Admin権限アドレス削除。<br/>
msg.senderがコントラクトをデプロイしたアドレスであること。<br/>
コントラクトをデプロイしたアドレスは削除できない。

```
emit event: RoleRevoked()
```

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| eoaDel | address | 権限を削除するEOA |

### delProviderRole

```solidity
function delProviderRole(bytes32 provID, address eoaDel) external
```

@dev
Provider権限アドレス削除。<br/>
msg.senderがコントラクトをデプロイしたアドレスであること。<br/>

```
emit event: RoleRevoked()
```

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| provID | bytes32 | プロバイダID |
| eoaDel | address | 権限を削除するEOA |

### delIssuerRole

```solidity
function delIssuerRole(bytes32 issuerID, address eoaDel) external
```

@dev
Issuer権限アドレス削除。<br/>
msg.senderがコントラクトをデプロイしたアドレスであること。<br/>

```
emit event: RoleRevoked()
```

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerID | bytes32 | 発行者ID |
| eoaDel | address | 権限を削除するEOA |

### delValidRole

```solidity
function delValidRole(bytes32 validID, address eoaDel) external
```

@dev
Validator権限アドレス削除。<br/>
msg.senderがコントラクトをデプロイしたアドレスであること。<br/>

```
emit event: RoleRevoked()
```

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validID | bytes32 | 検証者ID |
| eoaDel | address | 権限を削除するEOA |

### calcRole

```solidity
function calcRole(bytes32 prefix, bytes32 id) public pure returns (bytes32)
```

_権限値計算。権限(role)として大きくAdmin、Admin以外、無し、の3つのどれかに属する。
     Admin以外ではProvider, Issuerがあるが、権限の有効範囲は各Providerや各Issuerに
     属するものだけである。
     ここでは各Providerや各Issuer用の権限値を作成する。
     prefixを用意したのは、idが連番になると値が重複してしまうためである。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| prefix | bytes32 | ROLE_PREFIX_xxx()を想定(チェックはしない) |
| id | bytes32 | 権限を割り振るID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | bytes32 | 権限値 |

### checkAdminRole

```solidity
function checkAdminRole(bytes32 hash, uint256 deadline, bytes signature) external view returns (bool has, string err)
```

_権限チェック(Admin)。署名からEOAを復元し、Admin権限を持つかチェックする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 権限チェック対象の署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| has | bool | true:Admin権限あり |
| err | string | 空文字列では無い場合、エラー有り(その他の戻り値は無効) |

### checkRole

```solidity
function checkRole(bytes32 role, bytes32 hash, uint256 deadline, bytes signature) external view returns (bool has, string err)
```

_権限チェック(Admin以外)。署名からEOAを復元し、対象の権限を持つかチェックする。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| role | bytes32 | チェック対象の権限値 |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 権限チェック対象の署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| has | bool | true:該当の権限あり |
| err | string | 空文字列では無い場合、エラー有り(その他の戻り値は無効) |

### _checkRole

```solidity
function _checkRole(bytes32 role, bytes32 hash, uint256 deadline, bytes signature) internal view returns (bool has, string err)
```

_権限チェック(内部用)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| role | bytes32 | チェック対象の権限値 |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 権限チェック対象の署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| has | bool | true:該当の権限あり |
| err | string | 空文字列では無い場合、エラー有り(その他の戻り値は無効) |

### _recoverAddr

```solidity
function _recoverAddr(bytes32 hash, bytes signature) internal pure returns (address)
```

_署名からのアドレス復元(内部用)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| signature | bytes | 権限チェック対象の署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | address | 復元したアドレス |

### _pubRecover

```solidity
function _pubRecover(uint256 x, uint256 y) internal pure returns (address addr)
```

### _checkValidatorSig_deep

```solidity
function _checkValidatorSig_deep(bytes32 validatorId, uint256 pt, bytes pkc, bytes sigcpt) internal view returns (bool has, string err)
```

### checkSigAccount

```solidity
function checkSigAccount(bytes32 accountId, bytes32 hashAccount, bytes accountSignature, bytes info) external view returns (bool has, string err)
```

### eccAddRecover

```solidity
function eccAddRecover(uint256 pox, uint256 pkoSign, uint256 pcx, uint256 pkcSign) external view returns (address addr)
```

_ecAddRecover recover an address_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| pox | uint256 | Px of public key O |
| pkoSign | uint256 | compress flag of public key O |
| pcx | uint256 | Px of public key C |
| pkcSign | uint256 | Px compress flag of public key C |

### pp

```solidity
uint256 pp
```

### addPoint

```solidity
function addPoint(uint256[2] po, uint256[2] pc) internal pure returns (uint256, uint256)
```

### toUint256

```solidity
function toUint256(bytes _bytes, uint256 _start) internal pure returns (uint256)
```

### _checkAccountOneTime_deep

```solidity
function _checkAccountOneTime_deep(bytes32 accountId, bytes pko, bytes pkc) internal view returns (bool has, string err)
```

### _sigVerify

```solidity
function _sigVerify(bytes signature) internal pure returns (string err)
```

### _checkAccountSig_deep

```solidity
function _checkAccountSig_deep(bytes pko, bytes32 hashAccount, bytes accountSignature) internal view returns (bool has, string err)
```

### getProviderEoa

```solidity
function getProviderEoa(bytes32 providerId) public view returns (address)
```

_ProviderIDからEOAを逆引きする。Providerコントラクトのみ実行を許す。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| providerId | bytes32 | ProviderId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | address | providerEoa |

### getIssuerEoa

```solidity
function getIssuerEoa(bytes32 issuerId) public view returns (address)
```

_IssuerIDからEOAを逆引きする。Issuerコントラクトのみ実行を許す。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | IssuerId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | address | issuerEoa |

### getValidatorEoa

```solidity
function getValidatorEoa(bytes32 validatorId) public view returns (address)
```

_ValidatorIDからEOAを逆引きする。Validatorコントラクトのみ実行を許す。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| validatorId | bytes32 | ValidatorId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | address | validatorEoa |

### getAccountEoa

```solidity
function getAccountEoa(bytes32 accountId) public view returns (address)
```

_AccountIDからEOAを逆引きする。Accountコントラクトのみ実行を許す。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | AccountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | address | accountEoa |

