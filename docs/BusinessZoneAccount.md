# Solidity API

## BusinessZoneAccount

_BizinessZoneAccount情報管理コントラクト(FinZone用)_

### initialize

```solidity
function initialize(contract IContractManager contractManager) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | contractManager |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | version コントラクトバージョン |

### setActiveBusinessAccountWithZone

```solidity
function setActiveBusinessAccountWithZone(uint16 zoneId, bytes32 accountId, bytes32 traceId) external
```

_BusinessZoneアカウント追加(CoreAPI/industry用)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| accountId | bytes32 | アカウントID |
| traceId | bytes32 | トレースID |

### setBizZoneTerminated

```solidity
function setBizZoneTerminated(uint16 zoneId, bytes32 accountId) external
```

_BusinessZoneアカウント解約_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| accountId | bytes32 | アカウントID |

### syncBusinessZoneStatus

```solidity
function syncBusinessZoneStatus(uint16 zoneId, string zoneName, bytes32 accountId, string accountName, bytes32 accountStatus, bytes32 traceId) external
```

_ビジネスゾーンアカウントステータス更新申し込み(BizZoneアカウント申し込み含む)
```
emit event: SyncBusinessZoneStatus()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | zoneId |
| zoneName | string | zone名 |
| accountId | bytes32 | アカウントID |
| accountName | string |  |
| accountStatus | bytes32 | アカウントステータス |
| traceId | bytes32 | トレースID |

### syncBusinessZoneBalance

```solidity
function syncBusinessZoneBalance(struct SyncBuisinessZoneBlanaceParams params) external
```

_ビジネスゾーンアカウントステータス更新
```
emit event: SyncBusinessZoneBalance()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| params | struct SyncBuisinessZoneBlanaceParams | BizZone内送金の残高更新のデータ |

### addBusinessZoneBalance

```solidity
function addBusinessZoneBalance(uint16 zoneId, bytes32 accountId, uint256 amount) external
```

_ビジネスゾーン残高チャージ_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | zoneId |
| accountId | bytes32 | 送信先アカウントID |
| amount | uint256 | チャージ額 |

### subtractBusinessZoneBalance

```solidity
function subtractBusinessZoneBalance(uint16 zoneId, bytes32 accountId, uint256 amount) external
```

_ビジネスゾーン残高ディスチャージ_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | zoneId |
| accountId | bytes32 | 送信先アカウントID |
| amount | uint256 | チャージ額 |

### forceBurnAllBalance

```solidity
function forceBurnAllBalance(bytes32 accountId) external returns (uint256 burnedAmount)
```

_全てのビジネスゾーンの残高を強制償却する
ゾーンの一覧をAccountコントラクトから取得し、取得した各ゾーンごとに以下の処理を行う
各ゾーンの残高をbalanceUpdateByForceBurnを実行して0に更新する
対象ゾーンの残高が0の場合はスキップする_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| burnedAmount | uint256 | 償却した数量 |

### balanceUpdateByRedeemVoucher

```solidity
function balanceUpdateByRedeemVoucher(uint16 zoneId, bytes32 accountId, uint256 amount, bytes32 traceId) external
```

_残高を更新(償却)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | zoneId |
| accountId | bytes32 | accountId |
| amount | uint256 | Burnする数量 |
| traceId | bytes32 | トレースID |

### balanceUpdateByIssueVoucher

```solidity
function balanceUpdateByIssueVoucher(uint16 zoneId, bytes32 accountId, uint256 amount, bytes32 traceId) external
```

_残高を更新(発行)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | zoneId |
| accountId | bytes32 | accountId |
| amount | uint256 | Burnする数量 |
| traceId | bytes32 | トレースID |

### setBizAccountsAll

```solidity
function setBizAccountsAll(struct BizAccountsAll[] bizAccounts, uint256 deadline, bytes signature) external
```

_指定されたAcountIdsに紐づくBusinessZoneAccounts情報を登録、もしくは上書きする
     バックアップリスト作業時のみ実行_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| bizAccounts | struct BizAccountsAll[] | bizAccountsInfo |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### hasAccountByZone

```solidity
function hasAccountByZone(uint16 zoneId, bytes32 accountId) external view returns (bool success, string err)
```

_BizZoneのAccountId存在確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:存在する,false:存在しない |
| err | string | エラーメッセージ |

### _hasAccountByZone

```solidity
function _hasAccountByZone(uint16 zoneId, bytes32 accountId) internal view returns (bool success, string err)
```

_BizZoneのAccountId存在確認(内部関数)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:存在する,false:存在しない |
| err | string | エラーメッセージ |

### getBusinessZoneAccount

```solidity
function getBusinessZoneAccount(uint16 zoneId, bytes32 accountId) external view returns (struct BusinessZoneAccountData)
```

_BusinessZoneアカウント情報取得_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | struct BusinessZoneAccountData | businessZoneAccountData |

### isActivatedByZone

```solidity
function isActivatedByZone(uint16 zoneId, bytes32 accountId) external view returns (bool success, string err)
```

_BusinessZoneアカウントのアクティブ状態確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:アクティブ状態,false:アクティブ以外 |
| err | string |  |

### accountIdExistenceByZoneId

```solidity
function accountIdExistenceByZoneId(uint16 zoneId, bytes32 accountId) external view returns (bool success)
```

_BusinessZoneアカウントの存在確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | ゾーンID |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:アクティブ状態,false:アクティブ以外 |

### getBizAccountsAll

```solidity
function getBizAccountsAll(uint256 offset, uint256 limit, uint256 deadline, bytes signature) external view returns (struct BizAccountsAll[] bizAccounts, uint256 totalCount, string err)
```

_limitとoffsetで指定したBusinessZoneAccountsを一括取得する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| offset | uint256 | オフセット |
| limit | uint256 | 取得件数 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| bizAccounts | struct BizAccountsAll[] | 全BusinessZoneAccountsの情報 |
| totalCount | uint256 | 取得件数 |
| err | string | エラーメッセージ |

