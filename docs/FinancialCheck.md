# Solidity API

## FinancialCheck

### initialize

```solidity
function initialize(contract IContractManager contractManager) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | version コントラクトバージョン |

### checkTransaction

```solidity
function checkTransaction(uint16 zoneId, bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes accountSignature, bytes info) external view returns (bool success, string err)
```

FinancialZoneトランザクションをチェックする

_この関数はFinDLT上で実行されるためBizDZone実行時には参照先を意識して呼び出す必要がある_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| zoneId | uint16 | 処理ゾーンのID |
| sendAccountId | bytes32 | 送信元アカウントID |
| fromAccountId | bytes32 | 送金元アカウントID |
| toAccountId | bytes32 | 送金先アカウントID |
| amount | uint256 | 送金額 |
| accountSignature | bytes | アカウント署名 |
| info | bytes | 追加情報 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | トランザクションが成功したかどうか |
| err | string | エラーメッセージ |

### checkExchange

```solidity
function checkExchange(bytes32 accountId, uint16 fromZoneId, uint16 toZoneId, uint256 amount) external view returns (bool success, string err)
```

### checkSyncAccount

```solidity
function checkSyncAccount(bytes32 validatorId, bytes32 accountId, uint16 zoneId, bytes32 accountStatus, bytes accountSignature, bytes info) external view returns (bool success, string err)
```

### checkFinAccountStatus

```solidity
function checkFinAccountStatus(bytes32 accountId) external view returns (bytes32 accountStatus, string err)
```

### getAccountLimit

```solidity
function getAccountLimit(bytes32 accountId) external view returns (struct FinancialZoneAccountData accountLimitData, string err)
```

_BizZone側からFinZoneの限度額情報を参照するための関数(bizZoneのBCClientからのみ呼び出される)_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountLimitData | struct FinancialZoneAccountData | accountLimitData |
| err | string |  |

