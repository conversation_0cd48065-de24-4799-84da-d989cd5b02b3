# Solidity API

## Issuer

_Issuerコントラクト_

### ROLE_PREFIX_ISSUER

```solidity
bytes32 ROLE_PREFIX_ISSUER
```

_Issuerロール計算用(calcRole()のprefix用文字列(Issuer権限))_

### initialize

```solidity
function initialize(contract IContractManager contractManager) public
```

_Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| contractManager | contract IContractManager | ContractManagerアドレス |

### version

```solidity
function version() external pure returns (string)
```

_コントラクトバージョン取得。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| [0] | string | コントラクトバージョン |

### _adminOnly

```solidity
function _adminOnly(bytes32 hash, uint256 deadline, bytes signature) internal view
```

_Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 権限チェック対象の署名 |

### _issuerOnly

```solidity
function _issuerOnly(bytes32 issuerId, bytes32 hash, uint256 deadline, bytes signature) internal view
```

_Issuer権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 権限チェック対象の署名 |

### addIssuer

```solidity
function addIssuer(bytes32 issuerId, uint16 bankCode, string name, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Issuerの追加。Adminの権限が必要。

```
emit event: AddIssuer()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| bankCode | uint16 | 金融機関コード |
| name | string | issuer名 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### addAccountId

```solidity
function addAccountId(bytes32 issuerId, bytes32 accountId, bytes32 traceId) external
```

_issuerにaccountを紐付ける。

```
emit event: AddAccountByIssuer()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |
| traceId | bytes32 | トレースID |

### addIssuerRole

```solidity
function addIssuerRole(bytes32 issuerId, address issuerEoa, bytes32 traceId, uint256 deadline, bytes signature) external
```

_issuer権限の追加。

```
emit event: AddIssuerRole()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| issuerEoa | address | issuerEoa |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

### addAccountRole

```solidity
function addAccountRole(bytes32 issuerId, bytes32 accountId, address accountEoa, bytes32 traceId, uint256 deadline, bytes signature) external
```

_account権限の追加。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |
| accountEoa | address | accountEoa |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### setAccountStatus

```solidity
function setAccountStatus(bytes32 issuerId, bytes32 accountId, bytes32 accountStatus, bytes32 reasonCode, bytes32 traceId, uint256 deadline, bytes signature) external
```

_アカウントの状態を更新する
__

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |
| accountStatus | bytes32 | アカウントステータス |
| reasonCode | bytes32 | 理由コード |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### modIssuer

```solidity
function modIssuer(bytes32 issuerId, string name, bytes32 traceId, uint256 deadline, bytes signature) external
```

_issuer名の更新。

```
emit event: ModIssuer()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| name | string | issuer名 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### modTokenLimit

```solidity
function modTokenLimit(bytes32 issuerId, bytes32 accountId, bool[] itemFlgs, uint256[] limitAmounts, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Accountの限度額を更新する。

```
emit event: ModTokenLimit()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |
| itemFlgs | bool[] | true:限度額を更新する,false:限度額を更新しない |
| limitAmounts | uint256[] | 更新後の限度額 |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

### cumulativeReset

```solidity
function cumulativeReset(bytes32 issuerId, bytes32 accountId, bytes32 traceId, uint256 deadline, bytes signature) external
```

_Accountの累積限度額初期化。

```
emit event: CumulativeReset()
```_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |
| traceId | bytes32 | トレースID |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | 署名 |

### forceBurn

```solidity
function forceBurn(bytes32 issuerId, bytes32 accountId, bytes32 traceId, uint256 deadline, bytes signature) external
```

_アカウントを強制償却させる
AccountコントラクトのforceBurnを呼び出す。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | 発行者ID |
| accountId | bytes32 | アカウントID |
| traceId | bytes32 | traceId |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署 |

### hasIssuer

```solidity
function hasIssuer(bytes32 issuerId) external view returns (bool success, string err)
```

_issuerの存在確認。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:存在する,false:存在しない |
| err | string | エラーメッセージ |

### _hasIssuer

```solidity
function _hasIssuer(bytes32 issuerId) internal view returns (bool success, string err)
```

_issuerの存在確認(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:存在する,false:存在しない |
| err | string | エラーメッセージ |

### getIssuerList

```solidity
function getIssuerList(uint256 limit, uint256 offset) external view returns (struct IssuerListData[] issuers, uint256 totalCount, string err)
```

_issuerのリストを取得する。
TODO: DeccuretIssuerを含む前Issuerを返却する関数を別途作成する_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| limit | uint256 | limit |
| offset | uint256 | offset |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuers | struct IssuerListData[] | issuers |
| totalCount | uint256 | issuerの数 |
| err | string | zoneId |

### hasAccount

```solidity
function hasAccount(bytes32 issuerId, bytes32 accountId) external view returns (bool success, string err)
```

_指定されたIssuerIDにAccountが紐付いているか確認を行う。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:紐づいている,false:紐づいていない |
| err | string | エラーメッセージ |

### _hasAccount

```solidity
function _hasAccount(bytes32 issuerId, bytes32 accountId) internal view returns (bool success, string err)
```

_指定されたIssuerIDにAccountが紐付いているか確認を行う(内部関数)。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:紐づいている,false:紐づいていない |
| err | string | エラーメッセージ |

### getIssuer

```solidity
function getIssuer(bytes32 issuerId) external view returns (string name, uint16 bankCode, string err)
```

_issuerの情報を取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |

### getAccount

```solidity
function getAccount(bytes32 issuerId, bytes32 accountId) external view returns (string accountName, uint256 balance, bytes32 accountStatus, bytes32 reasonCode, string err)
```

_Issuerに紐づくAccountの情報を取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| accountId | bytes32 | accountId |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountName | string | アカウント名 |
| balance | uint256 | アカウントの残高 |
| accountStatus | bytes32 | アカウントの状態 |
| reasonCode | bytes32 | reasonCode |
| err | string | エラーメッセージ |

### getAccountList

```solidity
function getAccountList(bytes32 issuerId, bytes32[] inAccountIds, uint256 limit, uint256 offset) external view returns (struct IssuerAccountsData[] accounts, uint256 totalCount, string err)
```

_該当IssuerIDに紐づくAccountIDを取得する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| inAccountIds | bytes32[] | inAccountIds |
| limit | uint256 |  |
| offset | uint256 |  |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| accounts | struct IssuerAccountsData[] | 指定のアカウントを取得する |
| totalCount | uint256 | アカウントの総数 |
| err | string | エラーメッセージ |

### getIssuerCount

```solidity
function getIssuerCount() external view returns (uint256 count)
```

_issuerの数を返却する。_

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| count | uint256 | issuerの数 |

### getIssuerId

```solidity
function getIssuerId(uint256 index) external view returns (bytes32 issuerId, string err)
```

_indexに対応するissuerIdの取得。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| index | uint256 | index |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| err | string | エラーメッセージ |

### checkRole

```solidity
function checkRole(bytes32 issuerId, bytes32 hash, uint256 deadline, bytes signature) external view returns (bool has, string err)
```

_権限チェック。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| issuerId | bytes32 | issuerId |
| hash | bytes32 | signatureの計算元ハッシュ値 |
| deadline | uint256 | signatureのタイムスタンプ(秒) |
| signature | bytes | Adminユーザによる署名 |

### checkMint

```solidity
function checkMint(bytes32 accountId, uint256 amount) external view returns (bool success, string err)
```

_FinZoneコイン発行前確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 発行額 |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| success | bool | true:チェックOK,false:チェックNG |
| err | string | エラーメッセージ |

### isFrozen

```solidity
function isFrozen(bytes32 accountId) external view returns (bool frozen, string err)
```

_アカウントの凍結状態を確認する。_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |

#### Return Values

| Name | Type | Description |
| ---- | ---- | ----------- |
| frozen | bool | true:凍結中,false:凍結中でない |
| err | string | エラーメッセージ |

### checkBurn

```solidity
function checkBurn(bytes32 accountId, uint256 amount) external view returns (bool success, string err)
```

_FinZoneコイン償却前確認_

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |
| accountId | bytes32 | アカウントID |
| amount | uint256 | 償却額 |

