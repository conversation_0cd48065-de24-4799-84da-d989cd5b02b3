simpleArgs: &params
  <param_name>: <data>

test:
  name: test name
  description: >-
    test description
  workers:
    type: local
    number: 1
  rounds:
    - label: workload-round1
      description: round description.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/<workload>.js
        arguments:
          <<: *params