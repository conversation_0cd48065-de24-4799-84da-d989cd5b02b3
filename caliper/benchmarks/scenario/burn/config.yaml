simpleArgs: &simple-args
  # Use the correct issuerId that has burn permissions
  issuerId: "0x3232323100000000000000000000000000000000000000000000000000000000"
  accountId: "0x3330300000000000000000000000000000000000000000000000000000000000"
  burnAmount: 1

test:
  name: burn
  description: >-
    custom burn token
  workers:
    type: local
    number: 1
  rounds:
    - label: burn-round1
      description: Test description for burn tokens.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 10
      workload:
        module: ../benchmarks/scenario/burn/burn.js
        arguments:
          << : *simple-args
    - label: burn-round2
      description: Test description for burn tokens.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 20
      workload:
        module: ../benchmarks/scenario/burn/burn.js
        arguments:
          << : *simple-args
    - label: burn-round3
      description: Test description for burn tokens.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 30
      workload:
        module: ../benchmarks/scenario/burn/burn.js
        arguments:
          << : *simple-args
