/*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

'use strict';

const traceId = '0x00'

/**
 * Class for managing simple account states for burn.
 */
class SimpleState {

    /**
     * Initializes the instance.
     */
    constructor(issuerId, accountId, burnAmount) {
        this.issuerId = issuerId;
        this.accountId = accountId;
        this.burnAmount = burnAmount;
    }

    /**
     * Get the arguments for burn tokens.
     * @returns {object} The burn arguments as an object.
     */
    getBurnArguments() {
        // Return arguments as an object with named properties
        return {
            issuerId: this.issuerId,
            accountId: this.accountId,
            amount: this.burnAmount,
            traceId: traceId
        };
    }
}

module.exports = SimpleState;
