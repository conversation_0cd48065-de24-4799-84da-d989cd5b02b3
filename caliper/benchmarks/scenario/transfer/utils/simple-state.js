/*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

'use strict';

const miscValue1 = '0x00'
const miscValue2 = '0x00'
const memo = 'test'
const traceId = '0x00'

/**
 * Class for managing simple account states.
 */
class SimpleState {

    /**
     * Initializes the instance.
     */
    constructor(fromAccountId, moneyToTransfer, toAccountId) {
        this.fromAccountId = fromAccountId;
        this.moneyToTransfer = moneyToTransfer;
        this.toAccountId = toAccountId;
    }

    /**
     * Get the arguments for transfering money between accounts.
     * @returns {object} The account arguments.
     */
    getTransferArguments() {
        return {
            sendAccountId: this.fromAccountId,
            fromAccountId: this.fromAccountId,
            toAccountId: this.toAccountId,
            amount: this.moneyToTransfer,
            miscValue1: miscValue1,
            miscValue2: miscValue2,
            memo: memo,
            traceId: traceId
        };
    }
}

module.exports = SimpleState;
