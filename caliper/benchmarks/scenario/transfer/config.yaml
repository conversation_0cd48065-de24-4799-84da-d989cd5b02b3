simpleArgs: &simple-args
  fromAccountId: "0x3330300000000000000000000000000000000000000000000000000000000000"
  toAccountId: "0x3330320000000000000000000000000000000000000000000000000000000000"
  moneyToTransfer: 1

test:
  name: transfer
  description: >-
    custom transfer
  workers:
    type: local
    number: 1
  rounds:
    - label: transfer-single-round1
      description: Test description for transfering money between accounts.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 10
      workload:
        module: benchmarks/scenario/transfer/transfer.js
        arguments:
          << : *simple-args
    - label: transfer-single-round2
      description: Test description for transfering money between accounts.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 20
      workload:
        module: benchmarks/scenario/transfer/transfer.js
        arguments:
          << : *simple-args
    - label: transfer-single-round3
      description: Test description for transfering money between accounts.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 30
      workload:
        module: benchmarks/scenario/transfer/transfer.js
        arguments:
          << : *simple-args
    - label: transfer-single-round4
      description: Test description for transfering money between accounts.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 40
      workload:
        module: benchmarks/scenario/transfer/transfer.js
        arguments:
          << : *simple-args
    - label: transfer-single-round5
      description: Test description for transfering money between accounts.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 300
      workload:
        module: benchmarks/scenario/transfer/transfer.js
        arguments:
          << : *simple-args
    - label: transfer-single-round6
      description: Test description for transfering money between accounts.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 500
      workload:
        module: benchmarks/scenario/transfer/transfer.js
        arguments:
          << : *simple-args