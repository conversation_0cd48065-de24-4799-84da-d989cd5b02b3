/*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

'use strict';

const traceId = '0x00'
const timeoutHeight =  1000000;


/**
 * Class for managing simple account states for discharge.
 */
class SimpleState {

    /**
     * Initializes the instance.
     */
    constructor(accountId, fromZoneId, toZoneId, amount) {
        this.accountId = accountId;
        this.fromZoneId = fromZoneId;
        this.toZoneId = toZoneId;
        this.amount = amount;
    }

    /**
     * Get the arguments for discharge
     * @returns {object} The mint arguments as an object.
     */
    getDischargeArguments() {
        // Return arguments as an object with named properties
        return {
            accountId: this.accountId,
            fromZoneId: this.fromZoneId,
            toZoneId: this.toZoneId,
            amount: this.amount,
            timeoutHeight: timeoutHeight,
            traceId: traceId,
        };
    }
}

module.exports = SimpleState;