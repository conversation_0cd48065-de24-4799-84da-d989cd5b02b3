simpleArgs: &params
  issuerId: 3000
  count: 100

test:
  name: issuer
  description: >-
    add issuer
  workers:
    type: local
    number: 1
  rounds:
    - label: addIssuer-round1
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addIssuer.js
        arguments:
          <<: *params
    - label: addIssuer-round2
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addIssuer.js
        arguments:
          <<: *params
    - label: addIssuer-round3
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addIssuer.js
        arguments:
          <<: *params
    - label: addIssuer-round4
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addIssuer.js
        arguments:
          <<: *params
    - label: addIssuer-round5
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addIssuer.js
        arguments:
          <<: *params