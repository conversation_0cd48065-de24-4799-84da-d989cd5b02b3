simpleArgs: &params
  issuerId: 3000
  count: 100
  
test:
  name: validator
  description: >-
    add validator
  workers:
    type: local
    number: 1
  rounds:
    - label: addValidator-round1
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addValidator.js
        arguments:
          <<: *params
    - label: addValidator-round2
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addValidator.js
        arguments:
          <<: *params
    - label: addValidator-round3
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addValidator.js
        arguments:
          <<: *params
    - label: addValidator-round4
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addValidator.js
        arguments:
          <<: *params
    - label: addValidator-round5
      description: Test description for transfering money between accounts.
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: benchmarks/scenario/register/addValidator.js
        arguments:
          <<: *params