/*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/


'use strict';

const { WorkloadModuleBase } = require('@hyperledger/caliper-core');
const Tools = require("../tools.js");

/**
 * Workload module for transferring money between accounts.
 */
class Workload extends WorkloadModuleBase {

    /**
     * Initializes the instance.
     */
    constructor() {
        super();
    }

    /**
     * Initialize the workload module with the given parameters.
     * @param {number} workerIndex The 0-based index of the worker instantiating the workload module.
     * @param {number} totalWorkers The total number of workers participating in the round.
     * @param {number} roundIndex The 0-based index of the currently executing round.
     * @param {Object} roundArguments The user-provided arguments for the round from the benchmark configuration file.
     * @param {ConnectorBase} sutAdapter The adapter of the underlying SUT.
     * @param {Object} sutContext The custom context object provided by the SUT adapter.
     * @async
     */
     async initializeWorkloadModule(workerIndex, totalWorkers, roundIndex, roundArguments, sutAdapter, sutContext) {
        await super.initializeWorkloadModule(workerIndex, totalWorkers, roundIndex, roundArguments, sutAdapter, sutContext);
    }

        
    /**
     * Custom parameters setting
     */
    getParameters() {
        const txs = [...Array(this.roundArguments.count).keys()].map((v) => {
            // 各txに送っているIdが異なる
            // configからのparameter: countは1Roundに送るtxを設定
            // roundIndexは各Roundの設定(configのRoundにより自動設定：0~)
            // countとroundIndexでIdを重複しないように算出
            const issuerId = this.roundArguments.issuerId + v + (this.roundIndex * this.roundArguments.count);
            // `test`stringが変換ごの内容
            const validName =  "0x74657374";
            // 署名時間
            const deadline = Math.floor(Date.now() / 1000);
            // Admin key 
            const privAdm = 'c87509a1c067bbde78beb793e6fa76530b6382a4c0241e5e4a9ec0a0f44dc0d3';

            const Args = [
                issuerId,
                issuerId,
                validName,
                deadline,
                Tools.extSign(
                    privAdm,
                    ["uint256", "uint256", "bytes32", "uint256"],
                    [issuerId, issuerId, validName, deadline]
                )[0]
            ];
            return {
                // Contract名
                contract: 'Validator',
                // Method名
                verb: 'addValidator',
                // Parameters
                args: Args
            }
        })
        return txs
    }

    /**
     * Assemble TXs
     */
    async submitTransaction() {
        const txs = await this.getParameters();
        await this.sutAdapter.sendRequests(txs);
    }
}

/**
 * Create a new instance of the workload module.
 * @return {WorkloadModuleInterface}
 */
function createWorkloadModule() {
    return new Workload();
}

module.exports.createWorkloadModule = createWorkloadModule;
