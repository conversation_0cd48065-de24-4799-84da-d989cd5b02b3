const ethSigUtil = require("eth-sig-util");
const Web3 = require('web3');


function extSign(privateKey, typesArray, parameters) {
  let web3 = new Web3('ws://localhost:8546');
  const abi = web3.eth.abi.encodeParameters(typesArray, parameters);
  const hash = web3.utils.keccak256(abi);
  return [
    ethSigUtil.personalSign(Buffer.from(privateKey, "hex"), { data: hash }),
    hash,
  ];
}

function recoverSign(hash, signature) {
  const msgParams = {
    data: hash,
    sig: signature,
  };
  return ethSigUtil.recoverPersonalSignature(msgParams);
}

function showTx(tx) {
  console.log("txHash: " + tx.tx);
  console.log("  [receipt]");
  console.log("    blockHash: " + tx.receipt.blockHash);
  console.log("    blockNumber: " + tx.receipt.blockNumber);
  console.log("    from: " + tx.receipt.from);
  for (lp = 0; lp < Object.keys(tx.logs).length; lp++) {
    console.log("  [logs]:" + lp);
    console.log("    address: " + tx.logs[lp].address);
    console.log("    event: " + tx.logs[lp].event);
    console.log("    args: " + JSON.stringify(tx.logs[lp].args));
  }
  console.log();
}

function showThrow(e) {
  let web3 = new Web3('ws://localhost:8546');
  console.log("*********** EXCEPTION ***********");
  if (e.tx != null) {
    console.log(`throw: ${e.name}`);
    console.log(`txHash: ${e.tx}`);
    console.log(`  [receipt]`);
    console.log(`    blockHash: ${e.receipt.blockHash}`);
    console.log(`    blockNumber: ${e.receipt.blockNumber}`);
    console.log(`    from: ${e.receipt.from}`);
    console.log(`    to  : ${e.receipt.to}`);
    console.log(`    status: ${e.receipt.status}`);
    if (e.receipt.revertReason == null) {
      console.log("    no revertReason");
    } else if (e.receipt.revertReason.length >= 202) {
      // https://besu.hyperledger.org/en/stable/HowTo/Send-Transactions/Revert-Reason/#revert-reason-format
      // 0x: 2
      // selector: 8
      // total length: 64
      // text length: 64
      // text: 64?
      const reasonLen = web3.utils.hexToNumber(
        "0x" + e.receipt.revertReason.substr(74, 64)
      );
      console.log(
        `    revertReason: ${web3.utils.hexToUtf8(
          "0x" + e.receipt.revertReason.substr(138, 2 * reasonLen)
        )}`
      );
    } else {
      console.log(
        "    bad revertReason: " + JSON.stringify(e.receipt.revertReason)
      );
    }
    console.log("*********************************");
    console.error(e.hijackedStack);
  } else {
    console.error(e);
  }
  console.log("*********************************");
}

module.exports = {
  extSign: extSign,
  showTx: showTx,
  showThrow: showThrow,
  recoverSign: recoverSign,
};
