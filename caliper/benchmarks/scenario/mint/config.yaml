simpleArgs: &simple-args
  issuerId: "0x3232323100000000000000000000000000000000000000000000000000000000"
  accountId: "0x3330300000000000000000000000000000000000000000000000000000000000"
  mintAmount: 1

test:
  name: mint
  description: >-
    custom mint token
  workers:
    type: local
    number: 1
  rounds:
    - label: mint-round1
      description: Test description for minting tokens.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 10
      workload:
        module: ../benchmarks/scenario/mint/mint.js
        arguments:
          << : *simple-args
    - label: mint-round2
      description: Test description for minting tokens.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 20
      workload:
        module: ../benchmarks/scenario/mint/mint.js
        arguments:
          << : *simple-args
    - label: mint-round3
      description: Test description for minting tokens.
      txNumber: 500
      rateControl:
        type: fixed-rate
        opts:
          tps: 30
      workload:
        module: ../benchmarks/scenario/mint/mint.js
        arguments:
          << : *simple-args
