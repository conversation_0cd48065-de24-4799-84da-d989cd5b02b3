/*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

'use strict';

const traceId = '0x00'

/**
 * Class for managing simple account states for minting.
 */
class SimpleState {

    /**
     * Initializes the instance.
     */
    constructor(issuerId, accountId, mintAmount) {
        this.issuerId = issuerId;
        this.accountId = accountId;
        this.mintAmount = mintAmount;
    }

    /**
     * Get the arguments for minting tokens.
     * @returns {object} The mint arguments as an object.
     */
    getMintArguments() {
        // Return arguments as an object with named properties
        return {
            issuerId: this.issuerId,
            accountId: this.accountId,
            amount: this.mintAmount,
            traceId: traceId
        };
    }
}

module.exports = SimpleState;
