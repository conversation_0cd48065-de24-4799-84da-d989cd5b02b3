simpleArgs: &simple-args
  accountId: "0x3630320000000000000000000000000000000000000000000000000000000000"
  fromZoneId: 3000
  toZoneId: 3001
  amount: 1

test:
  name: charge
  description: >-
    custom charge
  workers:
    type: local
    number: 1
  rounds:
    - label: charge-round1
      description: Test description for charge
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: ../benchmarks/scenario/charge/charge.js
        arguments:
          << : *simple-args
    - label: charge-round2
      description: Test description for charge
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: ../benchmarks/scenario/charge/charge.js
        arguments:
          << : *simple-args
    - label: charge-round3
      description: Test description for charge
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: ../benchmarks/scenario/charge/charge.js
        arguments:
          << : *simple-args
    - label: charge-round4
      description: Test description for charge
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: ../benchmarks/scenario/charge/charge.js
        arguments:
          << : *simple-args
    - label: charge-round5
      description: Test description for charge
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: ../benchmarks/scenario/charge/charge.js
        arguments:
          << : *simple-args