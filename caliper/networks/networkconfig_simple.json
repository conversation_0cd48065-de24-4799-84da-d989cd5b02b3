{"caliper": {"blockchain": "ethereum", "command": {}}, "ethereum": {"url": "ws://127.0.0.1:8546", "contractDeployerAddress": "******************************************", "contractDeployerAddressPrivateKey": "0x797c13f7235c627f6bd013dc17fff4c12213ab49abcf091f77c83f16db10e90b", "fromAddressSeed": "0x3f841bf589fdf83a521e55d51afddc34fa65351161eead24f064855fc29c9580", "transactionConfirmationBlocks": 2, "contracts": {"simple": {"path": "./src/simple/simple.json", "estimateGas": true, "gas": {"query": 100000, "transfer": 70000}}}}}