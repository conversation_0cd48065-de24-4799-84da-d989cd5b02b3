{"caliper": {"blockchain": "ethereum", "command": {}}, "ethereum": {"url": "ws://127.0.0.1:8541", "fromAddressSeed": "0x8d5366123cb560bb606379f90a0bfd4769eecc0557f1b362dcae9012b548b1e5", "transactionConfirmationBlocks": 2, "contracts": {"Issuer": {"address": "******************************************", "estimateGas": true, "gas": {"addIssuer": 1000000}, "abi": [{"inputs": [{"internalType": "uint256", "name": "issuerId", "type": "uint256"}, {"internalType": "bytes32", "name": "name", "type": "bytes32"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "add<PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]}, "Validator": {"address": "******************************************", "estimateGas": true, "gas": {"addValidator": 1000000, "addAccount": 1000000}, "abi": [{"inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "uint256", "name": "issuerId", "type": "uint256"}, {"internalType": "bytes32", "name": "name", "type": "bytes32"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "addValidator", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "uint256", "name": "accountId", "type": "uint256"}, {"internalType": "uint256[]", "name": "limitAmounts", "type": "uint256[]"}], "name": "addAccount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]}}}}