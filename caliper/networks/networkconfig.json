{"caliper": {"blockchain": "ethereum", "command": {}, "transactionSendTimeout": 99999999, "sutName": "besu"}, "ethereum": {"url": "ws://127.0.0.1:18541", "fromAddressSeed": "0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6", "transactionConfirmationBlocks": 2, "contracts": {"Token": {"address": "******************************************", "estimateGas": true, "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint16", "name": "zoneId", "type": "uint16"}, {"indexed": false, "internalType": "bytes32", "name": "validatorId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "issuerId", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "accountId", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "accountName", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "balance", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "Burn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint16", "name": "zoneId", "type": "uint16"}, {"indexed": false, "internalType": "bytes32", "name": "validatorId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "issuerId", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "accountId", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "accountName", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "balance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "blockTimestamp", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "BurnCancel", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "sendAccountId", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "fromAccountId", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "toAccountId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "miscValue1", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "miscValue2", "type": "string"}, {"indexed": false, "internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "CustomTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint16", "name": "zoneId", "type": "uint16"}, {"indexed": false, "internalType": "bytes32", "name": "validatorId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "issuerId", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "accountId", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "accountName", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "balance", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "tokenId", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "name", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "symbol", "type": "bytes32"}, {"indexed": false, "internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "ModToken", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "tokenId", "type": "bytes32"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}, {"indexed": false, "internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "SetEnabledToken", "type": "event"}, {"anonymous": false, "inputs": [{"components": [{"internalType": "bytes32", "name": "transferType", "type": "bytes32"}, {"internalType": "uint16", "name": "zoneId", "type": "uint16"}, {"internalType": "bytes32", "name": "fromValidatorId", "type": "bytes32"}, {"internalType": "bytes32", "name": "toValidatorId", "type": "bytes32"}, {"internalType": "uint256", "name": "fromAccountBalance", "type": "uint256"}, {"internalType": "uint256", "name": "toAccountBalance", "type": "uint256"}, {"internalType": "uint256", "name": "businessZoneBalance", "type": "uint256"}, {"internalType": "uint16", "name": "bizZoneId", "type": "uint16"}, {"internalType": "bytes32", "name": "sendAccountId", "type": "bytes32"}, {"internalType": "bytes32", "name": "fromAccountId", "type": "bytes32"}, {"internalType": "string", "name": "fromAccountName", "type": "string"}, {"internalType": "bytes32", "name": "toAccountId", "type": "bytes32"}, {"internalType": "string", "name": "toAccountName", "type": "string"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes32", "name": "miscValue1", "type": "bytes32"}, {"internalType": "string", "name": "miscValue2", "type": "string"}, {"internalType": "string", "name": "memo", "type": "string"}], "indexed": false, "internalType": "struct TransferData", "name": "transferData", "type": "tuple"}, {"indexed": false, "internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "tokenId", "type": "bytes32"}, {"internalType": "bytes32", "name": "name", "type": "bytes32"}, {"internalType": "bytes32", "name": "symbol", "type": "bytes32"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "addToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "addTotalSupply", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "validatorId", "type": "bytes32"}, {"internalType": "bytes32", "name": "ownerId", "type": "bytes32"}, {"internalType": "bytes32", "name": "spenderId", "type": "bytes32"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "issuerId", "type": "bytes32"}, {"internalType": "bytes32", "name": "accountId", "type": "bytes32"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "issuerId", "type": "bytes32"}, {"internalType": "bytes32", "name": "accountId", "type": "bytes32"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "blockTimestamp", "type": "uint256"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "burnCancel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "validatorId", "type": "bytes32"}, {"internalType": "bytes32", "name": "ownerId", "type": "bytes32"}, {"internalType": "bytes32", "name": "spenderId", "type": "bytes32"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "accountSignature", "type": "bytes"}, {"internalType": "bytes", "name": "info", "type": "bytes"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "checkApprove", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "string", "name": "err", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "sendAccountId", "type": "bytes32"}, {"internalType": "bytes32", "name": "fromAccountId", "type": "bytes32"}, {"internalType": "bytes32", "name": "toAccountId", "type": "bytes32"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes32", "name": "miscValue1", "type": "bytes32"}, {"internalType": "string", "name": "miscValue2", "type": "string"}, {"internalType": "string", "name": "memo", "type": "string"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "customTransfer", "outputs": [{"internalType": "bool", "name": "result", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "validatorId", "type": "bytes32"}, {"internalType": "bytes32", "name": "ownerId", "type": "bytes32"}, {"internalType": "bytes32", "name": "spenderId", "type": "bytes32"}], "name": "getAllowance", "outputs": [{"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "approvedAt", "type": "uint256"}, {"internalType": "string", "name": "err", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ownerAccountId", "type": "bytes32"}, {"internalType": "uint256", "name": "offset", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getAllowanceList", "outputs": [{"components": [{"internalType": "bytes32", "name": "spanderId", "type": "bytes32"}, {"internalType": "string", "name": "spenderAccountName", "type": "string"}, {"internalType": "uint256", "name": "allowanceAmount", "type": "uint256"}, {"internalType": "uint256", "name": "approvedAt", "type": "uint256"}], "internalType": "struct AccountApprovalAll[]", "name": "approvalData", "type": "tuple[]"}, {"internalType": "uint256", "name": "totalCount", "type": "uint256"}, {"internalType": "string", "name": "err", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "accountId", "type": "bytes32"}], "name": "getBalanceList", "outputs": [{"internalType": "uint16[]", "name": "zoneIds", "type": "uint16[]"}, {"internalType": "string[]", "name": "zoneNames", "type": "string[]"}, {"internalType": "uint256[]", "name": "balances", "type": "uint256[]"}, {"internalType": "string[]", "name": "accountNames", "type": "string[]"}, {"internalType": "bytes32[]", "name": "accountStatus", "type": "bytes32[]"}, {"internalType": "uint256", "name": "totalBalance", "type": "uint256"}, {"internalType": "string", "name": "err", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getToken", "outputs": [{"internalType": "bytes32", "name": "tokenId", "type": "bytes32"}, {"internalType": "bytes32", "name": "name", "type": "bytes32"}, {"internalType": "bytes32", "name": "symbol", "type": "bytes32"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "bool", "name": "enabled", "type": "bool"}, {"internalType": "string", "name": "err", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTokenAll", "outputs": [{"components": [{"internalType": "bytes32", "name": "tokenId", "type": "bytes32"}, {"internalType": "bytes32", "name": "name", "type": "bytes32"}, {"internalType": "bytes32", "name": "symbol", "type": "bytes32"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "internalType": "struct Token<PERSON>ll", "name": "token", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "tokenId", "type": "bytes32"}, {"internalType": "bool", "name": "chkEnabled", "type": "bool"}], "name": "hasToken", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "string", "name": "err", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "hasTokenState", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "string", "name": "err", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IContractManager", "name": "contractManager", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "issuerId", "type": "bytes32"}, {"internalType": "bytes32", "name": "accountId", "type": "bytes32"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "tokenId", "type": "bytes32"}, {"internalType": "bytes32", "name": "name", "type": "bytes32"}, {"internalType": "bytes32", "name": "symbol", "type": "bytes32"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "modToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "bytes32", "name": "tokenId", "type": "bytes32"}, {"internalType": "bytes32", "name": "name", "type": "bytes32"}, {"internalType": "bytes32", "name": "symbol", "type": "bytes32"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "internalType": "struct Token<PERSON>ll", "name": "token", "type": "tuple"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "setTokenAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "providerId", "type": "bytes32"}, {"internalType": "bytes32", "name": "tokenId", "type": "bytes32"}, {"internalType": "bool", "name": "enabled", "type": "bool"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "setTokenEnabled", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "subTotalSupply", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "sendAccountId", "type": "bytes32"}, {"internalType": "bytes32", "name": "fromAccountId", "type": "bytes32"}, {"internalType": "bytes32", "name": "toAccountId", "type": "bytes32"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes32", "name": "miscValue1", "type": "bytes32"}, {"internalType": "string", "name": "miscValue2", "type": "string"}, {"internalType": "string", "name": "memo", "type": "string"}, {"internalType": "bytes32", "name": "traceId", "type": "bytes32"}], "name": "transferSingle", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}]}}}}