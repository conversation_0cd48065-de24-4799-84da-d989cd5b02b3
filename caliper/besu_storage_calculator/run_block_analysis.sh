#!/bin/bash

# Script to automate block analysis process for both FinZone and BizZone blockchains
# This script will:
# 1. Get the current block numbers and measure initial DB storage size for both FinZone and BizZone
# 2. Run caliper to generate transactions
# 3. Get the new block numbers and measure final DB storage size for both zones after caliper completes
# 4. Run block_size_calculator.sh with the start and end block numbers for FinZone
# 5. Run analyze_rlp_blocks.js to analyze the results

# Usage:
# ./run_block_analysis.sh [FINZONE_CONTAINER_ID] [BIZZONE_CONTAINER_ID] [FINZONE_DB_PATH] [BIZZONE_DB_PATH]
#
# Examples:
# ./run_block_analysis.sh                                                          # Use default containers and paths
# ./run_block_analysis.sh a3777dc9ed1c                                             # Use specified FinZone container with default BizZone container and paths
# ./run_block_analysis.sh a3777dc9ed1c b4888ef5fd2d                                # Use specified FinZone and BizZone containers with default paths
# ./run_block_analysis.sh a3777dc9ed1c b4888ef5fd2d /mnt/data/database             # Use specified FinZone and BizZone containers with specified FinZone DB path
# ./run_block_analysis.sh a3777dc9ed1c b4888ef5fd2d /mnt/data/database /data/besu  # Use specified FinZone and BizZone containers with specified DB paths

# Set the Besu node WebSocket URLs for both zones
FinZone_WS_URL="ws://127.0.0.1:18541"
BizZone_WS_URL="ws://127.0.0.1:28541"

# Set the caliper configuration files
NETWORK_CONFIG="../networks/discharge_networkconfig.json"
BENCHMARK_CONFIG="../benchmarks/scenario/discharge/config.yaml"

# Set default Besu DB paths - using Docker containers
FINZONE_DEFAULT_CONTAINER_ID="50c216c30739"
BIZZONE_DEFAULT_CONTAINER_ID="927deb0e40eb"
FINZONE_DB_PATH="/mnt/data/database"
BIZZONE_DB_PATH="/mnt/data/database"
IS_DOCKER=true

# Process command line arguments
if [ $# -ge 1 ]; then
    # First argument is FinZone container ID
    CUSTOM_FINZONE_CONTAINER_ID="$1"
    echo "Using custom FinZone Docker container ID: $CUSTOM_FINZONE_CONTAINER_ID"
    FINZONE_DEFAULT_CONTAINER_ID="$CUSTOM_FINZONE_CONTAINER_ID"

    # Check if container exists
    if ! docker ps -a | grep -q "$FINZONE_DEFAULT_CONTAINER_ID"; then
        echo "Warning: Docker container $FINZONE_DEFAULT_CONTAINER_ID not found"
        echo "Will try to use it anyway in case it's a partial ID"
    fi

    # Second argument is BizZone container ID (if provided)
    if [ $# -ge 2 ]; then
        CUSTOM_BIZZONE_CONTAINER_ID="$2"
        echo "Using custom BizZone Docker container ID: $CUSTOM_BIZZONE_CONTAINER_ID"
        BIZZONE_DEFAULT_CONTAINER_ID="$CUSTOM_BIZZONE_CONTAINER_ID"

        # Check if container exists
        if ! docker ps -a | grep -q "$BIZZONE_DEFAULT_CONTAINER_ID"; then
            echo "Warning: Docker container $BIZZONE_DEFAULT_CONTAINER_ID not found"
            echo "Will try to use it anyway in case it's a partial ID"
        fi
    fi

    # Third argument is FinZone DB path (if provided)
    if [ $# -ge 3 ]; then
        CUSTOM_FINZONE_DB_PATH="$3"
        echo "Using custom FinZone DB path: $CUSTOM_FINZONE_DB_PATH"
        FINZONE_DB_PATH="$CUSTOM_FINZONE_DB_PATH"
    fi

    # Fourth argument is BizZone DB path (if provided)
    if [ $# -ge 4 ]; then
        CUSTOM_BIZZONE_DB_PATH="$4"
        echo "Using custom BizZone DB path: $CUSTOM_BIZZONE_DB_PATH"
        BIZZONE_DB_PATH="$CUSTOM_BIZZONE_DB_PATH"
    fi
fi

# Log the Docker containers and paths we're using
echo "Using FinZone Docker container: $FINZONE_DEFAULT_CONTAINER_ID with DB path: $FINZONE_DB_PATH"
echo "Using BizZone Docker container: $BIZZONE_DEFAULT_CONTAINER_ID with DB path: $BIZZONE_DB_PATH"

# Create a temporary file for storing the JSON-RPC responses
TEMP_FILE=$(mktemp)

# Create directories for each zone's data
mkdir -p block_data/finzone
mkdir -p block_data/bizzone

# Define output files for each zone
FINZONE_STORAGE_REPORT_FILE="block_data/finzone/storage_analysis_report.txt"
BIZZONE_STORAGE_REPORT_FILE="block_data/bizzone/storage_analysis_report.txt"
FINZONE_BLOCK_DATA_FILE="block_data/finzone/all_block_rlp_encoded_data.json"
BIZZONE_BLOCK_DATA_FILE="block_data/bizzone/all_block_rlp_encoded_data.json"
FINZONE_RLP_RAW_DATA_FILE="block_data/finzone/all_blocks_rlp_raw_data.txt"
BIZZONE_RLP_RAW_DATA_FILE="block_data/bizzone/all_blocks_rlp_raw_data.txt"
FINZONE_RLP_ANALYSIS_FILE="block_data/finzone/rlp_analysis_report.txt"
BIZZONE_RLP_ANALYSIS_FILE="block_data/bizzone/rlp_analysis_report.txt"

# Function to measure directory size in MB
measure_db_size() {
    local dir_path=$1
    local container_id=$2
    local size_bytes

    # Check if we're measuring inside a Docker container
    if [ "$IS_DOCKER" = true ] && [ -n "$container_id" ]; then
        # Use docker exec to run du inside the container
        # First check if the container is running
        if docker ps -q --filter "id=$container_id" | grep -q .; then
            # Container is running, execute du command with -b for bytes
            size_bytes=$(docker exec $container_id du -b -s "$dir_path" | awk '{print $1}')
        else
            echo "Warning: Docker container $container_id is not running" >&2
            return 1
        fi
    else
        # Local directory measurement
        # Check OS type and use appropriate du options
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            size_bytes=$(du -sk "$dir_path" | cut -f1)
            # Convert KB to bytes (multiply by 1024)
            size_bytes=$((size_bytes * 1024))
        else
            # Linux and others
            size_bytes=$(du -sb "$dir_path" | cut -f1)
        fi
    fi

    # Return the size in bytes directly
    echo "$size_bytes"
}

# Function to measure Besu DB storage size
analyze_db_structure() {
    local dir_path=$1
    local output_file=$2
    local prefix=$3
    local container_id=$4

    echo "${prefix} Besu DB Storage Measurement:" >> "$output_file"
    echo "--------------------------------" >> "$output_file"

    # Check if we're using Docker
    if [ "$IS_DOCKER" = true ]; then
        echo "Measuring storage in Docker container: $container_id" >> "$output_file"
        echo "Database path: $dir_path" >> "$output_file"

        # Measure total size of the Besu database in Docker
        local total_size=$(measure_db_size "$dir_path" "$container_id")
        if [ $? -eq 0 ]; then
            echo "Total Besu DB size: $total_size bytes" >> "$output_file"
        else
            echo "Error measuring Docker container storage" >> "$output_file"
            total_size=0
        fi
    else
        # Measure total size of the local Besu database
        local total_size=$(measure_db_size "$dir_path")
        echo "Total Besu DB size: $total_size bytes" >> "$output_file"
    fi

    echo "" >> "$output_file"

    # Return the total size for calculations
    echo "$total_size"
}

# Initialize FinZone report
echo "=== Starting FinZone Block Analysis Process ===" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
echo "Analysis timestamp: $(date)" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
echo "" >> "$FINZONE_STORAGE_REPORT_FILE"

# Initialize BizZone report
echo "=== Starting BizZone Block Analysis Process ===" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
echo "Analysis timestamp: $(date)" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
echo "" >> "$BIZZONE_STORAGE_REPORT_FILE"

# Step 1: Get the current block number and measure initial DB storage for both zones
echo "Step 1: Getting current block numbers and measuring initial DB storage for both zones..."

# FinZone block number
echo "Getting FinZone block number..." | tee -a "$FINZONE_STORAGE_REPORT_FILE"
wscat -c $FinZone_WS_URL -x '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' > $TEMP_FILE
FINZONE_START_BLOCK_HEX=$(cat $TEMP_FILE | grep -o '"result":"0x[^"]*"' | cut -d'"' -f4)
FINZONE_START_BLOCK=$((16#${FINZONE_START_BLOCK_HEX:2}))
echo "FinZone current block number: $FINZONE_START_BLOCK (hex: $FINZONE_START_BLOCK_HEX)" | tee -a "$FINZONE_STORAGE_REPORT_FILE"

# BizZone block number
echo "Getting BizZone block number..." | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
wscat -c $BizZone_WS_URL -x '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' > $TEMP_FILE
BIZZONE_START_BLOCK_HEX=$(cat $TEMP_FILE | grep -o '"result":"0x[^"]*"' | cut -d'"' -f4)
BIZZONE_START_BLOCK=$((16#${BIZZONE_START_BLOCK_HEX:2}))
echo "BizZone current block number: $BIZZONE_START_BLOCK (hex: $BIZZONE_START_BLOCK_HEX)" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"

# Measure initial DB storage for FinZone
echo "Measuring initial FinZone Besu DB storage..." | tee -a "$FINZONE_STORAGE_REPORT_FILE"
if [ "$IS_DOCKER" = true ]; then
    # For Docker, we check if the container is running
    if docker ps -q --filter "id=$FINZONE_DEFAULT_CONTAINER_ID" | grep -q .; then
        FINZONE_INITIAL_DB_SIZE=$(analyze_db_structure "$FINZONE_DB_PATH" "$FINZONE_STORAGE_REPORT_FILE" "Initial FinZone" "$FINZONE_DEFAULT_CONTAINER_ID")
        echo "Initial FinZone DB size: $FINZONE_INITIAL_DB_SIZE bytes" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
    else
        echo "Warning: Docker container $FINZONE_DEFAULT_CONTAINER_ID is not running" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
        FINZONE_INITIAL_DB_SIZE=0
    fi
else
    # For local path, we check if the directory exists
    if [ -d "$FINZONE_DB_PATH" ]; then
        FINZONE_INITIAL_DB_SIZE=$(analyze_db_structure "$FINZONE_DB_PATH" "$FINZONE_STORAGE_REPORT_FILE" "Initial FinZone" "")
        echo "Initial FinZone DB size: $FINZONE_INITIAL_DB_SIZE bytes" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
    else
        echo "Warning: Besu DB path not found at $FINZONE_DB_PATH" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
        FINZONE_INITIAL_DB_SIZE=0
    fi
fi

# Measure initial DB storage for BizZone
echo "Measuring initial BizZone Besu DB storage..." | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
if [ "$IS_DOCKER" = true ]; then
    # For Docker, we check if the container is running
    if docker ps -q --filter "id=$BIZZONE_DEFAULT_CONTAINER_ID" | grep -q .; then
        BIZZONE_INITIAL_DB_SIZE=$(analyze_db_structure "$BIZZONE_DB_PATH" "$BIZZONE_STORAGE_REPORT_FILE" "Initial BizZone" "$BIZZONE_DEFAULT_CONTAINER_ID")
        echo "Initial BizZone DB size: $BIZZONE_INITIAL_DB_SIZE bytes" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
    else
        echo "Warning: Docker container $BIZZONE_DEFAULT_CONTAINER_ID is not running" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
        BIZZONE_INITIAL_DB_SIZE=0
    fi
else
    # For local path, we check if the directory exists
    if [ -d "$BIZZONE_DB_PATH" ]; then
        BIZZONE_INITIAL_DB_SIZE=$(analyze_db_structure "$BIZZONE_DB_PATH" "$BIZZONE_STORAGE_REPORT_FILE" "Initial BizZone" "")
        echo "Initial BizZone DB size: $BIZZONE_INITIAL_DB_SIZE bytes" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
    else
        echo "Warning: Besu DB path not found at $BIZZONE_DB_PATH" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
        BIZZONE_INITIAL_DB_SIZE=0
    fi
fi

# Step 2: Run caliper to generate transactions
echo "Step 2: Running caliper to generate transactions..."
npx caliper launch manager --caliper-flow-only-test=true --caliper-workspace . --caliper-networkconfig $NETWORK_CONFIG --caliper-benchconfig $BENCHMARK_CONFIG
sleep 20

# Step 3: Get the new block numbers and measure final DB storage after caliper completes for both zones
echo "Step 3: Getting new block numbers and measuring final DB storage for both zones..."

# FinZone block number
echo "Getting FinZone final block number..." | tee -a "$FINZONE_STORAGE_REPORT_FILE"
wscat -c $FinZone_WS_URL -x '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' > $TEMP_FILE
FINZONE_END_BLOCK_HEX=$(cat $TEMP_FILE | grep -o '"result":"0x[^"]*"' | cut -d'"' -f4)
FINZONE_END_BLOCK=$((16#${FINZONE_END_BLOCK_HEX:2}))
echo "FinZone new block number: $FINZONE_END_BLOCK (hex: $FINZONE_END_BLOCK_HEX)" | tee -a "$FINZONE_STORAGE_REPORT_FILE"

# BizZone block number
echo "Getting BizZone final block number..." | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
wscat -c $BizZone_WS_URL -x '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' > $TEMP_FILE
BIZZONE_END_BLOCK_HEX=$(cat $TEMP_FILE | grep -o '"result":"0x[^"]*"' | cut -d'"' -f4)
BIZZONE_END_BLOCK=$((16#${BIZZONE_END_BLOCK_HEX:2}))
echo "BizZone new block number: $BIZZONE_END_BLOCK (hex: $BIZZONE_END_BLOCK_HEX)" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"

# Measure final DB storage for FinZone
echo "Measuring final FinZone Besu DB storage..." | tee -a "$FINZONE_STORAGE_REPORT_FILE"
if [ "$IS_DOCKER" = true ]; then
    # For Docker, we check if the container is running
    if docker ps -q --filter "id=$FINZONE_DEFAULT_CONTAINER_ID" | grep -q .; then
        FINZONE_FINAL_DB_SIZE=$(analyze_db_structure "$FINZONE_DB_PATH" "$FINZONE_STORAGE_REPORT_FILE" "Final FinZone" "$FINZONE_DEFAULT_CONTAINER_ID")
        echo "Final FinZone DB size: $FINZONE_FINAL_DB_SIZE bytes" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
    else
        echo "Warning: Docker container $FINZONE_DEFAULT_CONTAINER_ID is not running" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
        FINZONE_FINAL_DB_SIZE=0
    fi
else
    # For local path, we check if the directory exists
    if [ -d "$FINZONE_DB_PATH" ]; then
        FINZONE_FINAL_DB_SIZE=$(analyze_db_structure "$FINZONE_DB_PATH" "$FINZONE_STORAGE_REPORT_FILE" "Final FinZone" "")
        echo "Final FinZone DB size: $FINZONE_FINAL_DB_SIZE bytes" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
    else
        echo "Warning: Besu DB path not found at $FINZONE_DB_PATH" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
        FINZONE_FINAL_DB_SIZE=0
    fi
fi

# Measure final DB storage for BizZone
echo "Measuring final BizZone Besu DB storage..." | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
if [ "$IS_DOCKER" = true ]; then
    # For Docker, we check if the container is running
    if docker ps -q --filter "id=$BIZZONE_DEFAULT_CONTAINER_ID" | grep -q .; then
        BIZZONE_FINAL_DB_SIZE=$(analyze_db_structure "$BIZZONE_DB_PATH" "$BIZZONE_STORAGE_REPORT_FILE" "Final BizZone" "$BIZZONE_DEFAULT_CONTAINER_ID")
        echo "Final BizZone DB size: $BIZZONE_FINAL_DB_SIZE bytes" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
    else
        echo "Warning: Docker container $BIZZONE_DEFAULT_CONTAINER_ID is not running" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
        BIZZONE_FINAL_DB_SIZE=0
    fi
else
    # For local path, we check if the directory exists
    if [ -d "$BIZZONE_DB_PATH" ]; then
        BIZZONE_FINAL_DB_SIZE=$(analyze_db_structure "$BIZZONE_DB_PATH" "$BIZZONE_STORAGE_REPORT_FILE" "Final BizZone" "")
        echo "Final BizZone DB size: $BIZZONE_FINAL_DB_SIZE bytes" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
    else
        echo "Warning: Besu DB path not found at $BIZZONE_DB_PATH" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
        BIZZONE_FINAL_DB_SIZE=0
    fi
fi

# Function to perform calculation with fallback
calculate() {
    local expr="$1"
    if command -v bc > /dev/null; then
        echo "scale=4; $expr" | bc
    else
        awk "BEGIN {printf \"%.4f\", $expr}"
    fi
}

# Calculate storage growth for FinZone if we have both initial and final measurements
if [ "$FINZONE_INITIAL_DB_SIZE" != "0" ] && [ "$FINZONE_FINAL_DB_SIZE" != "0" ]; then
    FINZONE_DB_SIZE_GROWTH=$(calculate "$FINZONE_FINAL_DB_SIZE - $FINZONE_INITIAL_DB_SIZE" | awk '{printf "%.2f", $0}')
    echo "FinZone DB size growth: $FINZONE_DB_SIZE_GROWTH bytes" | tee -a "$FINZONE_STORAGE_REPORT_FILE"

    # Calculate blocks added for FinZone
    FINZONE_BLOCKS_ADDED=$((FINZONE_END_BLOCK - FINZONE_START_BLOCK))
    echo "FinZone blocks added: $FINZONE_BLOCKS_ADDED" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
fi

# Calculate storage growth for BizZone if we have both initial and final measurements
if [ "$BIZZONE_INITIAL_DB_SIZE" != "0" ] && [ "$BIZZONE_FINAL_DB_SIZE" != "0" ]; then
    BIZZONE_DB_SIZE_GROWTH=$(calculate "$BIZZONE_FINAL_DB_SIZE - $BIZZONE_INITIAL_DB_SIZE" | awk '{printf "%.2f", $0}')
    echo "BizZone DB size growth: $BIZZONE_DB_SIZE_GROWTH bytes" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"

    # Calculate blocks added for BizZone
    BIZZONE_BLOCKS_ADDED=$((BIZZONE_END_BLOCK - BIZZONE_START_BLOCK))
    echo "BizZone blocks added: $BIZZONE_BLOCKS_ADDED" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
fi

# Step 4: Run block_size_calculator.sh with the start and end block numbers for both zones
echo "Step 4: Running block_size_calculator.sh for FinZone with blocks $FINZONE_START_BLOCK to $FINZONE_END_BLOCK..."
# Set environment variables for FinZone
export BLOCK_DATA_DIR="block_data/finzone"
export WS_URL="$FinZone_WS_URL"
./utils/block_size_calculator.sh $FINZONE_START_BLOCK $FINZONE_END_BLOCK

echo "Step 4: Running block_size_calculator.sh for BizZone with blocks $BIZZONE_START_BLOCK to $BIZZONE_END_BLOCK..."
# Set environment variables for BizZone
export BLOCK_DATA_DIR="block_data/bizzone"
export WS_URL="$BizZone_WS_URL"
./utils/block_size_calculator.sh $BIZZONE_START_BLOCK $BIZZONE_END_BLOCK

# Step 5: Run analyze_rlp_blocks.js to analyze the results for both zones
echo "Step 5: Running analyze_rlp_blocks.js to analyze the FinZone results..."
# Set environment variables for FinZone analysis
export BLOCK_DATA_DIR="block_data/finzone"
export RAW_DATA_FILE="block_data/finzone/all_blocks_rlp_raw_data.txt"
export RLP_ANALYSIS_FILE="block_data/finzone/rlp_analysis_report.txt"
node ./utils/analyze_rlp_blocks.js

echo "Step 5: Running analyze_rlp_blocks.js to analyze the BizZone results..."
# Set environment variables for BizZone analysis
export BLOCK_DATA_DIR="block_data/bizzone"
export RAW_DATA_FILE="block_data/bizzone/all_blocks_rlp_raw_data.txt"
export RLP_ANALYSIS_FILE="block_data/bizzone/rlp_analysis_report.txt"
node ./utils/analyze_rlp_blocks.js

# Clean up temporary file
rm $TEMP_FILE

# FinZone Summary
echo "=== FinZone Block Analysis Process Completed ===" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
echo "" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
echo "=== FinZone Summary ===" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
echo "Start Block: $FINZONE_START_BLOCK" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
echo "End Block: $FINZONE_END_BLOCK" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
echo "Total Blocks Added: $FINZONE_BLOCKS_ADDED" | tee -a "$FINZONE_STORAGE_REPORT_FILE"

# BizZone Summary
echo "=== BizZone Block Analysis Process Completed ===" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
echo "" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
echo "=== BizZone Summary ===" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
echo "Start Block: $BIZZONE_START_BLOCK" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
echo "End Block: $BIZZONE_END_BLOCK" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
echo "Total Blocks Added: $BIZZONE_BLOCKS_ADDED" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"

# Storage analysis summary for FinZone
if [ "$FINZONE_INITIAL_DB_SIZE" != "0" ] && [ -d "$FINZONE_DB_PATH" ]; then
    echo "" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
    echo "=== FinZone Storage Analysis Summary ===" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
    echo "Initial DB Size: $FINZONE_INITIAL_DB_SIZE bytes" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
    echo "Final DB Size: $FINZONE_FINAL_DB_SIZE bytes" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
    echo "DB Size Growth: $FINZONE_DB_SIZE_GROWTH bytes" | tee -a "$FINZONE_STORAGE_REPORT_FILE"

    # Calculate average storage per block if blocks were added
    if [ "$FINZONE_BLOCKS_ADDED" -gt 0 ]; then
        FINZONE_AVG_STORAGE_PER_BLOCK=$(calculate "$FINZONE_DB_SIZE_GROWTH / $FINZONE_BLOCKS_ADDED" | awk '{printf "%.2f", $0}')
        echo "Average Storage Per Block: $FINZONE_AVG_STORAGE_PER_BLOCK bytes" | tee -a "$FINZONE_STORAGE_REPORT_FILE"
    fi
fi

# Storage analysis summary for BizZone
if [ "$BIZZONE_INITIAL_DB_SIZE" != "0" ] && [ -d "$BIZZONE_DB_PATH" ]; then
    echo "" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
    echo "=== BizZone Storage Analysis Summary ===" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
    echo "Initial DB Size: $BIZZONE_INITIAL_DB_SIZE bytes" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
    echo "Final DB Size: $BIZZONE_FINAL_DB_SIZE bytes" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
    echo "DB Size Growth: $BIZZONE_DB_SIZE_GROWTH bytes" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"

    # Calculate average storage per block if blocks were added
    if [ "$BIZZONE_BLOCKS_ADDED" -gt 0 ]; then
        BIZZONE_AVG_STORAGE_PER_BLOCK=$(calculate "$BIZZONE_DB_SIZE_GROWTH / $BIZZONE_BLOCKS_ADDED" | awk '{printf "%.2f", $0}')
        echo "Average Storage Per Block: $BIZZONE_AVG_STORAGE_PER_BLOCK bytes" | tee -a "$BIZZONE_STORAGE_REPORT_FILE"
    fi
fi

# Print final summary to console
echo ""
echo "=== Analysis Complete ==="
echo "Analysis results are available in the following directories:"
echo ""
echo "FinZone Results:"
echo "- Storage analysis: block_data/finzone/storage_analysis_report.txt"
echo "- Block data: block_data/finzone/all_block_rlp_encoded_data.json"
echo "- Raw hex data: block_data/finzone/all_blocks_rlp_raw_data.txt"
echo "- Block summary: block_data/finzone/block_summary.json"
echo "- RLP analysis: block_data/finzone/rlp_analysis_report.txt"
echo ""
echo "BizZone Results:"
echo "- Storage analysis: block_data/bizzone/storage_analysis_report.txt"
echo "- Block data: block_data/bizzone/all_block_rlp_encoded_data.json"
echo "- Raw hex data: block_data/bizzone/all_blocks_rlp_raw_data.txt"
echo "- Block summary: block_data/bizzone/block_summary.json"
echo "- RLP analysis: block_data/bizzone/rlp_analysis_report.txt"
