#!/bin/bash

# Check if both arguments are provided
if [ $# -ne 2 ]; then
  echo "Usage: $0 START_BLOCK END_BLOCK"
  echo "Example: $0 10147 10194"
  exit 1
fi

# Get block range from command line arguments
START_BLOCK=$1
END_BLOCK=$2
TOTAL_SIZE=0
TOTAL_TXS=0

# Check if BLOCK_DATA_DIR environment variable is set
if [ -z "$BLOCK_DATA_DIR" ]; then
  # If not set, check if block_data directory exists in the parent directory, if not use the one in the current directory
  if [ -d "../block_data" ]; then
    BLOCK_DATA_DIR="../block_data"
  else
    BLOCK_DATA_DIR="./block_data"
  fi
fi

# Create the directory if it doesn't exist
mkdir -p "$BLOCK_DATA_DIR"

# Set WebSocket URL from environment variable if provided
if [ -z "$WS_URL" ]; then
  WS_URL="ws://127.0.0.1:18541"
fi

# Initialize the JSON array for all blocks
echo "{\"blocks\": [" > "$BLOCK_DATA_DIR/all_block_rlp_encoded_data.json"
first_block=true

echo "# Raw Block Data for Blocks $START_BLOCK to $END_BLOCK" > "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"
echo "" >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"

for ((block=$START_BLOCK; block<=$END_BLOCK; block++)); do
  # Convert decimal to hex
  hex_block=$(printf "0x%x" $block)

  # Print current block information
  echo "Processing Block $block ($hex_block)..."

  # Call RPC and get response for block data
  if [ "$first_block" = true ]; then
    first_block=false
  else
    echo "," >> "$BLOCK_DATA_DIR/all_block_rlp_encoded_data.json"
  fi

  # Start a new block object
  echo "{" >> "$BLOCK_DATA_DIR/all_block_rlp_encoded_data.json"
  echo "\"Block $block ($hex_block) \": [" >> "$BLOCK_DATA_DIR/all_block_rlp_encoded_data.json"

  # Get block data and save to JSON file
  echo "  Getting block data..."
  block_data=$(wscat -c $WS_URL \
               -x "{\"jsonrpc\":\"2.0\",\"method\":\"eth_getBlockByNumber\",\"params\":[\"$hex_block\",true],\"id\":1}" \
               | jq '.result')

  # Write the block data to the file
  echo "$block_data" >> "$BLOCK_DATA_DIR/all_block_rlp_encoded_data.json"

  # Extract block size and transaction count
  if command -v jq &> /dev/null; then
    # We already have the block data, so we can extract from it
    block_size=$(echo "$block_data" | jq -r '.size')
    tx_count=$(echo "$block_data" | jq -r '.transactions | length')

    # If block_size is a number (not "N/A"), add to total
    if [[ "$block_size" != "N/A" && "$block_size" != "null" ]]; then
      # Remove "0x" prefix if present and convert from hex to decimal
      if [[ "$block_size" == 0x* ]]; then
        decimal_size=$((16#${block_size:2}))
      else
        decimal_size=$block_size
      fi
      TOTAL_SIZE=$((TOTAL_SIZE + decimal_size))
      TOTAL_TXS=$((TOTAL_TXS + tx_count))

      echo "  Block size = $decimal_size bytes"
      echo "  Transaction count = $tx_count"
    fi
  fi

  # Get raw block data
  echo "  Getting raw block data..."

  # Save the command and raw response to the file
  echo "Block $block ($hex_block): " >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"
  echo "wscat -c $WS_URL -x '{\"jsonrpc\":\"2.0\", \"method\":\"debug_getRawBlock\", \"params\":[\"$hex_block\"], \"id\":1}'" >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"
  echo "]" >> "$BLOCK_DATA_DIR/all_block_rlp_encoded_data.json"
  echo "}" >> "$BLOCK_DATA_DIR/all_block_rlp_encoded_data.json"

  # Execute the command and save the full response
  raw_block_data=$(wscat -c $WS_URL \
                   -x "{\"jsonrpc\":\"2.0\",\"method\":\"debug_getRawBlock\",\"params\":[\"$hex_block\"],\"id\":1}")

  # Check if the response is longer than 40000 characters and split it if necessary
  if [ ${#raw_block_data} -gt 40000 ]; then
    # Split the response into chunks of 40000 characters
    # First, extract the result part (the hex data)
    result_part=$(echo "$raw_block_data" | grep -o '"result":"[^"]*"')
    prefix_part=$(echo "$raw_block_data" | grep -o '^{[^"]*"jsonrpc":"[^"]*","id":[^,]*,')

    # Extract just the hex data without the "result":"" wrapper
    hex_data=$(echo "$result_part" | grep -o '"result":"[^"]*"' | sed 's/"result":"//;s/"$//')

    # Write the prefix part
    echo -n "$prefix_part" >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"
    echo -n '"result":"' >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"

    # Split the hex data into chunks and write them with newlines
    remaining=${#hex_data}
    position=0
    chunk_size=40000

    while [ $remaining -gt 0 ]; do
      # Calculate how many characters to take in this chunk
      if [ $remaining -lt $chunk_size ]; then
        take=$remaining
      else
        take=$chunk_size
      fi

      # Extract and write the chunk
      chunk=${hex_data:$position:$take}
      echo -n "$chunk" >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"

      # If there's more data, add a newline
      position=$((position + take))
      remaining=$((remaining - take))
      if [ $remaining -gt 0 ]; then
        echo "" >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"
      fi
    done

    # Close the JSON string and object
    echo '"' >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"
    echo "}" >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"
  else
    # If the response is not too long, save it as is
    echo "$raw_block_data" >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"
  fi

  echo "" >> "$BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"

  # Extract the raw data result for calculations
  raw_data=$(echo "$raw_block_data" | jq -r '.result')

  # Wait a bit to avoid overloading
  sleep 0.5
done

# Close the last block array and object
echo "]" >> "$BLOCK_DATA_DIR/all_block_rlp_encoded_data.json"
echo "}" >> "$BLOCK_DATA_DIR/all_block_rlp_encoded_data.json"

# Create a separate summary file
echo "{
  \"summary\": {
    \"start_block\": $START_BLOCK,
    \"end_block\": $END_BLOCK,
    \"total_size\": $TOTAL_SIZE,
    \"average_block_size\": $(echo "scale=2; $TOTAL_SIZE / ($END_BLOCK - $START_BLOCK + 1)" | bc),
    \"total_transactions\": $TOTAL_TXS,
    \"average_transactions\": $(echo "scale=2; $TOTAL_TXS / ($END_BLOCK - $START_BLOCK + 1)" | bc)
  }
}" > "$BLOCK_DATA_DIR/block_summary.json"

# Print summary
echo "======================================="
echo "Summary of Block Analysis:"
echo "---------------------------------------"
echo "Block range: $START_BLOCK to $END_BLOCK"
echo "Total blocks: $(($END_BLOCK - $START_BLOCK + 1))"
echo "Total size: $TOTAL_SIZE bytes"
echo "Average block size: $(echo "scale=2; $TOTAL_SIZE / ($END_BLOCK - $START_BLOCK + 1)" | bc) bytes"
echo "Total transactions: $TOTAL_TXS"
echo "Average transactions per block: $(echo "scale=2; $TOTAL_TXS / ($END_BLOCK - $START_BLOCK + 1)" | bc)"
echo "---------------------------------------"
echo "Output files:"
echo "- Block data: $BLOCK_DATA_DIR/all_block_rlp_encoded_data.json"
echo "- Raw hex data: $BLOCK_DATA_DIR/all_blocks_rlp_raw_data.txt"
echo "- Summary data: $BLOCK_DATA_DIR/block_summary.json"
echo "======================================="