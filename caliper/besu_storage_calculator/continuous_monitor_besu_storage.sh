#!/bin/bash

CONTAINER_ID="a3777dc9ed1c"  # Your zone1_besu1 container ID
DB_PATH="/mnt/data/database" # Database directory
INTERVAL=2                   # Check every 2 seconds
DURATION=60                # Monitor for 66 seconds
BESU_WS_URL="ws://127.0.0.1:18541"  # Besu WebSocket URL
TEMP_FILE="/tmp/besu_block_number.json"  # Temporary file for wscat output

# Check if wscat is installed
if ! command -v wscat &> /dev/null; then
    echo "Error: wscat is not installed. Please install it using 'npm install -g wscat'."
    exit 1
fi

echo "=== Continuous Besu Storage Monitor ==="
echo "This script will monitor storage changes in your Besu node for $DURATION seconds."
echo "Checking every $INTERVAL seconds."
echo

# Function to get sizes in bytes for precise comparison
get_sizes() {
    # Get sizes in bytes for precise comparison
    DB_SIZE_BYTES=$(docker exec $CONTAINER_ID du -b -s $DB_PATH | awk '{print $1}')

    # Get human-readable sizes for display
    DB_SIZE_HUMAN=$(docker exec $CONTAINER_ID du -h -s $DB_PATH | awk '{print $1}')
}

# Function to get the current block number using WebSocket
get_block_number() {
    # Use wscat with -x parameter to send the request and get the response
    wscat -c $BESU_WS_URL -x '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' > $TEMP_FILE 2>$TEMP_FILE.err

    # Check if there was an error
    if [ $? -ne 0 ]; then
        echo "Error connecting to WebSocket. See error log below:" >&2
        cat $TEMP_FILE.err >&2
        return 1
    fi

    # Extract the block number from the response and convert from hex to decimal
    if grep -q "result" $TEMP_FILE; then
        BLOCK_NUMBER=$(cat $TEMP_FILE | grep -o '"result":"0x[^"]*"' | cut -d'"' -f4 | xargs printf "%d\n" 2>/dev/null)

        # Check if conversion was successful
        if [ -z "$BLOCK_NUMBER" ]; then
            echo "Failed to parse block number from response." >&2
            return 1
        fi

        # Return only the block number (no debug messages)
        echo $BLOCK_NUMBER
    else
        echo "No 'result' field found in the response." >&2
        return 1
    fi
}

# Get initial block number
echo "Getting initial block number..."
INITIAL_BLOCK_NUMBER=$(get_block_number)
if [ $? -ne 0 ]; then
    echo "Warning: Failed to get initial block number. Continuing without block number tracking."
    INITIAL_BLOCK_NUMBER="unknown"
else
    echo "Successfully retrieved initial block number: $INITIAL_BLOCK_NUMBER"
fi

# Check initial sizes and block number
get_sizes
INITIAL_DB_SIZE=$DB_SIZE_BYTES
LAST_DB_SIZE=$INITIAL_DB_SIZE

echo "Initial block number: $INITIAL_BLOCK_NUMBER"
echo "Initial database size: $DB_SIZE_HUMAN ($DB_SIZE_BYTES bytes)"
echo
echo "Starting continuous monitoring. Send your transactions at any time."
echo "Press Ctrl+C to stop monitoring."
echo
echo "Time | Database | DB Change"
echo "---------------------------------------"

# Start time
START_TIME=$(date +%s)
END_TIME=$((START_TIME + DURATION))

# Monitor loop
CURRENT_TIME=$(date +%s)
while [ $CURRENT_TIME -lt $END_TIME ]; do
    # Sleep for the interval
    sleep $INTERVAL

    # Update current time
    CURRENT_TIME=$(date +%s)

    # Check if this is the last iteration
    IS_LAST_ITERATION=false
    if [ $CURRENT_TIME -ge $END_TIME ]; then
        IS_LAST_ITERATION=true

        # Get final block number right before the last storage check
        echo "Getting final block number..."
        FINAL_BLOCK_NUMBER=$(get_block_number)
        if [ $? -ne 0 ]; then
            echo "Warning: Failed to get final block number. Continuing without block number tracking."
            FINAL_BLOCK_NUMBER="unknown"
        else
            echo "Successfully retrieved final block number: $FINAL_BLOCK_NUMBER"
        fi
    fi

    # Get current sizes
    get_sizes
    CURRENT_DB_SIZE=$DB_SIZE_BYTES

    # Calculate changes since last check
    DB_DIFF=$((CURRENT_DB_SIZE - LAST_DB_SIZE))

    # Calculate total changes
    TOTAL_DB_DIFF=$((CURRENT_DB_SIZE - INITIAL_DB_SIZE))

    # Format the changes with + or - sign
    if [ $DB_DIFF -ne 0 ]; then
        ELAPSED=$(($(date +%s) - START_TIME))
        printf "%3ds | %8s | %+9d\n" $ELAPSED $DB_SIZE_HUMAN $DB_DIFF
    fi

    # Update last sizes
    LAST_DB_SIZE=$CURRENT_DB_SIZE

    # Break the loop if this was the last iteration
    if [ "$IS_LAST_ITERATION" = true ]; then
        break
    fi
done

echo
echo "=== Summary ==="
echo "Monitoring period: $DURATION seconds"
echo "Initial block number: $INITIAL_BLOCK_NUMBER"
echo "Final block number: $FINAL_BLOCK_NUMBER"

# Calculate blocks added only if both block numbers are known
if [ "$INITIAL_BLOCK_NUMBER" != "unknown" ] && [ "$FINAL_BLOCK_NUMBER" != "unknown" ]; then
    BLOCK_DIFF=$((FINAL_BLOCK_NUMBER - INITIAL_BLOCK_NUMBER))
    echo "Blocks added: $BLOCK_DIFF"
else
    echo "Blocks added: unknown (could not retrieve block numbers)"
fi

echo "Total database change: $(printf "%+d" $TOTAL_DB_DIFF) bytes"

if [ $TOTAL_DB_DIFF -eq 0 ]; then
    echo
    echo "No storage changes detected during the monitoring period."
fi

# Clean up temporary files
rm -f $TEMP_FILE $TEMP_FILE.err
