# Caliper測定ガイド

## テストのみ利用時

    多くの場合には、既存ネットワークを測定するため、テストのみの利用方法を記載する

設定内容は下記の三つ：

※ **Templateファイルは /benchmarks/templatesに、具体的な例は/scenarioに確認ください**

* Network Setting
* Benchmarks Setting
* Workloads Setting

実行コマンド：

WorkloadsファイルはBechmarks SettingのModuleに設定しているため、実行時はNetwork SettingとBenchmarks Settingを利用

``` text
npx caliper launch manager \
  --caliper-flow-only-test=true \
  --caliper-benchconfig dir/to/config.yaml \
  --caliper-networkconfig dir/to/networkconfig.json \
  --caliper-workspace .
```

### 【１】Network Setting

`networkconfig.json` ファイルを作成し、下記のアイテムを編集

※保存場所は任意だが、コマンド実行時に指定すればいい

本Repoの例だと、`/networks`配下に作成している

.

環境に合わせてnetworkconfigを編集：

※STUは事前に立ち上げたBesu networkを指す

1. `url`: SUTのport情報を提供

    ```
    "ethereum": {
            "url": "ws://127.0.0.1:18541", // caliperではWebSocketしか利用できないため注意
            ...
    }

2. `contracts`: `contract address`, `abi` (測定methodのみ), `method gas` (測定methodのみ)

    ```
    "ethereum": {
        "Token": {
                    "address": "******************************************",
                    "estimateGas": true,
                    "gas": {
                        "transfer": 70000,
                        "transferBatch":100000
                    },
                    "abi": [
                        {
                        "inputs": [
                            {
                            "internalType": "uint256",
                            "name": "sendAccountId",
                            "type": "uint256"
                            },
                            ...
                            {
                            "internalType": "bytes32",
                            "name": "miscValue2",
                            "type": "bytes32"
                            }
                        ],
                        "name": "transferSingle",
                        "outputs": [],
                        "stateMutability": "nonpayable",
                        "type": "function"
                        }

### 【２】Benchmarks Setting

`workload_config.yaml` ファイルを作成し、下記のアイテムを編集

※保存場所は任意だが、コマンド実行時に指定すればいい

本Repoの例だと、`/benchmarks/scenario` 配下に作成している

.
テスト状況に合わせて`workload_config.yaml`を編集：

設定可能なものは:

* workers: round単位で実行するworker数、例えばrounds 10を作成して、workders 2に設定すると二人が10 roundsの測定を並列で測定する

``` text
workers: 
    type: local
    number: 1
```

* rounds: 測定タスクの単位、roundないで測定できるものは
  * txNumber: roundの送信合計数

  ``` text
    - label: workload-round1
        description: round description.
        txNumber: 1
  ```

  * rateControl: 送信tpsの設定、送信rateのtype設定

  ``` text
    rateControl:
        type: fixed-rate
        opts:
        tps: 1
  ```

  * workload: 測定Moduleの指定（本SampleはContractの関数ごとでModuleを作成している）

  ``` text
    simpleArgs: &params
    param_name: data
    workload:
        module: benchmarks/scenario/<workload>.js
        // workload moduleの中で使えるparamsも設定できる
        arguments:
            <<: *params
  ```

### 【３】Workload Setting

`workload.js` ファイルを作成し、下記のアイテムを編集

※保存場所は任意だが、コマンド実行時に指定すればいい

本Repoの例だと、`/benchmarks/scenario` 配下に作成している

.

Contractの関数の状況に合わせて`workload.js`を編集：

`/benchmarks/templates/workload_template.js`の利用はパラメータだけ編集

``` javascript
/**
 * Custom parameters setting
 */
getParameters() {
    const txs = [
        // Custom paramaters
    ];
    return txs
}
```

・

**Workload Examples**

関数によってはパラメータの設定方法が工夫する必要がある

1. Transfer系関数の場合

   * 送受信アカウントが固定にしても送金ができるため、パラメータが定数で問題がない
   * Transfer金額が0でも送金ができるため、金額のMintがなくても問題がない

   これによってテストのパラメータ準備が簡単になる

   `workload_config.yaml`のパラメータを直接設定すれば作成できる

   ※ コード詳細は`/benchmarks/scenario/transfer`にある例を参考ください、この例にはTemplate利用していないが、同じように`utils/simple-sates.js`パラメータを作成している

   ``` test
     // /benchmarks/scenario/transfer/config.yaml
     simpleArgs: &simple-args
     fromAccountId: 300
     moneyToTransfer: 0
     toAccountId: 302
   ```

   ``` javascript
       // /benchmarks/scenario/transfer/config.yaml
       /**
        * Initializes the instance.
        */
       constructor(fromAccountId, moneyToTransfer, toAccountId) {
           this.fromAccountId = fromAccountId;
           this.moneyToTransfer = moneyToTransfer;
           this.toAccountId = toAccountId;
       }
   ```

1. Register系関数の場合

   * 登録するアカウントのIDが全てのTxに異なる必要がある
   * 登録関数にAccessControlが必要なので、Signatureの作成が必要

   これによってテストのパラメータ準備が簡単になる

   `workload_config.yaml`のパラメータを直接設定すれば作成できる

   ※ コード詳細は`/benchmarks/scenario/register`にある例を参考ください、この例にはTemplate利用し、`addIssuer.js`, `addValidator.js`, `addAccount.js`で各自のパラメータを作成している

   ``` javascript
    // 例でaddIssuerのみ添付
    // /benchmarks/scenario/register/addIssuer.js

    /**
     * Custom parameters setting
     */
    getParameters() {
        const txs = [...Array(this.roundArguments.count).keys()].map((v) => {
            // 各txに送っているIdが異なる
            // configからのparameter: countは1Roundに送るtxを設定
            // roundIndexは各Roundの設定(configのRoundにより自動設定：0~)
            // countとroundIndexでIdを重複しないように算出
            const issuerId = this.roundArguments.issuerId + v + (this.roundIndex * this.roundArguments.count);
            // `test`stringが変換ごの内容
            const issuerName =  "0x74657374";
            // 署名時間
            const deadline = Math.floor(Date.now() / 1000);
            // Admin key
            const privAdm = 'c87509a1c067bbde78beb793e6fa76530b6382a4c0241e5e4a9ec0a0f44dc0d3';

            const Args = [
                issuerId,
                issuerName,
                deadline,
                Tools.extSign(
                    privAdm,
                    ["uint256", "bytes32", "uint256"],
                    [issuerId, issuerName, deadline]
                )[0]
            ];
            return {
                // Contract名
                contract: 'Issuer',
                // Method名
                verb: 'addIssuer',
                // Parameters
                args: Args
            }
        })
        return txs
    }
   ```

    各Round、各Txにパラメータが異なることを実現するためのパラメータはConfigファイルから設定

    * issuerId: 登録の開始ID、＋1で増やしている
    * count: 1 roundに登録するアカウント数

    注意：

    count設定が`txNumber`の代わりになるため、`txNumber`を`1`に固定する

   ``` test
     // /benchmarks/scenario/register/addIssuer_config.yaml
    simpleArgs: &params
    issuerId: 3000
    count: 100
   ```
