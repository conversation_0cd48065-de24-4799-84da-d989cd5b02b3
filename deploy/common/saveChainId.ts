import fs from 'fs'
import { ethers, network } from 'hardhat'
import path from 'path'

// chainIdを保存
export const saveChainId = async () => {
  const chainId = await ethers.provider.getNetwork().then((network) => network.chainId)
  const deploymentDir = path.join(__dirname, `../../deployments/${network.name}`)
  if (!fs.existsSync(deploymentDir)) {
    fs.mkdirSync(deploymentDir, { recursive: true })
  }
  const chainIdFilePath = path.join(deploymentDir, '.chainId')
  fs.writeFileSync(chainIdFilePath, chainId.toString(), 'utf-8')
  console.log(`Chain ID ${chainId} saved to ${chainIdFilePath}`)
}
