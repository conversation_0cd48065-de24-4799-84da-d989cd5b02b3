/**
 * Hardhat Deployment Script
 * 1. ネットワーク: 'local'と'main'を'hardhat.config.ts'で設定
 * 2. ウォレット: 'local'の場合、'admin'と'deployer'はデフォルトで設定されるプライベートキーを利用する
 * 3. デプロイライブラリ: 'hardhat-deploy'を利用し、Proxyとして'OpenZeppelinTransparentProxy'を設定
 */
import { showDeployNetwork, showDeploySuccessWithMsg } from '@deploy/common/consoles'
import { deployContractWithSaveABI } from '@deploy/common/deployContractWithSaveABI'
import { kmsSignerProvider } from '@deploy/common/kmsSignerProvider'
import { saveChainId } from '@deploy/common/saveChainId'
import * as Tools from '@tasks/common/tools'
import { Contract } from 'ethers'
import { ethers } from 'hardhat'
import { DeployFunction } from 'hardhat-deploy/types'
import { HardhatRuntimeEnvironment } from 'hardhat/types'

// 라이브러리 주소를 Contract 인스턴스로 변환하는 헬퍼 함수
async function getContractInstance(
  contractName: string,
  contractAddress: string | unknown,
  signer: any,
): Promise<Contract> {
  if (!contractAddress || typeof contractAddress !== 'string') {
    throw new Error(`Invalid contract address for ${contractName}`)
  }
  return await ethers.getContractAt(contractName, contractAddress, signer)
}

const func: DeployFunction = async (hre: HardhatRuntimeEnvironment) => {
  // 먼저 .chainId 파일 생성 (배포 폴더에 chainId 파일이 없는 경우를 대비)
  try {
    await saveChainId()
    console.log('.chainId 파일이 생성되었습니다.')
  } catch (error) {
    console.error('.chainId 파일 생성 중 오류 발생:', error)
  }

  // ethers.js v6のgetContractFactoryを利用してdeployを行う
  // getContractFactory()の引数には送信者を設定するsigner, Link対象コントラクトを設定するlibrariesが存在する
  // Deployの順番は前後順番があるので注意する：(1)Library系(Errorなど) (2)ContractManager (3)Others
  showDeployNetwork({ title: '' })

  // SetContractで利用するContract情報を入れるMap
  const contractMap = new Map()
  const kmsSigner = kmsSignerProvider()

  // nonce 재설정을 위한 코드 추가
  const currentNonce = await ethers.provider.getTransactionCount(await kmsSigner.getAddress())
  console.log(`Current nonce for deployer: ${currentNonce}`)

  const deployerAddress = await kmsSigner.getAddress()
  console.log(`Deployer address: ${deployerAddress}`)

  // Deploy ContractManager
  const contractManagerContract = await deployContractWithSaveABI({
    contractName: 'ContractManager',
    options: { signer: kmsSigner },
    saveABIflag: true,
  })
  contractMap.set('ContractManager', contractManagerContract.target)

  // Deploy Error
  const errorContract = await deployContractWithSaveABI({
    contractName: 'Error',
    options: { signer: kmsSigner },
    saveABIflag: true,
  })
  contractMap.set('Error', errorContract.target)

  // Deploy ProviderLib
  const ProviderLibContract = await deployContractWithSaveABI({
    contractName: 'ProviderLib',
    options: { signer: kmsSigner },
  })
  contractMap.set('ProviderLib', ProviderLibContract.target)

  // Deploy IssuerLib
  const IssuerLibContract = await deployContractWithSaveABI({
    contractName: 'IssuerLib',
    options: { signer: kmsSigner },
  })
  contractMap.set('IssuerLib', IssuerLibContract.target)

  // Deploy ValidatorLib
  const ValidatorLibContract = await deployContractWithSaveABI({
    contractName: 'ValidatorLib',
    options: { signer: kmsSigner },
  })
  contractMap.set('ValidatorLib', ValidatorLibContract.target)

  // Deploy TokenLib
  const TokenLibContract = await deployContractWithSaveABI({
    contractName: 'TokenLib',
    options: { signer: kmsSigner },
  })
  contractMap.set('TokenLib', TokenLibContract.target)

  // Deploy AccountLib
  const AccountLibContract = await deployContractWithSaveABI({
    contractName: 'AccountLib',
    options: { signer: kmsSigner },
  })
  contractMap.set('AccountLib', AccountLibContract.target)

  // Deploy FinancialZoneAccountLib
  const FinancialZoneAccountLibContract = await deployContractWithSaveABI({
    contractName: 'FinancialZoneAccountLib',
    options: { signer: kmsSigner },
  })
  contractMap.set('FinancialZoneAccountLib', FinancialZoneAccountLibContract.target)

  // Deploy BusinessZoneAccountLib
  const BusinessZoneAccountLibContract = await deployContractWithSaveABI({
    contractName: 'BusinessZoneAccountLib',
    options: { signer: kmsSigner },
  })
  contractMap.set('BusinessZoneAccountLib', BusinessZoneAccountLibContract.target)

  // Deploy RemigrationLib
  const remigrationLibContract = await deployContractWithSaveABI({
    contractName: 'RemigrationLib',
    options: { signer: kmsSigner },
  })
  contractMap.set('RemigrationLib', remigrationLibContract.target)

  // 라이브러리 컨트랙트 인스턴스 생성
  const providerLibInstance = await getContractInstance('ProviderLib', contractMap.get('ProviderLib'), kmsSigner)
  const issuerLibInstance = await getContractInstance('IssuerLib', contractMap.get('IssuerLib'), kmsSigner)
  const validatorLibInstance = await getContractInstance('ValidatorLib', contractMap.get('ValidatorLib'), kmsSigner)
  const accountLibInstance = await getContractInstance('AccountLib', contractMap.get('AccountLib'), kmsSigner)
  const financialZoneAccountLibInstance = await getContractInstance(
    'FinancialZoneAccountLib',
    contractMap.get('FinancialZoneAccountLib'),
    kmsSigner,
  )
  const businessZoneAccountLibInstance = await getContractInstance(
    'BusinessZoneAccountLib',
    contractMap.get('BusinessZoneAccountLib'),
    kmsSigner,
  )
  const tokenLibInstance = await getContractInstance('TokenLib', contractMap.get('TokenLib'), kmsSigner)
  const remigrationLibInstance = await getContractInstance(
    'RemigrationLib',
    contractMap.get('RemigrationLib'),
    kmsSigner,
  )

  // Deploy AccessCtrl
  const accessCtrlContract = await deployContractWithSaveABI({
    contractName: 'AccessCtrl',
    options: {
      signer: kmsSigner,
    },
    initialize: {
      args: [contractMap.get('ContractManager'), deployerAddress],
    },
    saveABIflag: true,
  })
  contractMap.set('AccessCtrl', accessCtrlContract.target)

  // Deploy Provider
  const providerContract = await deployContractWithSaveABI({
    contractName: 'Provider',
    options: {
      signer: kmsSigner,
      libraries: {
        ProviderLib: providerLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('Provider', providerContract.target)

  // Deploy Issuer
  const issuerContract = await deployContractWithSaveABI({
    contractName: 'Issuer',
    options: {
      signer: kmsSigner,
      libraries: {
        IssuerLib: issuerLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('Issuer', issuerContract.target)

  // Deploy Validator
  const validatorContract = await deployContractWithSaveABI({
    contractName: 'Validator',
    options: {
      signer: kmsSigner,
      libraries: {
        ValidatorLib: validatorLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('Validator', validatorContract.target)

  // Deploy Account
  const accountContract = await deployContractWithSaveABI({
    contractName: 'Account',
    options: {
      signer: kmsSigner,
      libraries: {
        AccountLib: accountLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('Account', accountContract.target)

  // Deploy FinancialZoneAccount
  const financialZoneAccountContract = await deployContractWithSaveABI({
    contractName: 'FinancialZoneAccount',
    options: {
      signer: kmsSigner,
      libraries: {
        FinancialZoneAccountLib: financialZoneAccountLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('FinancialZoneAccount', financialZoneAccountContract.target)

  // Deploy BusinessZoneAccount
  const businessZoneAccountContract = await deployContractWithSaveABI({
    contractName: 'BusinessZoneAccount',
    options: {
      signer: kmsSigner,
      libraries: {
        BusinessZoneAccountLib: businessZoneAccountLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('BusinessZoneAccount', businessZoneAccountContract.target)

  // Deploy Token
  const tokenContract = await deployContractWithSaveABI({
    contractName: 'Token',
    options: {
      signer: kmsSigner,
      libraries: {
        TokenLib: tokenLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('Token', tokenContract.target)

  // Deploy IBCToken
  const ibcTokenContract = await deployContractWithSaveABI({
    contractName: 'IBCToken',
    options: {
      signer: kmsSigner,
      libraries: {
        TokenLib: tokenLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('IBCToken', ibcTokenContract.target)

  // Deploy FinancialCheck
  const financialCheckContract = await deployContractWithSaveABI({
    contractName: 'FinancialCheck',
    options: {
      signer: kmsSigner,
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('FinancialCheck', financialCheckContract.target)

  // Deploy TransferProxy
  const transferProxyContract = await deployContractWithSaveABI({
    contractName: 'TransferProxy',
    options: {
      signer: kmsSigner,
    },
    initialize: {
      args: [contractMap.get('ContractManager'), contractMap.get('Token')],
    },
    saveABIflag: true,
  })
  contractMap.set('TransferProxy', transferProxyContract.target)

  // Deploy RemigrationRestore
  const remigrationRestoreContract = await deployContractWithSaveABI({
    contractName: 'RemigrationRestore',
    options: {
      signer: kmsSigner,
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('RemigrationRestore', remigrationRestoreContract.target)

  // Deploy RemigrationBackup
  const remigrationBackupContract = await deployContractWithSaveABI({
    contractName: 'RemigrationBackup',
    options: {
      signer: kmsSigner,
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
  })
  contractMap.set('RemigrationBackup', remigrationBackupContract.target)

  console.log('*************************************')
  console.log(`* Set contract addresses to ContractManager *`)
  console.log('*************************************')
  console.log('Setting all contract addresses to ContractManager...')

  const deadline = (await Math.floor(Date.now() / 1000)) + 10
  const types = [
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'address',
    'uint256',
  ]
  const data = [
    contractMap.get('AccessCtrl'),
    contractMap.get('Provider'),
    contractMap.get('Issuer'),
    contractMap.get('Validator'),
    contractMap.get('Account'),
    contractMap.get('FinancialZoneAccount'),
    contractMap.get('BusinessZoneAccount'),
    contractMap.get('Token'),
    contractMap.get('IBCToken'),
    contractMap.get('FinancialCheck'),
    contractMap.get('TransferProxy'),
    deadline,
  ]

  const signatureBytes = await kmsSigner.sign(types, data)

  const tx = await contractManagerContract.setContracts(
    {
      ctrlAddress: contractMap.get('AccessCtrl'),
      providerAddress: contractMap.get('Provider'),
      issuerAddress: contractMap.get('Issuer'),
      validatorAddress: contractMap.get('Validator'),
      accountAddress: contractMap.get('Account'),
      financialZoneAccountAddress: contractMap.get('FinancialZoneAccount'),
      businessZoneAccountAddress: contractMap.get('BusinessZoneAccount'),
      tokenAddress: contractMap.get('Token'),
      ibcTokenAddress: contractMap.get('IBCToken'),
      financialCheckAddress: contractMap.get('FinancialCheck'),
      transferProxyAddress: contractMap.get('TransferProxy'),
    },
    deadline,
    signatureBytes,
  )
  await tx
    .wait()
    .then((res) => {
      Tools.showEthersRes({ res })
    })
    .catch((error) => console.log(error)) // // Deploy情報をFormatしてPrintする

  // Contract set to manageContract情報をFormatしてPrintする
  showDeploySuccessWithMsg({
    deployerAddress,
    contractObj: {
      ContractManager: contractMap.get('ContractManager'),
      AccessCtrl: contractMap.get('AccessCtrl'),
      Provider: contractMap.get('Provider'),
      Issuer: contractMap.get('Issuer'),
      Validator: contractMap.get('Validator'),
      Account: contractMap.get('Account'),
      FinancialZoneAccount: contractMap.get('FinancialZoneAccount'),
      BusinessZoneAccount: contractMap.get('BusinessZoneAccount'),
      Token: contractMap.get('Token'),
      IBCToken: contractMap.get('IBCToken'),
      FinancialCheck: contractMap.get('FinancialCheck'),
      TransferProxy: contractMap.get('TransferProxy'),
      ErrorLib: contractMap.get('Error'),
      ProviderLib: contractMap.get('ProviderLib'),
      IssuerLib: contractMap.get('IssuerLib'),
      ValidatorLib: contractMap.get('ValidatorLib'),
      AccountLib: contractMap.get('AccountLib'),
      FinancialZoneAccountLib: contractMap.get('FinancialZoneAccountLib'),
      BusinessZoneAccountLib: contractMap.get('BusinessZoneAccountLib'),
      ADMIN: deployerAddress,
    },
  })

  // chainIdを保存
  await saveChainId()
}

export default func
func.tags = ['main-contracts']
