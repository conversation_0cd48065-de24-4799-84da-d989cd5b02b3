/**
 * Hardhat Deployment Script
 * 1. ネットワーク: 'local'と'main'を'hardhat.config.ts'で設定
 * 2. ウォレット: 'local'の場合、'admin'と'deployer'はデフォルトで設定されるプライベートキーを利用する
 * 3. デプロイライブラリ: 'hardhat-deploy'を利用し、Proxyとして'OpenZeppelinTransparentProxy'を設定
 */
import { envVers } from '@/envVers'
import { getAddressFromABI } from '@deploy/common/abiTools'
import { showDeployNetwork, showDeploySuccessWithMsg } from '@deploy/common/consoles'
import { deployContractWithSaveABI } from '@deploy/common/deployContractWithSaveABI'
import { kmsSignerProvider } from '@deploy/common/kmsSignerProvider'
import { saveChainId } from '@deploy/common/saveChainId'
import * as Tools from '@tasks/common/tools'
import { ethers, network } from 'hardhat'
import { DeployFunction } from 'hardhat-deploy/types'

const func: DeployFunction = async function () {
  // hardhat-deployのdeploy()関数を利用してdeployを行う
  // deploy()の引数には送信者アドレスを設定するfrom, Link対象コントラクトを設定するlink, proxyコントラクトの実行を指定できるproxy, 詳細なlogを表示させるlogが存在する
  // Deployの順番は前後順番があるので注意する：(1)Library系(Errorなど) (2)ContractManager (3)Others
  showDeployNetwork({ title: 'Bridges' })

  const kmsSigner = kmsSignerProvider()
  const deployerAddress = await kmsSigner.getAddress()

  // Deploy Error.sol
  // 他のContractがErrorをLinkするため、最初にDeployする
  const error = await deployContractWithSaveABI({
    contractName: 'Error',
    options: { signer: kmsSigner },
  })

  const ibcHandlerAddress = await getAddressFromABI({ network: network.name, contractName: 'OwnableIBCHandler' })
  console.log('ibcHandlerAddress:', ibcHandlerAddress)
  const ibcHandlerInstance = await ethers.getContractAt('OwnableIBCHandler', ibcHandlerAddress, kmsSigner)

  // Decide token address
  let tokenAddress
  let ibcTokenAddress
  let validatorAddress
  let accountAddress
  let accessCtrlAddress
  let businessZoneAccountAddress

  if (envVers.mode.test) {
    // Deploy TokenMock.sol
    await deployContractWithSaveABI({
      contractName: 'TokenMock',
    })

    // Deploy IBCTokenMock.sol
    const ibcTokenMock = await deployContractWithSaveABI({
      contractName: 'IBCTokenMock',
    })
    ibcTokenAddress = ibcTokenMock.target

    // Deploy ValidatorMock.sol
    const validatorMock = await deployContractWithSaveABI({
      contractName: 'ValidatorMock',
    })
    validatorAddress = validatorMock.target

    // Deploy AccountMock.sol
    const accountMock = await deployContractWithSaveABI({
      contractName: 'AccountMock',
    })
    accountAddress = accountMock.target

    // Deploy AccessCtrlMock.sol
    const accessCtrlMock = await deployContractWithSaveABI({
      contractName: 'AccessCtrlMock',
    })
    accessCtrlAddress = accessCtrlMock.target

    // Deploy BusinessZoneAccountMock.sol
    const businessZoneAccountMock = await deployContractWithSaveABI({
      contractName: 'BusinessZoneAccountMock',
    })
    businessZoneAccountAddress = businessZoneAccountMock.target
  } else if (network.name == 'localFin') {
    ibcTokenAddress = process.env.FIN_IBC_TOKEN_ADDRESS
    validatorAddress = process.env.FIN_VALIDATOR_ADDRESS
    accountAddress = process.env.FIN_ACCOUNT_ADDRESS
    accessCtrlAddress = process.env.FIN_ACCESS_CTRL_ADDRESS
    businessZoneAccountAddress = process.env.FIN_BUSINESS_ZONE_ACCOUNT_ADDRESS
  } else if (network.name == 'localBiz') {
    ibcTokenAddress = process.env.BIZ_IBC_TOKEN_ADDRESS
    validatorAddress = process.env.BIZ_VALIDATOR_ADDRESS
    accountAddress = process.env.BIZ_ACCOUNT_ADDRESS
    accessCtrlAddress = process.env.BIZ_ACCESS_CTRL_ADDRESS
    businessZoneAccountAddress = process.env.BIZ_BUSINESS_ZONE_ACCOUNT_ADDRESS
  } else {
    ibcTokenAddress = process.env.IBC_TOKEN_ADDRESS
    validatorAddress = process.env.VALIDATOR_ADDRESS
    accountAddress = process.env.ACCOUNT_ADDRESS
    accessCtrlAddress = process.env.ACCESS_CTRL_ADDRESS
    businessZoneAccountAddress = process.env.BUSINESS_ZONE_ACCOUNT_ADDRESS
  }

  console.log(`Using
  IBCTokenAddress: ${ibcTokenAddress}
  ValidatorAddress: ${validatorAddress}
  AccountAddress: ${accountAddress}
  AccessCtrlAddress: ${accessCtrlAddress}
  BusinessZoneAccountAddress: ${businessZoneAccountAddress}
  to deploy Bridge Contracts.`)

  // Deploy JPYTokenTransferBridge.sol
  const jpyTokenTransferBridge = await deployContractWithSaveABI({
    contractName: 'JPYTokenTransferBridge',
    initialize: {
      args: [ibcHandlerInstance.target, ibcTokenAddress, accessCtrlAddress],
    },
    saveABIflag: true,
  })

  // Deploy BalanceSyncBridge.sol
  const balanceSyncBridge = await deployContractWithSaveABI({
    contractName: 'BalanceSyncBridge',
    initialize: {
      args: [ibcHandlerInstance.target, ibcTokenAddress, accountAddress, accessCtrlAddress],
    },
    saveABIflag: true,
  })

  // Deploy AccountSyncBridge.sol
  const accountSyncBridge = await deployContractWithSaveABI({
    contractName: 'AccountSyncBridge',
    initialize: {
      args: [
        ibcHandlerInstance.target,
        validatorAddress,
        accessCtrlAddress,
        businessZoneAccountAddress,
        ibcTokenAddress,
      ],
    },
    saveABIflag: true,
  })

  const accountSyncPortId = 'account-sync'
  const balanceSyncAppPortId = 'balance-sync'
  const tokenTransferAppPortId = 'token-transfer'

  console.log('Binding Bridge Contracts to IBC Handler...')
  // Bind contract address for each Bridge
  const accountSyncBindTx = await ibcHandlerInstance.bindPort(accountSyncPortId, accountSyncBridge.target)
  await accountSyncBindTx.wait().then((res) => Tools.showEthersRes({ res }))
  const balanceSyncBindTx = await ibcHandlerInstance.bindPort(balanceSyncAppPortId, balanceSyncBridge.target)
  await balanceSyncBindTx.wait().then((res) => Tools.showEthersRes({ res }))
  const tokenTransferBindTx = await ibcHandlerInstance.bindPort(tokenTransferAppPortId, jpyTokenTransferBridge.target)
  await tokenTransferBindTx.wait().then((res) => Tools.showEthersRes({ res }))

  // Deploy情報をFormatしてPrintする
  showDeploySuccessWithMsg({
    deployerAddress,
    contractObj: {
      OwnableIBCHandler: ibcHandlerInstance.target,
      AccountSyncBridge: accountSyncBridge.target,
      BalanceSyncBridge: balanceSyncBridge.target,
      JPYTokenTransferBridge: jpyTokenTransferBridge.target,
      ErrorLib: error.target,
    },
  })

  // chainIdを保存
  await saveChainId()
}

export default func
func.tags = ['ibc-contracts']
func.dependencies = ['yui-contracts']
