{"extends": "solhint:recommended", "plugins": ["dcf"], "rules": {"quotes": ["warn", "double"], "compiler-version": ["off", "v0.8.10"], "dcf/no-comment-format": "off", "dcf/one-contract-one-file": "off", "no-unused-vars": "off", "dcf/interface-no-struct": "off", "dcf/no-console-log": "off", "avoid-low-level-calls": "off", "avoid-call-value": "off", "avoid-sha3": "off", "avoid-suicide": "off", "avoid-throw": "off", "avoid-tx-origin": "off", "check-send-result": "off", "state-visibility": "off", "multiple-sends": "off", "no-complex-fallback": "off", "not-rely-on-block-hash": "off", "const-name-snakecase": "off", "contract-name-camelcase": "off", "event-name-camelcase": "off", "private-vars-leading-underscore": "off", "custom-errors": "off", "no-global-import": "off", "var-name-mixedcase": "off", "func-name-mixedcase": "off"}}