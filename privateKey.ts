// ganacheのデフォルト秘密鍵
import * as ethSigUtil from 'eth-sig-util'
import Web3 from 'web3'

const web3 = new Web3()

const privateKey = [
  // [0]:******************************************
  'ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80',
  // [1]:******************************************',
  '59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d',
  // [2]:******************************************',
  '5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a',
  // [3]:******************************************',
  '7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6',
  // [4]:******************************************',
  '47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a',
  // [5]:******************************************',
  '8b3a350cf5c34c9194ca85829a2df0ec3153be0318b5e2d3348e872092edffba',
  // [6]:******************************************',
  '92db14e403b83dfe3df233f83dfa3a0d7096f21ca9b0d6d6b8d88b2b4ec1564e',
  // [7]:0x14dC79964da2C08b23698B3D3cc7Ca32193d9955',
  '4bbbf85ce3377467afe5d46f804f221813b2bb87f24d81f60f1fcdbf7cbf4356',
  // [8]:0x23618e81E3f5cdF7f54C3d65f7FBc0aBf5B21E8f',
  'dbda1821b80551c9d65939329250298aa3472ba22feea921c0cf5d620ea67b97',
  // [9]:0xa0Ee7A142d267C1f36714E4a8F75612F20a79720',
  '2a871d0798f97d79848a013d4936a73bf4cc922c825d33c1cf7073dff6d409c6',
  // [10]:0xBcd4042DE499D14e55001CcbB24a551F3b954096',
  'f214f2b2cd398c806f84e317254e0f0b801d0643303237d97a22a48e01628897',
  // [11]:0x71bE63f3384f5fb98995898A86B02Fb2426c5788',
  '701b615bbdfb9de65240bc28bd21bbc0d996645a3dd57e7b12bc2bdf6f192c82',
  // [12]:0xFABB0ac9d68B0B445fB7357272Ff202C5651694a',
  'a267530f49f8280200edf313ee7af6b827f2a8bce2897751d06a843f644967b1',
  // [13]:0x1CBd3b2770909D4e10f157cABC84C7264073C9Ec',
  '47c99abed3324a2707c28affff1267e45918ec8c3f20b8aa892e8b065d2942dd',
  // [14]:0xdF3e18d64BC6A983f673Ab319CCaE4f1a57C7097',
  'c526ee95bf44d8fc405a158bb884d9d1238d99f0612e9f33d006bb0789009aaa',
  // [15]:0xcd3B766CCDd6AE721141F452C550Ca635964ce71',
  '8166f546bab6da521a8369cab06c5d2b9e46670292d85c875ee9ec20e84ffb61',
  // [16]:0x2546BcD3c84621e976D8185a91A922aE77ECEc30',
  'ea6c44ac03bff858b476bba40716402b03e41b8e97e276d1baec7c37d42484a0',
  // [17]:******************************************',
  '689af8efa8c651a91ad287602527f3af2fe9f6501a7ac4b061667b5a93e037fd',
  // [18]:******************************************',
  'de9be858da4a475276426320d5e9262ecfc3ba460bfac56360bfa6c4c28b4ee0',
  // [19]:******************************************'
  'df57089febbacf7ba0bc227dafbffa9fc08a93fdc68e1e42411a14efcf23656e',
]

export function sigParam(privateKey, typesArray, parameters) {
  const abi = web3.eth.abi.encodeParameters(typesArray, parameters)
  const hash = web3.utils.keccak256(abi)
  return [ethSigUtil.personalSign(Buffer.from(privateKey, 'hex'), { data: hash }), hash]
}

export const MNEMONIC = 'candy maple cake sugar pudding cream honey rich smooth crumble sweet treat'
export const key = privateKey
export const sig = sigParam

export default {
  MNEMONIC,
  key,
  sig,
}
