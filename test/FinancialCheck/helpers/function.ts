import privateKey from '@/privateKey'
import { BASE } from '@test/common/consts'
import { EventReturnType } from '@test/common/types'
import * as utils from '@test/common/utils'
import {
  CheckExchangeType,
  CheckFinAccountStatusType,
  CheckSyncAccountType,
  CheckTransactionType,
  FinancialCheckType,
  GetAccountLimitType,
  GetBizZoneAccountStatusType,
} from './types'

/**
 * FinancialCheckのイベントを呼ぶ関数を持つobject
 */
export const financialCheckFuncs = {
  version: ({ financialCheck }: FinancialCheckType) => {
    return financialCheck.version()
  },
  checkTransaction: async ({
    financialCheck,
    zoneId,
    sendAccountId,
    fromAccountId,
    toAccountId,
    amount,
    sigInfo,
    options = {},
    tokenOptions = {},
  }: CheckTransactionType) => {
    const { accountSignature, eoaKey = BASE.EOA.VALID1 } = options
    const { miscValue1 = utils.toBytes32('0'), miscValue2 = '0' } = tokenOptions
    const _accountSignature =
      accountSignature ??
      privateKey.sig(
        sigInfo.signer,
        ['bytes32', 'bytes32', 'bytes32', 'uint256', 'uint256'],
        [sendAccountId, fromAccountId, toAccountId, amount, BASE.ACCOUNT_SIG_MSG.TRANSFER],
      )[0]

    return financialCheck.checkTransaction(
      zoneId,
      sendAccountId,
      fromAccountId,
      toAccountId,
      amount,
      miscValue1,
      miscValue2,
      _accountSignature,
      sigInfo.info,
    ) as unknown as Promise<EventReturnType['FinancialCheck']['CheckTransaction']>
  },

  checkExchange: async ({ financialCheck, accountId, fromZoneId, toZoneId, amount }: CheckExchangeType) => {
    return financialCheck.checkExchange(accountId, fromZoneId, toZoneId, amount) as unknown as Promise<
      EventReturnType['FinancialCheck']['CheckExchange']
    >
  },

  checkSyncAccount: async ({
    financialCheck,
    validatorId,
    accountId,
    zoneId,
    accountStatus,
    sigInfo,
    options = {},
  }: CheckSyncAccountType) => {
    const { accountSignature } = options
    const _accountSignature =
      accountSignature ??
      privateKey.sig(sigInfo.signer, ['bytes32', 'uint256'], [accountId, BASE.ACCOUNT_SIG_MSG.SYNCHRONOUS])[0]

    return financialCheck.checkSyncAccount(
      validatorId,
      accountId,
      zoneId,
      accountStatus,
      _accountSignature,
      sigInfo.info,
    ) as unknown as Promise<EventReturnType['FinancialCheck']['CheckSyncAccount']>
  },

  checkFinAccountStatus: ({ financialCheck, params }: CheckFinAccountStatusType) => {
    return financialCheck.checkFinAccountStatus(...params) as unknown as Promise<
      EventReturnType['FinancialCheck']['CheckFinAccountStatus']
    >
  },

  getAccountLimit: ({ financialCheck, params }: GetAccountLimitType) => {
    return financialCheck.getAccountLimit(...params) as unknown as Promise<
      EventReturnType['FinancialCheck']['GetAccountLimit']
    >
  },

  getBizZoneAccountStatus: ({ financialCheck, params }: GetBizZoneAccountStatusType) => {
    return financialCheck.getBizZoneAccountStatus(...params) as unknown as Promise<
      EventReturnType['FinancialCheck']['GetBizZoneAccountStatus']
    >
  },
}
