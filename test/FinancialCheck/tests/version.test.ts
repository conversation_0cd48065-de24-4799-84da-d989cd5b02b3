import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialCheckInstance } from '@test/common/types'
import { financialCheckFuncs } from '@test/FinancialCheck/helpers/function'
import { FinancialCheckContractType } from '@test/FinancialCheck/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let financialCheck: FinancialCheckInstance

  describe('正常系', () => {
    before(async () => {
      ;({ financialCheck } = await contractFixture<FinancialCheckContractType>())
    })

    it('versionが返されること', async () => {
      assert.equal(await financialCheckFuncs.version({ financialCheck: financialCheck }), BASE.APP.VERSION, 'version')
    })
  })
})
