import '@nomicfoundation/hardhat-chai-matchers'

describe('FinancialCheck', () => {
  require('./tests/version.test')
  require('./tests/initialize.test')
  require('./tests/checkTransaction.test')

  require('./tests/checkExchange.test')
  //   describe('準正常系', () => {
  //     before(async () => {
  //       [
  //         provider,
  //         issuer,
  //         validator,
  //         account,
  //         token,
  //         financialCheck,
  //         transferProxy,
  //         contractManager,
  //         customTransfer1,
  //         customTransfer2,
  //         customTransfer3,
  //       ] = await contractFixture<FinancialCheckContractType>();
  //     });

  //     describe('アカウントが登録されている状態', () => {
  //       const pramsAccounts = Object.values(BASE.ACCOUNT).slice(0, 4);
  //       const params = pramsAccounts.map((v, i) => {
  //         return {
  //           accountId: v.ID,
  //           accountName: v.NAME,
  //           accountEoa: accounts[i],
  //           accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
  //         };
  //       });

  //       before(async () => {
  //         const deadline = await utils.getDeadline();
  //         await providerFuncs.addProvider(provider, accounts, { zoneId: BASE.ZONE_ID.ID0, zoneName: BASE.ZONE_NAME.NAME0 });
  //         await providerFuncs.addProviderRole(provider, accounts, {
  //           providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
  //         });
  //         await issuerFuncs.addIssuer(issuer, accounts);
  //         await issuerFuncs.addIssuerRole(issuer, accounts);
  //         await validatorFuncs.addValidator(validator, accounts);
  //         await validatorFuncs.addValidatorRole(validator, accounts, {
  //           validatorEoa: BASE.VALID.EOA_ADDRESS,
  //         });
  //         await providerFuncs.addToken(provider, accounts, { eoaKey: BASE.EOA.PROV2 });
  //         await Promise.all(
  //           params.map(async (v, i) => {
  //             await validatorFuncs.addAccount(validator, accounts, {
  //               validatorId: BASE.VALID.VALID0.ID,
  //               accountId: v.accountId,
  //               accountName: v.accountName,
  //             });
  //             await issuerFuncs.addAccountRole(issuer, accounts, {
  //               issuerId: BASE.ISSUER.ISSUER0.ID,
  //               accountId: v.accountId,
  //               deadline: deadline + 60,
  //             });
  //           }),
  //         );
  //         await tokenFuncs.mint(token, accounts, 1000, {
  //           accountId: BASE.ACCOUNT.ACCOUNT1.ID,
  //         });
  //       });

  //       it('存在しないバリデータを指定した場合、エラーが返却されること', async () => {
  //         const pt = await utils.getDeadline();
  //         const siginfo = await utils.siginfoGenerator(0x64, BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt);

  //         const result = await financialCheckFuncs.checkApprove(
  //           financialCheck,
  //           BASE.VALID.VALID10.ID,
  //           BASE.ACCOUNT.ACCOUNT1.ID,
  //           BASE.ACCOUNT.ACCOUNT0.ID,
  //           100,
  //           siginfo,
  //           {
  //             privateKeyForSig: privateKey.key[valid0.eoaKey],
  //             eoaKey: valid0.eoaKey,
  //           },
  //         );
  //         const expected = {
  //           success: false,
  //           err: ERR.VALID.VALIDATOR_ID_NOT_EXIST,
  //         };
  //         utils.assertEqualForEachField(result, expected);
  //       });

  //       it('OwnerAccountが存在しない場合、エラーが返却されること', async () => {
  //         const pt = await utils.getDeadline();
  //         const siginfo = await utils.siginfoGenerator(0x64, BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt);

  //         const result = await financialCheckFuncs.checkApprove(
  //           financialCheck,
  //           BASE.VALID.VALID0.ID,
  //           BASE.ACCOUNT.ACCOUNT10.ID,
  //           BASE.ACCOUNT.ACCOUNT0.ID,
  //           100,
  //           siginfo,
  //           {
  //             privateKeyForSig: privateKey.key[valid0.eoaKey],
  //             eoaKey: valid0.eoaKey,
  //           },
  //         );
  //         const expected = {
  //           success: false,
  //           err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
  //         };
  //         utils.assertEqualForEachField(result, expected);
  //       });

  //       it('SenderAccountが存在しない場合、エラーが返却されること', async () => {
  //         const pt = await utils.getDeadline();
  //         const siginfo = await utils.siginfoGenerator(0x64, BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt);

  //         const result = await financialCheckFuncs.checkApprove(
  //           financialCheck,
  //           BASE.VALID.VALID0.ID,
  //           BASE.ACCOUNT.ACCOUNT0.ID,
  //           BASE.ACCOUNT.ACCOUNT10.ID,
  //           100,
  //           siginfo,
  //           {
  //             privateKeyForSig: privateKey.key[valid0.eoaKey],
  //             eoaKey: valid0.eoaKey,
  //           },
  //         );
  //         const expected = {
  //           success: false,
  //           err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
  //         };
  //         utils.assertEqualForEachField(result, expected);
  //       });

  //       it('バリデータに紐づいていないアカウントの場合、エラーが返却されること', async () => {
  //         await issuerFuncs.addIssuer(issuer, accounts, {
  //           issuerId: BASE.ISSUER.ISSUER10.ID,
  //         });
  //         await issuerFuncs.addIssuerRole(issuer, accounts, {
  //           issuerId: BASE.ISSUER.ISSUER10.ID,
  //         });
  //         await validatorFuncs.addValidator(validator, accounts, {
  //           validatorId: BASE.VALID.VALID20.ID,
  //           issuerId: BASE.ISSUER.ISSUER10.ID,
  //         });
  //         await validatorFuncs.addAccount(validator, accounts, {
  //           validatorId: BASE.VALID.VALID20.ID,
  //           accountId: BASE.ACCOUNT.ACCOUNT10.ID,
  //           accountName: BASE.ACCOUNT.ACCOUNT10.NAME,
  //         });

  //         const pt = await utils.getDeadline();
  //         const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt);

  //         const result = await financialCheckFuncs.checkApprove(
  //           financialCheck,
  //           BASE.VALID.VALID0.ID,
  //           BASE.ACCOUNT.ACCOUNT10.ID,
  //           BASE.ACCOUNT.ACCOUNT0.ID,
  //           100,
  //           siginfo,
  //           {
  //             privateKeyForSig: privateKey.key[valid0.eoaKey],
  //             eoaKey: valid0.eoaKey,
  //           },
  //         );
  //         const expected = {
  //           success: false,
  //           err: ERR.VALID.VALIDATOR_ID_NOT_EXIST,
  //         };
  //         utils.assertEqualForEachField(result, expected);
  //       });

  // it('バリデータ署名が不正である場合、エラーが返却されること', async () => {
  //   const pt = await utils.getDeadline();
  //   const siginfo = await utils.siginfoGenerator('0x64', BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt);

  //   const result = await financialCheckFuncs.checkApprove(
  //     financialCheck,
  //     BASE.VALID.VALID0.ID,
  //     BASE.ACCOUNT.ACCOUNT1.ID,
  //     BASE.ACCOUNT.ACCOUNT0.ID,
  //     100,
  //     siginfo,
  //     {
  //       privateKeyForSig: privateKey.key[valid0.eoaKey],
  //       eoaKey: valid1.eoaKey,
  //     },
  //   );
  //   const expected = {
  //     success: false,
  //     err: ERR.VALID.VALIDATOR_NOT_ROLE,
  //   };
  //   utils.assertEqualForEachField(result, expected);
  // });

  // it('バリデータ署名が有効期限切れである場合、エラーが返却されること', async () => {
  //   const exceededDeadline = await utils.getExceededDeadline();
  //   const pt = await utils.getDeadline();
  //   const siginfo = await utils.siginfoGenerator(0x64, BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt);

  //   const result = await financialCheckFuncs.checkApprove(
  //     financialCheck,
  //     BASE.VALID.VALID0.ID,
  //     BASE.ACCOUNT.ACCOUNT1.ID,
  //     BASE.ACCOUNT.ACCOUNT0.ID,
  //     100,
  //     siginfo,
  //     {
  //       privateKeyForSig: privateKey.key[valid0.eoaKey],
  //       eoaKey: valid0.eoaKey,
  //       deadline: exceededDeadline,
  //     },
  //   );
  //   const expected = {
  //     success: false,
  //     err: ERR.ACTRL.ACTRL_SIG_TIMEOUT,
  //   };
  //   utils.assertEqualForEachField(result, expected);
  // });

  //       it('アカウント署名が無効である場合、エラーが返却されること', async () => {
  //         const pt = await utils.getDeadline();
  //         const siginfo = await utils.siginfoGenerator(0x64, BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt);
  //         const invalidAccountSig = privateKey.sig(
  //           siginfo.signer,
  //           ['bytes32', 'bytes32', 'bytes32', 'bytes32', 'uint256', 'uint256'],
  //           [
  //             BASE.ACCOUNT.ACCOUNT1.ID,
  //             BASE.ACCOUNT.ACCOUNT1.ID,
  //             BASE.ACCOUNT.ACCOUNT0.ID,
  //             BASE.REToken.TOKENS.TOKEN1.TOKEN_ID,
  //             10,
  //             BASE.ACCOUNT_SIG_MSG.TRANSFER,
  //           ],
  //         )[0];

  //         const result = await financialCheckFuncs.checkApprove(
  //           financialCheck,
  //           BASE.VALID.VALID0.ID,
  //           BASE.ACCOUNT.ACCOUNT1.ID,
  //           BASE.ACCOUNT.ACCOUNT0.ID,
  //           100,
  //           siginfo,
  //           {
  //             privateKeyForSig: privateKey.key[valid0.eoaKey],
  //             eoaKey: valid0.eoaKey,
  //             accountSignature: invalidAccountSig,
  //           },
  //         );
  //         const expected = {
  //           success: false,
  //           err: ERR.ACCOUNT.ACCOUNT_BAD_SIG,
  //         };
  //         utils.assertEqualForEachField(result, expected);
  //       });

  //       it('空のアカウント署名が無効である場合、エラーが返却されること', async () => {
  //         const pt = await utils.getDeadline();
  //         const siginfo = await utils.siginfoGenerator(0x64, BASE.PK.PK2, BASE.NN, BASE.PK.VALID_PK, pt);
  //         const emptyAccountSig = utils.toBytes('');

  //         const result = await financialCheckFuncs.checkApprove(
  //           financialCheck,
  //           BASE.VALID.VALID0.ID,
  //           BASE.ACCOUNT.ACCOUNT1.ID,
  //           BASE.ACCOUNT.ACCOUNT0.ID,
  //           100,
  //           siginfo,
  //           {
  //             privateKeyForSig: privateKey.key[valid0.eoaKey],
  //             eoaKey: valid0.eoaKey,
  //             accountSignature: emptyAccountSig,
  //           },
  //         );
  //         const expected = {
  //           success: false,
  //           err: ERR.ACCOUNT.ACCOUNT_BAD_SIG,
  //         };
  //         utils.assertEqualForEachField(result, expected);
  //       });
  //     });
  //   });
  // });

  require('./tests/checkSyncAccount.test')
  require('./tests/checkFinAccountStatus.test')
  require('./tests/getAccountLimit.test')
  require('./tests/getBizZoneAccountStatus.test')
})
