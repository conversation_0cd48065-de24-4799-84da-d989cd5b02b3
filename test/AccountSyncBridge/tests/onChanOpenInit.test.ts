import '@nomicfoundation/hardhat-chai-matchers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountSyncBridgeInstance, IBCHandlerInstance } from '@test/common/types'
import { expect } from 'chai'
import { ethers } from 'hardhat'

describe('onChanOpenInit()', () => {
  let accountSyncBridge: AccountSyncBridgeInstance
  let ibcHandler: IBCHandlerInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accountSyncBridge, ibcHandler } = await contractFixture<AccountSyncBridgeContractType>())
    })

    it('accountSyncSourceVersion return normally', async () => {
      // Hack: onChanOpenInit of AccountSyncBridge only allow from IBCHandlerMock (_ibcH<PERSON>ler)
      // => but there's no call from _ib<PERSON><PERSON><PERSON><PERSON> in IBCHandlerMock contract
      // onChanOpenInit also be called from IBCChannelHandshake,
      // => but IBCChannelHandshake is inside yuiContract,
      // => there's no implement of it for testing, so it's not possible to call it directly
      // UNREACABLE: use impersonateAccount to fake call from _ibcHandler
      // TODO: should be refactored when IBCChannelHandshake is implemented and use
      // => or onChanOpenInit is called from IBCHandlerMock
      await helpers.impersonateAccount(await ibcHandler.getAddress())
      await helpers.setBalance(await ibcHandler.getAddress(), 100n ** 18n)
      const fakeIbcHandler = await ethers.getSigner(await ibcHandler.getAddress())

      const res = await accountSyncBridge.connect(fakeIbcHandler).onChanOpenInit({
        order: 0,
        connectionHops: [BASE.EMPTY_ADDRESS],
        portId: BASE.ACC_SYNC_BRIDGE.PORT,
        channelId: BASE.ACC_SYNC_BRIDGE.CHANNEL,
        counterparty: {
          port_id: BASE.ACC_SYNC_BRIDGE.PORT,
          channel_id: BASE.ACC_SYNC_BRIDGE.CHANNEL,
        },
        version: BASE.ACC_SYNC_BRIDGE.VERSION,
      })
      await helpers.stopImpersonatingAccount(await ibcHandler.getAddress())
      await expect(res).to.be.equal(BASE.ACC_SYNC_BRIDGE.VERSION)
    })
    it('accountSyncSourceVersion return normally when _msg.version is empty', async () => {
      // TODO: should be refactored when IBCChannelHandshake is implemented and use
      // => or onChanOpenInit is called from IBCHandlerMock
      await helpers.impersonateAccount(await ibcHandler.getAddress())
      await helpers.setBalance(await ibcHandler.getAddress(), 100n ** 18n)
      const fakeIbcHandler = await ethers.getSigner(await ibcHandler.getAddress())

      const res = await accountSyncBridge.connect(fakeIbcHandler).onChanOpenInit({
        order: 0,
        connectionHops: [BASE.EMPTY_ADDRESS],
        portId: BASE.ACC_SYNC_BRIDGE.PORT,
        channelId: BASE.ACC_SYNC_BRIDGE.CHANNEL,
        counterparty: {
          port_id: BASE.ACC_SYNC_BRIDGE.PORT,
          channel_id: BASE.ACC_SYNC_BRIDGE.CHANNEL,
        },
        version: '',
      })
      await helpers.stopImpersonatingAccount(await ibcHandler.getAddress())
      await expect(res).to.be.equal(BASE.ACC_SYNC_BRIDGE.VERSION)
    })
  })
  describe('準正常系', () => {
    before(async () => {
      ;({ accountSyncBridge } = await contractFixture<AccountSyncBridgeContractType>())
    })
    it('should revert when caller is not ibc', async () => {
      await expect(
        accountSyncBridge.onChanOpenInit({
          order: 0,
          connectionHops: [BASE.EMPTY_ADDRESS],
          portId: BASE.ACC_SYNC_BRIDGE.PORT,
          channelId: BASE.ACC_SYNC_BRIDGE.CHANNEL,
          counterparty: {
            port_id: BASE.ACC_SYNC_BRIDGE.PORT,
            channel_id: BASE.ACC_SYNC_BRIDGE.CHANNEL,
          },
          version: BASE.ACC_SYNC_BRIDGE.VERSION,
        }),
      ).to.be.revertedWith(ERR.COMMON.CALLER_NOT_IBC)
    })
    it('should revert when version is mismatch', async () => {
      // TODO: should be refactored when IBCChannelHandshake is implemented and use
      // => or onChanOpenInit is called from IBCHandlerMock
      await helpers.impersonateAccount(await ibcHandler.getAddress())
      await helpers.setBalance(await ibcHandler.getAddress(), 100n ** 18n)
      const fakeIbcHandler = await ethers.getSigner(await ibcHandler.getAddress())

      await expect(
        accountSyncBridge.connect(fakeIbcHandler).onChanOpenInit({
          order: 0,
          connectionHops: [BASE.EMPTY_ADDRESS],
          portId: BASE.ACC_SYNC_BRIDGE.PORT,
          channelId: BASE.ACC_SYNC_BRIDGE.CHANNEL,
          counterparty: {
            port_id: BASE.ACC_SYNC_BRIDGE.PORT,
            channel_id: BASE.ACC_SYNC_BRIDGE.CHANNEL,
          },
          version: 'false-version',
        }),
      ).to.be.revertedWith(ERR.COMMON.VER_NOT_MATCH)
      await helpers.stopImpersonatingAccount(await ibcHandler.getAddress())
    })
  })
})
