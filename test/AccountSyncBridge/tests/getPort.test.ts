import '@nomicfoundation/hardhat-chai-matchers'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountSyncBridgeInstance } from '@test/common/types'
import { assert } from 'chai'

describe('getPort()', () => {
  let accountSyncBridge: AccountSyncBridgeInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accountSyncBridge } = await contractFixture<AccountSyncBridgeContractType>())
    })

    it('accountSyncSourcePort is return normally', async () => {
      const port = await accountSyncBridge.getPort()
      assert.equal(port, BASE.ACC_SYNC_BRIDGE.PORT, 'port')
    })
  })
})
