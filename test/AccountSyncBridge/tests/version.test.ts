import '@nomicfoundation/hardhat-chai-matchers'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountSyncBridgeInstance } from '@test/common/types'
import { assert } from 'chai'

describe('version()', () => {
  let accountSyncBridge: AccountSyncBridgeInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accountSyncBridge } = await contractFixture<AccountSyncBridgeContractType>())
    })

    describe('初期状態', () => {
      it('バージョン情報を取得できること', async () => {
        const version = await accountSyncBridge.version()
        assert.equal(version, BASE.APP.VERSION, 'version')
      })
    })
  })
})
