import '@nomicfoundation/hardhat-chai-matchers'
import { accountSyncBridgeFuncs } from '@test/AccountSyncBridge/helpers/function'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountSyncBridgeInstance, IBCHandlerInstance, ValidatorMockInstance } from '@test/common/types'
import { assert } from 'chai'

describe('onTimeoutPacket', () => {
  let accountSyncBridge: AccountSyncBridgeInstance
  let ibcHandler: IBCHandlerInstance
  let validatorMock: ValidatorMockInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accountSyncBridge, ibcHandler, validatorMock } = await contractFixture<AccountSyncBridgeContractType>())
    })

    describe('初期状態', () => {
      beforeEach(async () => {
        await accountSyncBridgeFuncs.syncAccount({ accountSyncBridge })
      })

      it('FinZoneからpacketを受け取ることができた場合、BizZoneのアカウントがリセットされないこと', async () => {
        await accountSyncBridgeFuncs.timeoutPacket({ ibcHandler: ibcHandler, accountSyncBridge: accountSyncBridge })

        const accountData = await validatorMock.getAccount(BASE.BRIDGE.VALIDATOR_ID, BASE.BRIDGE.ACCOUNT_A)

        assert.equal(accountData[0].accountStatus, BASE.STATUS.ACTIVE, 'accountStatus')
      })
    })
  })
})
