import '@nomicfoundation/hardhat-chai-matchers'
import { accountSyncBridgeFuncs } from '@test/AccountSyncBridge/helpers/function'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountSyncBridgeInstance, BusinessZoneAccountMockInstance, IBCHandlerInstance } from '@test/common/types'
import { assert, expect } from 'chai'
import Web3 from 'web3'

declare let web3: Web3

describe('onRecvPacket', () => {
  let accountSyncBridge: AccountSyncBridgeInstance
  let ibcHandler: IBCHandlerInstance
  let businessZoneAccountMock: BusinessZoneAccountMockInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accountSyncBridge, ibcHandler, businessZoneAccountMock } =
        await contractFixture<AccountSyncBridgeContractType>())
    })

    describe('初期状態', () => {
      it('BizZoneからpacketを受け取ることができた場合、FinZone管理のBizZoneアカウントが更新されること', async () => {
        await accountSyncBridgeFuncs.recvPacket({ ibcHandler: ibcHandler, accountSyncBridge: accountSyncBridge })

        const bizZoneAccountData = await businessZoneAccountMock.getBizZoneAccount(BASE.ZONE.BIZ, BASE.BRIDGE.ACCOUNT_A)

        assert.equal(bizZoneAccountData, BASE.STATUS.APPLYING, 'accountStatus')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accountSyncBridge, ibcHandler } = await contractFixture<AccountSyncBridgeContractType>())
    })

    describe('初期状態', () => {
      it('packet空の場合、エラーがスローされること', async () => {
        const packetData = web3.eth.abi.encodeParameters([], [])
        await expect(
          accountSyncBridgeFuncs.recvPacket({
            ibcHandler: ibcHandler,
            accountSyncBridge: accountSyncBridge,
            options: {
              packetData: packetData,
            },
          }),
        ).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('should revert when packet is from fin zone', async () => {
        await expect(
          accountSyncBridgeFuncs.recvPacket({
            ibcHandler: ibcHandler,
            accountSyncBridge: accountSyncBridge,
            options: {
              fromZoneId: BASE.ZONE.FIN,
            },
          }),
        ).to.be.revertedWith(ERR.IBC.NOT_ALLOWED_FROM_FIN_ZONE)
      })
    })
  })
})
