import '@nomicfoundation/hardhat-chai-matchers'

describe('AccountSyncBridge', () => {
  require('./tests/initialize.test')
  require('./tests/version.test')
  require('./tests/setAddress.test')
  require('./tests/getConfig.test')
  require('./tests/getPort.test')
  require('./tests/syncAccount.test')
  require('./tests/onRecvPacket.test')
  require('./tests/onAcknowledgementPacket.test')
  require('./tests/onChanOpenInit.test')
  require('./tests/onChanOpenTry.test')
  require('./tests/onTimeoutPacket.test')
  require('./tests/recoverPacket.test')
})
