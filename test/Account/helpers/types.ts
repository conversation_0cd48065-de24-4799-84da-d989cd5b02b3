import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  EventReturnType,
  FinancialCheckInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'

export type AccountContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  ibcToken: IBCTokenInstance
  contractManager: ContractManagerInstance
  financialCheck: FinancialCheckInstance
  businessZoneAccount: BusinessZoneAccountInstance
}

type AccountType = { account: AccountInstance }

// type ApproveType = {
//   account: AccountInstance
//   accounts: SignerWithAddress[]
//   spenderId: string
//   amount: number
//   option?: Partial<ApproveOption>
// }

// type GetAccountsAllType = AccountType & { offset: number }

export type FuncParamsType = {
  version: AccountType
  hasAccount: AccountType & { params: Parameters<AccountInstance['hasAccount']> }
  isTerminated: AccountType & { params: Parameters<AccountInstance['isTerminated']> }
  balanceOf: AccountType & { params: Parameters<AccountInstance['balanceOf']> }
  getAccountId: AccountType & { params: Parameters<AccountInstance['getAccountId']> }
  getAccount: AccountType & { params: Parameters<AccountInstance['getAccount']> }
  getDestinationAccount: AccountType & { params: Parameters<AccountInstance['getDestinationAccount']> }
  getAccountCount: AccountType & { params?: Parameters<AccountInstance['getAccountCount']> }
  getValidatorIdByAccountId: AccountType & {
    params: Parameters<AccountInstance['getValidatorIdByAccountId']>
  }
}

export type FunctionType = {
  version: (args: FuncParamsType['version']) => Promise<string>
  hasAccount: (args: FuncParamsType['hasAccount']) => Promise<EventReturnType['Account']['HasAccount']>
  isTerminated: (args: FuncParamsType['isTerminated']) => Promise<EventReturnType['Account']['IsTerminated']>
  balanceOf: (args: FuncParamsType['balanceOf']) => Promise<EventReturnType['Account']['BalanceOf']>
  getAccountId: (args: FuncParamsType['getAccountId']) => Promise<EventReturnType['Account']['GetAccountId']>
  getAccount: (args: FuncParamsType['getAccount']) => Promise<EventReturnType['Account']['GetAccount']>
  getDestinationAccount: (
    args: FuncParamsType['getDestinationAccount'],
  ) => Promise<EventReturnType['Account']['GetDestinationAccount']>
  getAccountCount: (args: FuncParamsType['getAccountCount']) => Promise<EventReturnType['Account']['GetAccountCount']>
  getValidatorIdByAccountId: (
    args: FuncParamsType['getValidatorIdByAccountId'],
  ) => Promise<EventReturnType['Account']['GetValidatorIdByAccountId']>
}
