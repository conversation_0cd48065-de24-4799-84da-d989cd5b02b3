import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accountFuncs } from '@test/Account/helpers/function'
import { AccountContractType } from '@test/Account/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'

describe('hasAccount()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account } = await contractFixture<AccountContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('アカウント存在がチェックする', async () => {
        const result = await accountFuncs.hasAccount({ account: account, params: [BASE.ACCOUNT.ACCOUNT1.ID] })
        utils.assertEqualForEachField(result, { success: true, err: '' })
      })

      it('存在しないアカウントがチェックする場合エラー', async () => {
        const result = await accountFuncs.hasAccount({ account: account, params: [utils.toBytes32('x110')] })
        utils.assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST })
      })

      it('空アカウントがチェックする場合エラー', async () => {
        const result = await accountFuncs.hasAccount({ account: account, params: [BASE.ACCOUNT.EMPTY.ID] })
        utils.assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL })
      })
    })
  })
})
