import '@nomicfoundation/hardhat-chai-matchers'
import { accountFuncs } from '@test/Account/helpers/function'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance } from '@test/common/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let account: AccountInstance
  describe('正常系', () => {
    before(async () => {
      ;({ account } = await contractFixture<AccountContractType>())
    })

    it('versionが取得する', async () => {
      assert.equal(await accountFuncs.version({ account: account }), BASE.APP.VERSION, 'version')
    })
  })
})
