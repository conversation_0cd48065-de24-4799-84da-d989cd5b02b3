import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance } from '@test/common/types'
import { expect } from 'chai'

describe('calcBalance()', () => {
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  // calcBalance のテストは token.calcBalance から呼ばれるため token のテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, account } = await contractFixture<AccountContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がTokenではない場合、エラーがスローされること', async () => {
        await expect(
          account.connect(accounts[1]).calcBalance(BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID, 1000), // tokenコントラクト以外をfromに設定する
        ).to.be.revertedWith(ERR.TOKEN.NOT_TOKEN_CONTRACT)
      })
    })
  })
})
