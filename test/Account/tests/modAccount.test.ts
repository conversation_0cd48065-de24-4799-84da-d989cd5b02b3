import { AccountContractType } from '@/test/Account/helpers/types'
import { contractFixture } from '@/test/common/contractFixture'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { AccountInstance } from '@test/common/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('modAccount()', () => {
  let account: AccountInstance
  let accounts: SignerWithAddress[]
  // modAccount のテストは validator.modAccount から呼ばれるため validator のテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, account } = await contractFixture<AccountContractType>())
    })

    describe('Caller is not validator', () => {
      it('呼び出し元がValidatorではない場合、エラーがスローされること', async () => {
        await expect(
          account.connect(accounts[1]).modAccount(BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT0.NAME, BASE.TRACE_ID),
        ).to.be.revertedWith(ERR.VALID.NOT_VALIDATOR_CONTRACT)
      })
    })
  })
})
