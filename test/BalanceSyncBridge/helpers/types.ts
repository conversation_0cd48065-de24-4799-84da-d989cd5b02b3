import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlMockInstance,
  AccountMockInstance,
  BalanceSyncBridgeInstance,
  ContractCallOption,
  IBCHandlerInstance,
  IBCTokenMockInstance,
  PacketCallOption,
  SyncTransferCallOption,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type BalanceSyncBridgeContractType = {
  accounts: SignerWithAddress[]
  balanceSyncBridge: BalanceSyncBridgeInstance
  ibcHandler: IBCHandlerInstance
  ibcTokenMock: IBCTokenMockInstance
  accountMock: AccountMockInstance
  accessCtrlMock: AccessCtrlMockInstance
}

type BalanceSyncBridgeType = {
  balanceSyncBridge: BalanceSyncBridgeInstance
}

type PacketType = BalanceSyncBridgeType & {
  ibcHandler: IBCHandlerInstance
  options?: Partial<PacketCallOption>
}

export type FuncParamsType = {
  setAddress: BalanceSyncBridgeType & {
    ibcTokenMockAddress: string
    accountMockAddress: string
    accessCtrlMockAddress: string
    options?: Partial<ContractCallOption & { newAddress: string }>
  }
  syncTransfer: BalanceSyncBridgeType & {
    options?: Partial<SyncTransferCallOption>
  }
  recvPacket: PacketType
  acknowledgementPacket: PacketType
  timeoutPacket: PacketType
  recoverPacket: BalanceSyncBridgeType & {
    options?: Partial<ContractCallOption & PacketCallOption>
  }
}

export type FunctionType = {
  setAddress: (args: FuncParamsType['setAddress']) => Promise<ContractTransactionResponse>
  syncTransfer: (args: FuncParamsType['syncTransfer']) => Promise<ContractTransactionResponse>
  recvPacket: (args: FuncParamsType['recvPacket']) => Promise<ContractTransactionResponse>
  acknowledgementPacket: (args: FuncParamsType['acknowledgementPacket']) => Promise<ContractTransactionResponse>
  timeoutPacket: (args: FuncParamsType['timeoutPacket']) => Promise<ContractTransactionResponse>
  recoverPacket: (args: FuncParamsType['recoverPacket']) => Promise<ContractTransactionResponse>
}
