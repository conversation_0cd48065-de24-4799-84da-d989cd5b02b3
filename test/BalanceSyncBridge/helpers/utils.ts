import { BASE } from '@test/common/consts'
import * as utils from '@test/common/utils'
import Web3 from 'web3'

declare let web3: Web3

export const genPacketData = (packetData, fromZoneId, toZoneId) =>
  packetData ??
  web3.eth.abi.encodeParameters(
    ['(bytes32, string, bytes32, string, uint16, uint16, bytes32)'],
    [
      [
        BASE.BRIDGE.ACCOUNT_A,
        BASE.BRIDGE.ACCOUNT_A_NAME,
        BASE.BRIDGE.ACCOUNT_B,
        BASE.BRIDGE.ACCOUNT_B_NAME,
        fromZoneId ?? BASE.ZONE.BIZ,
        BASE.BRIDGE.AMOUNT,
        BASE.TRACE_ID,
      ],
    ],
  )

export const genPacket = (packetData, fromZoneId, toZoneId) =>
  utils.genPacketData(
    genPacketData(packetData, fromZoneId, toZoneId),
    BASE.BALANCE_SYNC_BRIDGE.PORT,
    BASE.BALANCE_SYNC_BRIDGE.CHANNEL,
    BASE.TIMEOUT_HEIGHT,
  )
