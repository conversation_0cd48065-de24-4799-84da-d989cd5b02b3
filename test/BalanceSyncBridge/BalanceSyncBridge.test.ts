import '@nomicfoundation/hardhat-chai-matchers'

describe('BalanceSyncBridge', () => {
  require('./tests/initialize.test')
  require('./tests/version.test')
  require('./tests/ibcAddress.test')
  require('./tests/setAddress.test')
  require('./tests/getConfig.test')
  require('./tests/syncTransfer.test')
  require('./tests/onRecvPacket.test')
  require('./tests/onChanOpenInit.test')
  require('./tests/onChanOpenTry.test')
  require('./tests/recoverPacket.test')
})
