import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { balanceSyncBridgeFuncs } from '@test/BalanceSyncBridge/helpers/function'
import { BalanceSyncBridgeContractType } from '@test/BalanceSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlMockInstance, BalanceSyncBridgeInstance, IBCTokenMockInstance } from '@test/common/types'
import { expect } from 'chai'

describe('recoverPacket', () => {
  let accounts: SignerWithAddress[]
  let balanceSyncBridge: BalanceSyncBridgeInstance
  let ibcTokenMock: IBCTokenMockInstance
  let accessCtrlMock: AccessCtrlMockInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, balanceSyncBridge, ibcTokenMock, accessCtrlMock } =
        await contractFixture<BalanceSyncBridgeContractType>())
      accessCtrlMock.addAdminRole(accounts[0], 0, BASE.TRACE_ID)
    })

    describe('初期状態', () => {
      it('BizZoneからpacketを受け取ることができた場合、FinZone管理のBizZoneアカウントが更新されること', async () => {
        await balanceSyncBridgeFuncs.recoverPacket({ balanceSyncBridge })

        const bizZoneAccountData = await ibcTokenMock.getBalanceByZone(BASE.BRIDGE.ACCOUNT_B, BASE.ZONE.BIZ)
        await expect(bizZoneAccountData).to.be.equal(BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT))
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ balanceSyncBridge, ibcTokenMock, accessCtrlMock } = await contractFixture<BalanceSyncBridgeContractType>())
    })

    describe('初期状態', () => {
      it('ADMIN権限ではない場合、エラーが返却されること', async () => {
        await expect(
          balanceSyncBridgeFuncs.recoverPacket({
            balanceSyncBridge: balanceSyncBridge,
            options: {
              privateKeyForSig: privateKey[1],
            },
          }),
        ).to.be.revertedWith(ERR.COMMON.NOT_ADMIN_ROLE)
      })

      it('署名が無効の場合、エラーがスローされること', async () => {
        await expect(
          balanceSyncBridgeFuncs.recoverPacket({
            balanceSyncBridge: balanceSyncBridge,
            options: {
              sig: ['0x12345678', ''],
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名が無効（not signature）の場合、エラーがスローされること', async () => {
        const bad_sig =
          '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000'

        await expect(
          balanceSyncBridgeFuncs.recoverPacket({
            balanceSyncBridge: balanceSyncBridge,
            options: {
              sig: [bad_sig, ''],
            },
          }),
        ).to.be.reverted // OpenZeppelin revertのため文字列がDCF仕様と異なる
      })
    })
  })
})
