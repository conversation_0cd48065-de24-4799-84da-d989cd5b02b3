import '@nomicfoundation/hardhat-chai-matchers'
import { BalanceSyncBridgeContractType } from '@test/BalanceSyncBridge/helpers/types'
import { contractFixture } from '@test/common/contractFixture'
import { BalanceSyncBridgeInstance, IBCHandlerInstance } from '@test/common/types'
import { assert } from 'chai'

describe('ibcAddress()', () => {
  let balanceSyncBridge: BalanceSyncBridgeInstance
  let ibcHandler: IBCHandlerInstance

  describe('正常系', () => {
    before(async () => {
      ;({ balanceSyncBridge, ibcHandler } = await contractFixture<BalanceSyncBridgeContractType>())
    })

    describe('初期状態', () => {
      it('ibc<PERSON><PERSON><PERSON> return the correct address', async () => {
        const ibcAddress = await balanceSyncBridge.ibcAddress()
        assert.equal(ibcAddress, await ibcHandler.getAddress(), 'ibcAddress')
      })
    })
  })
})
