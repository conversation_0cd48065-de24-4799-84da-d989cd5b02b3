import '@nomicfoundation/hardhat-chai-matchers'
import { BalanceSyncBridgeContractType } from '@test/BalanceSyncBridge/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { BalanceSyncBridgeInstance } from '@test/common/types'
import { expect } from 'chai'

describe('getConfig()', () => {
  let balanceSyncBridge: BalanceSyncBridgeInstance

  describe('正常系', () => {
    before(async () => {
      ;({ balanceSyncBridge } = await contractFixture<BalanceSyncBridgeContractType>())
    })

    describe('初期状態', () => {
      it('getConfig return the correct config of BalanceSyncBridge', async () => {
        const expectParams = {
          port: BASE.BALANCE_SYNC_BRIDGE.PORT,
          channel: BASE.BALANCE_SYNC_BRIDGE.CHANNEL,
          version: BASE.BALANCE_SYNC_BRIDGE.VERSION,
        }

        const res = await balanceSyncBridge.getConfig()
        await expect(res).to.be.deep.equal(Object.values(expectParams))
      })
    })
  })
})
