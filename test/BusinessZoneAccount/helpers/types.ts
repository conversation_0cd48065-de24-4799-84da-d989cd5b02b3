import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  EventReturnType,
  FinancialCheckInstance,
  FinancialZoneAccountInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  SetBizAccountsAllOption,
  SyncBusinessZoneStatusOption,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type BusinessZoneAccountContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  financialZoneAccount: FinancialZoneAccountInstance
  businessZoneAccount: BusinessZoneAccountInstance
  token: TokenInstance
  financialCheck: FinancialCheckInstance
  contractManager: ContractManagerInstance
  ibcToken: IBCTokenInstance
}

type BusinessZoneAccountType = { businessZoneAccount: BusinessZoneAccountInstance }

export type FuncParamsType = {
  version: BusinessZoneAccountType
  hasAccountByZone: BusinessZoneAccountType & {
    params: Parameters<BusinessZoneAccountInstance['hasAccountByZone']>
  }
  getBusinessZoneAccount: BusinessZoneAccountType & {
    params: Parameters<BusinessZoneAccountInstance['getBusinessZoneAccount']>
  }
  isActivatedByZone: BusinessZoneAccountType & {
    params: Parameters<BusinessZoneAccountInstance['isActivatedByZone']>
  }
  accountIdExistenceByZoneId: BusinessZoneAccountType & {
    params: Parameters<BusinessZoneAccountInstance['accountIdExistenceByZoneId']>
  }
  forceBurnAllBalance: BusinessZoneAccountType & {
    params: Parameters<BusinessZoneAccountInstance['forceBurnAllBalance']>
  }
  getBizAccountsAll: BusinessZoneAccountType & {
    offset: number
  }
  setBizAccountsAll: BusinessZoneAccountType & {
    bizAccounts: any
    options: Partial<SetBizAccountsAllOption & ContractCallOption>
  }
  syncBusinessZoneStatus: BusinessZoneAccountType & {
    accounts: SignerWithAddress[]
    options?: Partial<SyncBusinessZoneStatusOption & ContractCallOption>
  }
}

export type FunctionType = {
  version: (args: FuncParamsType['version']) => Promise<string>
  hasAccountByZone: (
    args: FuncParamsType['hasAccountByZone'],
  ) => Promise<EventReturnType['BusinessZoneAccount']['HasAccountByZone']>
  getBusinessZoneAccount: (
    args: FuncParamsType['getBusinessZoneAccount'],
  ) => Promise<EventReturnType['BusinessZoneAccount']['GetBusinessZoneAccount']>
  isActivatedByZone: (
    args: FuncParamsType['isActivatedByZone'],
  ) => Promise<EventReturnType['BusinessZoneAccount']['IsActivatedByZone']>
  accountIdExistenceByZoneId: (
    args: FuncParamsType['accountIdExistenceByZoneId'],
  ) => Promise<EventReturnType['BusinessZoneAccount']['AccountIdExistenceByZoneId']>
  forceBurnAllBalance: (
    args: FuncParamsType['forceBurnAllBalance'],
  ) => Promise<EventReturnType['BusinessZoneAccount']['ForceBurnAllBalance']>
  getBizAccountsAll: (
    args: FuncParamsType['getBizAccountsAll'],
  ) => Promise<EventReturnType['BusinessZoneAccount']['GetBizAccountsAll']>
  setBizAccountsAll: (args: FuncParamsType['setBizAccountsAll']) => Promise<ContractTransactionResponse>
  syncBusinessZoneStatus: (args: FuncParamsType['syncBusinessZoneStatus']) => Promise<ContractTransactionResponse>
}
