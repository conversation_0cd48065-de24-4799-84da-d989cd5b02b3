/* eslint-disable @typescript-eslint/no-explicit-any */
import privateKey from '@/privateKey'
import { BASE } from '@test/common/consts'
import * as utils from '@test/common/utils'
import { FunctionType } from './types'

export const businessZoneAccountFuncs: FunctionType = {
  version: ({ businessZoneAccount }) => {
    return businessZoneAccount.version()
  },
  hasAccountByZone: ({ businessZoneAccount, params }) => {
    return businessZoneAccount.hasAccountByZone(...params)
  },
  getBusinessZoneAccount: ({ businessZoneAccount, params }) => {
    return businessZoneAccount.getBusinessZoneAccount(...params) as any
  },
  isActivatedByZone: ({ businessZoneAccount, params }) => {
    return businessZoneAccount.isActivatedByZone(...params)
  },
  accountIdExistenceByZoneId: ({ businessZoneAccount, params }) => {
    return businessZoneAccount.accountIdExistenceByZoneId(...params) as any
  },
  forceBurnAllBalance: ({ businessZoneAccount, params }) => {
    return businessZoneAccount.forceBurnAllBalance(...params) as any
  },
  getBizAccountsAll: async ({ businessZoneAccount, offset }) => {
    return businessZoneAccount.getBizAccountsAll(offset) as any
  },
  setBizAccountsAll: async ({ businessZoneAccount, bizAccounts, options = {} }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_BIZACCOUNTS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return businessZoneAccount.setBizAccountsAll(bizAccounts, _deadline, _sig[0])
  },
  syncBusinessZoneStatus: ({ businessZoneAccount, accounts, options }) => {
    const {
      zoneId = BASE.ZONE_ID.ID0,
      zoneName = BASE.ZONE_NAME.NAME0,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      accountName = BASE.ACCOUNT.ACCOUNT1.NAME,
      accountStatus = BASE.STATUS.APPLYING,
    } = options
    return businessZoneAccount
      .connect(accounts[0])
      .syncBusinessZoneStatus(zoneId, zoneName, accountId, accountName, accountStatus, BASE.TRACE_ID)
  },
}
