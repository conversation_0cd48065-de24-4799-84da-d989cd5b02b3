import '@nomicfoundation/hardhat-chai-matchers'

describe('BusinessZoneAccount', () => {
  require('./tests/version.test')
  require('./tests/initialize.test')
  require('./tests/setActiveBusinessAccountWithZone.test')
  require('./tests/setBizZoneTerminated.test')
  require('./tests/hasAccountByZone.test')
  require('./tests/isActivatedByZone.test')
  require('./tests/syncBusinessZoneStatus.test')
  require('./tests/syncBusinessZoneBalance.test')
  require('./tests/addBusinessZoneBalance.test')
  require('./tests/subtractBusinessZoneBalance.test')
  require('./tests/balanceUpdateByRedeemVoucher.test')
  require('./tests/balanceUpdateByIssueVoucher.test')
  require('./tests/forceBurnAllBalance.test')
  // describe('syncBusinessZoneStatus()', () => {
  //   describe('正常系', () => {
  //     before(async () => {
  //       [prov, issuer, valid, account] = await contractFixture<BusinessZoneAccountContractType>();
  //       contractManager = await contractManagerFactory();
  //     });

  //     describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
  //       const ibcAddress = accounts[0];

  //       before(async () => {
  //         await providerFuncs.addProvider(prov, accounts, { zoneId: BASE.ZONE_ID.ID0, zoneName: BASE.ZONE_NAME.NAME0 });
  //         await providerFuncs.addProviderRole(prov, accounts, {
  //           providerEoa: accounts[BASE.EOA.PROV2],
  //         });
  //         await issuerFuncs.addIssuer(issuer, accounts);
  //         await validatorFuncs.addValidator(valid, accounts);
  //         await providerFuncs.addToken(prov, accounts, { eoaKey: BASE.EOA.PROV2 });
  //         await Promise.all(
  //           [BASE.ACCOUNT.ACCOUNT1, BASE.ACCOUNT.ACCOUNT2, BASE.ACCOUNT.ACCOUNT3, BASE.ACCOUNT.ACCOUNT4].map((v) =>
  //             validatorFuncs.addAccount(valid, accounts, { accountId: v.ID }),
  //           ),
  //         );
  //         await contractManagerFuncs.setIbcApp(contractManager, accounts, ibcAddress, BASE.IBCAPP_NAME.ACCOUNT_SYNC);
  //         await providerFuncs.addBizZone(prov, accounts, {
  //           zoneId: BASE.ZONE_ID.ID1,
  //           zoneName: BASE.ZONE_NAME.NAME1,
  //         });
  //       });

  //       it('FinZone管理のBusinessZoneAccountを申し込みできること', async () => {
  //         await validatorFuncs.syncBusinessZoneStatus(valid, accounts, {
  //           accountId: BASE.ACCOUNT.ACCOUNT1.ID,
  //           accountStatus: BASE.STATUS.APPLYING,
  //           zoneId: BASE.ZONE_ID.ID1,
  //         });

  //         const expectedFin = {
  //           accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
  //           accountStatus: BASE.STATUS.ACTIVE,
  //           balance: '0',
  //           reasonCode: BASE.REASON_CODE1,
  //           appliedAt: '0',
  //           registeredAt: await utils.getLatestBlockTimestamp(),
  //           terminatingAt: '0',
  //           terminatedAt: '0',
  //           mintLimit: '3000',
  //           burnLimit: '4000',
  //           chargeLimit: '2000',
  //           transferLimit: '1000',
  //           cumulativeLimit: '5000',
  //           cumulativeAmount: '0',
  //           cumulativeDate: '0',
  //         };

  //         const expectedBiz = [
  //           {
  //             accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
  //             zoneName: BASE.ZONE_NAME.NAME1,
  //             balance: '0',
  //             accountStatus: BASE.STATUS.APPLYING,
  //             appliedAt: await utils.getLatestBlockTimestamp(),
  //             registeredAt: '0',
  //             terminatingAt: '0',
  //             terminatedAt: '0',
  //           },
  //         ];

  //         // BizZone管理のアカウント情報を取得
  //         const result = await validatorFuncs.getAccountAll(valid, BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID);

  //         utils.assertEqualForEachField(result.accountDataAll, expectedFin);
  //         utils.assertEqualForEachField(result.accountDataAll.businessZoneAccounts, expectedBiz);
  //         assert.equal(result.err, '');
  //       });

  //       it('FinZone管理のBusinessZoneAccountの解約申し込みができること', async () => {
  //         // 登録申し込みしたアカウントを登録
  //         await validatorFuncs.setActiveBusinessAccountWithZone(valid, accounts, {
  //           validatorId: BASE.VALID.VALID0.ID,
  //           zoneId: BASE.ZONE_ID.ID1,
  //           accountId: BASE.ACCOUNT.ACCOUNT1.ID,
  //         });

  //         // 解約申し込みを行う
  //         await validatorFuncs.syncBusinessZoneStatus(valid, accounts, {
  //           accountId: BASE.ACCOUNT.ACCOUNT1.ID,
  //           zoneId: BASE.ZONE_ID.ID1,
  //           accountStatus: BASE.STATUS.TERMINATING,
  //         });

  //         const expectedFin = {
  //           accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
  //           accountStatus: BASE.STATUS.ACTIVE,
  //           balance: '0',
  //           reasonCode: BASE.REASON_CODE1,
  //           appliedAt: '0',
  //           registeredAt: await utils.getLatestBlockTimestamp(),
  //           terminatingAt: '0',
  //           terminatedAt: '0',
  //           mintLimit: '3000',
  //           burnLimit: '4000',
  //           chargeLimit: '2000',
  //           transferLimit: '1000',
  //           cumulativeLimit: '5000',
  //           cumulativeAmount: '0',
  //           cumulativeDate: '0',
  //         };

  //         const expectedBiz = [
  //           {
  //             accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
  //             zoneName: BASE.ZONE_NAME.NAME1,
  //             balance: '0',
  //             accountStatus: BASE.STATUS.TERMINATING,
  //             appliedAt: await utils.getLatestBlockTimestamp(),
  //             registeredAt: await utils.getLatestBlockTimestamp(),
  //             terminatingAt: await utils.getLatestBlockTimestamp(),
  //             terminatedAt: '0',
  //           },
  //         ];

  //         // BizZone管理のアカウント情報を取得
  //         const result = await validatorFuncs.getAccountAll(valid, BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID);

  //         utils.assertEqualForEachField(result.accountDataAll, expectedFin);
  //         utils.assertEqualForEachField(result.accountDataAll.businessZoneAccounts, expectedBiz);
  //         assert.equal(result.err, '');
  //       });
  //     });
  //   });

  //   describe('準正常系', () => {
  //     before(async () => {
  //       [prov, issuer, valid, account] = await contractFixture<BusinessZoneAccountContractType>();
  //       contractManager = await contractManagerFactory();
  //     });

  //     describe('provider, providerRole, issuer, validator, token, account, 1つのbusinessZoneAccountが解約申込となっている状態', () => {
  //       const ibcAddress = accounts[0];

  //       before(async () => {
  //         await providerFuncs.addProvider(prov, accounts, { zoneId: BASE.ZONE_ID.ID0, zoneName: BASE.ZONE_NAME.NAME0 });
  //         await providerFuncs.addProviderRole(prov, accounts, {
  //           providerEoa: accounts[BASE.EOA.PROV2],
  //         });
  //         await issuerFuncs.addIssuer(issuer, accounts);
  //         await validatorFuncs.addValidator(valid, accounts);
  //         await providerFuncs.addToken(prov, accounts, { eoaKey: BASE.EOA.PROV2 });
  //         await Promise.all(
  //           [BASE.ACCOUNT.ACCOUNT1, BASE.ACCOUNT.ACCOUNT2, BASE.ACCOUNT.ACCOUNT3, BASE.ACCOUNT.ACCOUNT4].map((v) =>
  //             validatorFuncs.addAccount(valid, accounts, { accountId: v.ID }),
  //           ),
  //         );
  //         await contractManagerFuncs.setIbcApp(contractManager, accounts, ibcAddress, BASE.IBCAPP_NAME.ACCOUNT_SYNC);
  //         await validatorFuncs.syncBusinessZoneStatus(valid, accounts, {
  //           accountId: BASE.ACCOUNT.ACCOUNT1.ID,
  //           accountStatus: BASE.STATUS.APPLYING,
  //           zoneId: BASE.ZONE_ID.ID1,
  //         });
  //       });
  //       it('アカウント申し込み時に既に申し込み済みのアカウントIDを指定した場合、エラー終了すること', async () => {
  //         await truffleAssert.reverts(
  //           validatorFuncs.syncBusinessZoneStatus(valid, accounts, {
  //             zoneId: BASE.ZONE_ID.ID1,
  //             accountId: BASE.ACCOUNT.ACCOUNT1.ID,
  //             accountStatus: BASE.STATUS.APPLYING,
  //           }),
  //           ERR.ACCOUNT.ACCOUNT_ID_EXIST,
  //         );
  //       });
  //       it('アカウント解約申し込み時に存在しないアカウントを指定した場合、エラー終了すること', async () => {
  //         await truffleAssert.reverts(
  //           validatorFuncs.syncBusinessZoneStatus(valid, accounts, {
  //             zoneId: BASE.ZONE_ID.ID1,
  //             accountId: BASE.ACCOUNT.ACCOUNT10.ID,
  //             accountStatus: BASE.STATUS.TERMINATING,
  //           }),
  //           ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
  //         );
  //       });
  //     });
  //   });
  // });
})
