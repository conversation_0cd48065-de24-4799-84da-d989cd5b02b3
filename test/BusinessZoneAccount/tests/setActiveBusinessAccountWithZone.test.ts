import { contractManagerFuncs } from '@/test/ContractManager/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'

describe('setActiveBusinessAccountWithZone()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance

  describe('正常系', () => {
    let ibcAddress
    before(async () => {
      ;({ accounts, provider, issuer, validator, businessZoneAccount, token, contractManager } =
        await contractFixture<BusinessZoneAccountContractType>())
      ibcAddress = await accounts[0]
    })

    describe('account, ibcAppAddressが登録されている状態', () => {
      before(async () => {
        const ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator: validator, accounts: accounts, options: { accountId } })
        }
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 500,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
        })

        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME0,
            BASE.ACCOUNT.ACCOUNT0.ID,
            BASE.ACCOUNT.ACCOUNT0.NAME,
            BASE.STATUS.ACTIVE,
            BASE.TRACE_ID,
          )
      })

      describe('初期状態', () => {
        it('呼び出し元がValidatorではない場合、エラーがスローされること', async () => {
          await expect(
            businessZoneAccount
              .connect(accounts[0])
              .setActiveBusinessAccountWithZone(BASE.ZONE_ID.ID0, BASE.ACCOUNT.ACCOUNT1.ID, BASE.TRACE_ID),
          ).to.be.revertedWith(ERR.VALID.NOT_VALIDATOR_CONTRACT)
        })
        it('呼び出し元がValidatorの場合、イベントが発行され、アクティブに更新されていること', async () => {
          const setActiveBusinessAccountWithZoneTx = await validatorFuncs.setActiveBusinessAccountWithZone({
            validator: validator,
            accounts: accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              zoneId: BASE.ZONE_ID.ID1,
              accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            },
          })
          const expectParams = {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            traceId: BASE.TRACE_ID,
          }
          await expect(setActiveBusinessAccountWithZoneTx)
            .to.emit(businessZoneAccount, 'SetActiveBusinessAccountWithZone')
            .withArgs(...Object.values(expectParams))
          const result = await businessZoneAccount.getBusinessZoneAccount(BASE.ZONE_ID.ID1, BASE.ACCOUNT.ACCOUNT0.ID)
          utils.assertEqualForEachField(result, { accountStatus: BASE.STATUS.ACTIVE })
        })
      })
    })
  })

  describe('準正常系', () => {
    let ibcAddress
    before(async () => {
      ibcAddress = await accounts[0]
      ;({ accounts, provider, issuer, validator, businessZoneAccount, token, contractManager } =
        await contractFixture<BusinessZoneAccountContractType>())
    })

    describe('初期状態', () => {
      it('should revert when caller is not validator contract', async () => {
        await expect(
          businessZoneAccount.setActiveBusinessAccountWithZone(
            BASE.ZONE_ID.ID0,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.TRACE_ID,
          ),
        ).to.be.revertedWith(ERR.VALID.NOT_VALIDATOR_CONTRACT)
      })
    })

    describe('invalid params test', () => {
      before(async () => {
        const ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator: validator, accounts: accounts, options: { accountId } })
        }
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 500,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
        })
        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME0,
            BASE.ACCOUNT.ACCOUNT0.ID,
            BASE.ACCOUNT.ACCOUNT0.NAME,
            BASE.STATUS.ACTIVE,
            BASE.TRACE_ID,
          )
      })

      it('should revert when zoneId is empty', async () => {
        await expect(
          validatorFuncs.setActiveBusinessAccountWithZone({
            validator: validator,
            accounts: accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              zoneId: BASE.ZONE_ID.EMPTY_ID,
              accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })

      it('should revert when accountId is not registered', async () => {
        await expect(
          validatorFuncs.setActiveBusinessAccountWithZone({
            validator: validator,
            accounts: accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              zoneId: BASE.ZONE_ID.ID1,
              accountId: BASE.ACCOUNT.ACCOUNT11.ID,
            },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })
  })
})
