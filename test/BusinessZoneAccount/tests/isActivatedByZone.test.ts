import { contractManagerFuncs } from '@/test/ContractManager/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IssuerInstance,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

describe('isActivatedByZone()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let contractManager: ContractManagerInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, businessZoneAccount, contractManager } =
        await contractFixture<BusinessZoneAccountContractType>())
    })

    describe('初期状態', () => {
      it('zoneIdが不正である場合、エラーがスローされること', async () => {
        const result = await businessZoneAccount.isActivatedByZone(BASE.ZONE_ID.EMPTY_ID, BASE.ACCOUNT.ACCOUNT1.ID)
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL,
        }
        utils.assertEqualForEachField(result, expected)
      })

      it('accountIdが不正である場合、エラーがスローされること', async () => {
        const result = await businessZoneAccount.isActivatedByZone(BASE.ZONE_ID.ID0, BASE.ACCOUNT.EMPTY.ID)
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL,
        }
        utils.assertEqualForEachField(result, expected)
      })
    })

    describe('provider, providerRole, issuer, validator, token, account, 1つのbusinessZoneAccountが解約申込となっている状態', () => {
      let ibcAddress
      let ibcAddressString
      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator: validator, accounts: accounts, options: { accountId } })
        }
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME1,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT1.NAME,
            BASE.STATUS.ACTIVE,
            BASE.TRACE_ID,
          )
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator: validator,
          accounts: accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccount
          .connect(accounts[0])
          .syncBusinessZoneStatus(
            BASE.ZONE_ID.ID1,
            BASE.ZONE_NAME.NAME1,
            BASE.ACCOUNT.ACCOUNT1.ID,
            BASE.ACCOUNT.ACCOUNT1.NAME,
            BASE.STATUS.TERMINATING,
            BASE.TRACE_ID,
          )
      })

      it('アカウントがアクティブでない場合、エラーがスローされること', async () => {
        const result = await businessZoneAccount.isActivatedByZone(BASE.ZONE_ID.ID1, BASE.ACCOUNT.ACCOUNT1.ID)
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_DISABLED,
        }
        utils.assertEqualForEachField(result, expected)
      })
    })
  })
})
