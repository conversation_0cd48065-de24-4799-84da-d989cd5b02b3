import '@nomicfoundation/hardhat-chai-matchers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { BusinessZoneAccountInstance } from '@test/common/types'
import { assert } from 'chai'
import { businessZoneAccountFuncs } from '../helpers/function'

describe('version()', () => {
  let businessZoneAccount: BusinessZoneAccountInstance

  describe('正常系', () => {
    before(async () => {
      ;({ businessZoneAccount } = await contractFixture<BusinessZoneAccountContractType>())
    })

    it('versionが取得する', async () => {
      assert.equal(
        await businessZoneAccountFuncs.version({ businessZoneAccount: businessZoneAccount }),
        BASE.APP.VERSION,
        'version',
      )
    })
  })
})
