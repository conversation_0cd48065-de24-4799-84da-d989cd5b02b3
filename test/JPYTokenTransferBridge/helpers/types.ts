import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlMockInstance,
  ContractCallOption,
  IBCHandlerInstance,
  IBCTokenMockInstance,
  JPYTokenTransferBridgeInstance,
  PacketCallOption,
  TransferCallOption,
} from '@test/common/types'

export type JPYTokenTransferBridgeContractType = {
  accounts: SignerWithAddress[]
  jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
  ibcHandler: IBCHandlerInstance
  ibcTokenMock: IBCTokenMockInstance
  accessCtrlMock: AccessCtrlMockInstance
}

export type JPYTokenTransferBridgeType = {
  jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
}

export type SetAddressType = JPYTokenTransferBridgeType & {
  ibcTokenMockAddress: string
  accessCtrlMockAddress: string
  options?: Partial<ContractCallOption & { newAddress: string }>
}

export type RegisterEscrowAccountType = JPYTokenTransferBridgeType & {
  zoneId: Parameters<JPYTokenTransferBridgeInstance['registerEscrowAccount']>[0]
  dstChannelID: Parameters<JPYTokenTransferBridgeInstance['registerEscrowAccount']>[1]
  escrowAccount: Parameters<JPYTokenTransferBridgeInstance['registerEscrowAccount']>[2]
  options?: Partial<ContractCallOption>
}

export type UnregisterEscrowAccountType = JPYTokenTransferBridgeType & {
  zoneId: Parameters<JPYTokenTransferBridgeInstance['unregisterEscrowAccount']>[0]
  dstChannelID: Parameters<JPYTokenTransferBridgeInstance['unregisterEscrowAccount']>[1]
  options?: Partial<ContractCallOption>
}

export type TransferType = JPYTokenTransferBridgeType & {
  options?: Partial<TransferCallOption>
}

export type PacketType = JPYTokenTransferBridgeType & {
  ibcHandler: IBCHandlerInstance
  options?: Partial<PacketCallOption>
}

export type RecoverPacketType = JPYTokenTransferBridgeType & {
  options?: Partial<ContractCallOption & PacketCallOption>
}
