import privateKey from '@/privateKey'
import { BASE } from '@test/common/consts'
import * as utils from '@test/common/utils'
import {
  PacketType,
  RecoverPacketType,
  RegisterEscrowAccountType,
  SetAddressType,
  TransferType,
  UnregisterEscrowAccountType,
} from './types'
import { genPacket } from './utils'
/**
 * JPYTokenTransferBridgeのイベントを呼ぶ関数を持つobject
 */

export const jpyTokenTransferBridgeFuncs = {
  setAddress: async ({
    jpyTokenTransferBridge,
    ibcTokenMockAddress,
    accessCtrlMockAddress,
    options = {},
  }: SetAddressType) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN] } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKeyForSig!, ['bytes32', 'uint256'], [BASE.SALT.TOKEN_TRANSFER, _deadline])
    return jpyTokenTransferBridge.setAddress(ibcTokenMockAddress, accessCtrlMockAddress, _deadline, _sig[0])
  },
  registerEscrowAccount: async ({
    jpyTokenTransferBridge,
    zoneId,
    dstChannelID,
    escrowAccount,
    options = {},
  }: RegisterEscrowAccountType) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN] } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(
        privateKeyForSig!,
        ['uint16', 'uint256', 'bytes32', 'uint256'],
        [zoneId, dstChannelID, escrowAccount, _deadline],
      )
    return jpyTokenTransferBridge.registerEscrowAccount(zoneId, dstChannelID, escrowAccount, _deadline, _sig[0])
  },
  unregisterEscrowAccount: async ({
    jpyTokenTransferBridge,
    zoneId,
    dstChannelID,
    options = {},
  }: UnregisterEscrowAccountType) => {
    const { sig, deadline, privateKeyForSig = privateKey.key[BASE.EOA.ADMIN] } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKeyForSig!, ['uint16', 'uint256', 'uint256'], [zoneId, dstChannelID, _deadline])
    return jpyTokenTransferBridge.unregisterEscrowAccount(zoneId, dstChannelID, _deadline, _sig[0])
  },
  transfer: async ({ jpyTokenTransferBridge, options = {} }: TransferType) => {
    const { accountId, fromZoneId, toZoneId, amount, timeoutHeight, traceId } = options
    return jpyTokenTransferBridge.transfer(
      accountId ?? BASE.BRIDGE.ACCOUNT_A,
      fromZoneId ?? BASE.ZONE.FIN,
      toZoneId ?? BASE.ZONE.BIZ,
      amount ?? BASE.BRIDGE.AMOUNT,
      timeoutHeight ?? BASE.TIMEOUT_HEIGHT,
      traceId ?? BASE.TRACE_ID,
    )
  },
  recvPacket: async ({ ibcHandler, jpyTokenTransferBridge, options = {} }: PacketType) => {
    const { packetData, fromZoneId, toZoneId, amount, ack, transferType } = options
    const packet = genPacket(packetData, fromZoneId, toZoneId, amount, transferType)

    return ibcHandler.recvPacket(await jpyTokenTransferBridge.getAddress(), packet, await ibcHandler.getAddress())
  },
  acknowledgementPacket: async ({ ibcHandler, jpyTokenTransferBridge, options = {} }: PacketType) => {
    const { packetData, fromZoneId, toZoneId, amount, ack, transferType } = options
    const packet = genPacket(packetData, fromZoneId, toZoneId, amount, transferType)

    return ibcHandler.acknowledgementPacket(
      await jpyTokenTransferBridge.getAddress(),
      packet,
      ack ?? BASE.IBC.ACK,
      await ibcHandler.getAddress(),
    )
  },
  timeoutPacket: async ({ ibcHandler, jpyTokenTransferBridge, options = {} }: PacketType) => {
    const { packetData, fromZoneId, toZoneId, amount, transferType } = options
    const packet = genPacket(packetData, fromZoneId, toZoneId, amount, transferType)
    return ibcHandler.timeoutPacket(await jpyTokenTransferBridge.getAddress(), packet, await ibcHandler.getAddress())
  },
  recoverPacket: async ({ jpyTokenTransferBridge, options = {} }: RecoverPacketType) => {
    const {
      sig,
      deadline,
      privateKeyForSig = privateKey.key[BASE.EOA.ADMIN],
      packetData,
      fromZoneId,
      toZoneId,
      amount,
      transferType,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKeyForSig!, ['bytes32', 'uint256'], [BASE.SALT.TOKEN_TRANSFER, _deadline])

    const packet = genPacket(packetData, fromZoneId, toZoneId, amount, transferType)

    return jpyTokenTransferBridge.recoverPacket(packet, await jpyTokenTransferBridge.getAddress(), _deadline, _sig[0])
  },
}
