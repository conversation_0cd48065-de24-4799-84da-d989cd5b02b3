import '@nomicfoundation/hardhat-chai-matchers'

describe('JPYTokenTransferBridge Test', () => {
  require('./tests/initialize.test')
  require('./tests/version.test')
  require('./tests/setAddress.test')
  require('./tests/getConfig.test')
  require('./tests/registerEscrowAccount.test')
  require('./tests/unregisterEscrowAccount.test')
  require('./tests/escrowAccount.test')
  require('./tests/transfer.test')
  require('./tests/onRecvPacket.test')
  require('./tests/onAcknowledgementPacket.test')
  require('./tests/recoverPacket.test')
  require('./tests/onChanOpenInit.test')
  require('./tests/onChanOpenTry.test')

  // timeoutの機能は現在実装不可
  // describe("onTimeoutPacket()", () => {
  //   describe("正常系", () => {
  //     before(async () => {
  //       [jpyTokenTransferBridge, ibc<PERSON><PERSON><PERSON>, tokenMock] =
  //         await contractFixture<JPYTokenTransferBridgeContractType>();
  //     });

  //     describe("EscrowAccountが登録されていて、任意のアカウントが残高を保持している状態", () => {
  //       before(async () => {
  //         await jpyTokenTransferBridgeFuncs.registerEscrowAccount(
  //           jpyTokenTransferBridge,
  //           BASE.ZONE.FIN,
  //           BASE.ZONE.BIZ,
  //           BASE.BRIDGE.ESCROW_ACCOUNT
  //         );
  //       });

  //       beforeEach(async () => {
  //         // IssueVoucherを実行し、アカウントの残高を補填する。
  //         await tokenMock.issueVoucher(
  //           BASE.BRIDGE.ACCOUNT_A,
  //           BASE.BRIDGE.AMOUNT,
  //           BASE.TRACE_ID
  //         );

  //         // TransferToEscrowを実行しEscrowAccountの残高を補填する。
  //         await jpyTokenTransferBridgeFuncs.transfer(jpyTokenTransferBridge, {
  //           amount: BASE.BRIDGE.EXCHANGE_AMOUNT,
  //         });
  //       });

  //       it("FinZoneからBizZoneへの送金がTimeoutで失敗した場合、EscrowAccountからFinZoneアカウントに返金がなされること", async () => {
  //         const accountBalanceBefore = await tokenMock.balanceOf(
  //           BASE.ZONE.FIN,
  //           BASE.BRIDGE.ACCOUNT_A
  //         );
  //         const escrowBalanceBefore = await tokenMock.balanceOf(
  //           BASE.ZONE.FIN,
  //           BASE.BRIDGE.ESCROW_ACCOUNT
  //         );

  //         await jpyTokenTransferBridgeFuncs.timeoutPacket(
  //           ibcHandler,
  //           jpyTokenTransferBridge
  //         );

  //         const accountBalance = await tokenMock.balanceOf(
  //           BASE.ZONE.FIN,
  //           BASE.BRIDGE.ACCOUNT_A
  //         );
  //         const escrowBalance = await tokenMock.balanceOf(
  //           BASE.ZONE.FIN,
  //           BASE.BRIDGE.ESCROW_ACCOUNT
  //         );

  //         assertEqualBn(
  //           accountBalance[0],
  //           accountBalanceBefore[0].add(
  //             web3.utils.toBN(BASE.BRIDGE.EXCHANGE_AMOUNT)
  //           )
  //         );
  //         assertEqualBn(
  //           escrowBalance[0],
  //           escrowBalanceBefore[0].sub(
  //             web3.utils.toBN(BASE.BRIDGE.EXCHANGE_AMOUNT)
  //           )
  //         );
  //       });
  //     });
  //   });
  // });
})
