import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlMockInstance, IBCTokenMockInstance, JPYTokenTransferBridgeInstance } from '@test/common/types'
import { jpyTokenTransferBridgeFuncs } from '@test/JPYTokenTransferBridge/helpers/function'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('setAddress()', () => {
  let accounts: SignerWithAddress[]
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
  let ibcTokenMock: IBCTokenMockInstance
  let accessCtrlMock: AccessCtrlMockInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, jpyTokenTransferBridge, ibcTokenMock, accessCtrlMock } =
        await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    describe('初期状態', () => {
      it('_ibcToken, _accessCtrl can be set normally', async () => {
        await accessCtrlMock.addAdminRole(accounts[0], 0, BASE.TRACE_ID)
        const tx = await jpyTokenTransferBridgeFuncs.setAddress({
          jpyTokenTransferBridge: jpyTokenTransferBridge,
          ibcTokenMockAddress: await ibcTokenMock.getAddress(),
          accessCtrlMockAddress: await accessCtrlMock.getAddress(),
        })

        // TODO: setAddress don't have event and variable is private
        // Should change this to check the value of the variable when event is implemented
        await expect(tx).to.been.ok
      })
    })

    describe('準正常系', () => {
      before(async () => {
        ;({ accounts, jpyTokenTransferBridge, ibcTokenMock, accessCtrlMock } =
          await contractFixture<JPYTokenTransferBridgeContractType>())
      })

      it('should revert when not admin', async () => {
        await expect(
          jpyTokenTransferBridgeFuncs.setAddress({
            jpyTokenTransferBridge: jpyTokenTransferBridge,
            ibcTokenMockAddress: await ibcTokenMock.getAddress(),
            accessCtrlMockAddress: await accessCtrlMock.getAddress(),
            options: {
              privateKeyForSig: privateKey[1],
            },
          }),
        ).to.be.revertedWith(ERR.COMMON.NOT_ADMIN_ROLE)
      })
    })
  })
})
