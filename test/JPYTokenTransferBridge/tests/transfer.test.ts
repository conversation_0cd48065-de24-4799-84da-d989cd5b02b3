import '@nomicfoundation/hardhat-chai-matchers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IBCTokenMockInstance, JPYTokenTransferBridgeInstance } from '@test/common/types'
import { jpyTokenTransferBridgeFuncs } from '@test/JPYTokenTransferBridge/helpers/function'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('transfer()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
  let ibcTokenMock: IBCTokenMockInstance

  describe('正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge, ibcTokenMock } = await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    describe('EscrowAccountが登録されている状態', () => {
      before(async () => {
        await jpyTokenTransferBridgeFuncs.registerEscrowAccount({
          jpyTokenTransferBridge: jpyTokenTransferBridge,
          zoneId: BASE.ZONE.FIN,
          dstChannelID: BASE.ZONE.BIZ,
          escrowAccount: BASE.BRIDGE.ESCROW_ACCOUNT,
        })
      })

      beforeEach(async () => {
        // IssueVoucherを実行し、tokenMockのアカウントの残高を補填する。
        await ibcTokenMock.issueVoucher(BASE.BRIDGE.ACCOUNT_A, BASE.BRIDGE.AMOUNT, BASE.TRACE_ID)
        // IssueVoucherを実行し、ibcTokenMockのアカウントの残高を補填する。
        await ibcTokenMock.issueVoucher(BASE.BRIDGE.ACCOUNT_A, BASE.BRIDGE.AMOUNT, BASE.TRACE_ID)
      })

      it('(TransferToEscrow)FinZoneでIBCのTransferを行った場合に、EscrowAccountの残高が加算されていること', async () => {
        const accountBalanceBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)
        const escrowBalanceBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ESCROW_ACCOUNT)

        // FinZoneからBizZoneへのTransferを実行するので、TransferToEscrowが実行される。
        const tx = await jpyTokenTransferBridgeFuncs.transfer({
          jpyTokenTransferBridge: jpyTokenTransferBridge,
          options: {
            fromZoneId: BASE.ZONE.FIN,
            toZoneId: BASE.ZONE.BIZ,
          },
        })

        const accountBalance = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)
        const escrowBalance = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ESCROW_ACCOUNT)

        await expect(accountBalance[0]).to.be.equal(
          BigInt(accountBalanceBefore[0]) - BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT),
        )

        await expect(escrowBalance[0]).to.be.equal(BigInt(escrowBalanceBefore[0]) + BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT))
      })

      it('(RedeemVoucher)BizZoneでIBCのTransferを行った場合に、BizZoneアカウントの残高が減算されていること', async () => {
        const accountBalanceBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)

        // BizZoneからFinZoneへのTransferを実行するので、RedeemVoucherが実行される。
        await jpyTokenTransferBridgeFuncs.transfer({
          jpyTokenTransferBridge: jpyTokenTransferBridge,
          options: {
            fromZoneId: BASE.ZONE.BIZ,
            toZoneId: BASE.ZONE.FIN,
          },
        })

        const accountBalance = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)

        await expect(accountBalance[0]).to.be.equal(
          BigInt(accountBalanceBefore[0]) - BigInt(BASE.BRIDGE.EXCHANGE_AMOUNT),
        )
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge } = await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    describe('初期状態', () => {
      it('送金先と送金元に同一の領域が設定された場合、エラーがスローされること', async () => {
        await expect(
          jpyTokenTransferBridgeFuncs.transfer({
            jpyTokenTransferBridge: jpyTokenTransferBridge,
            options: {
              fromZoneId: BASE.ZONE.FIN,
              toZoneId: BASE.ZONE.FIN,
            },
          }),
        ).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
    })

    it('EscrowAccountが登録されていない場合、エラーがスローされること', async () => {
      await expect(
        jpyTokenTransferBridgeFuncs.transfer({ jpyTokenTransferBridge: jpyTokenTransferBridge }),
      ).to.be.revertedWith(ERR.IBC.IBC_APP_JPYTT_ESCROW_NOT_REG)
    })
  })
})
