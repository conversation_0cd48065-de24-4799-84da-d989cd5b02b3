import '@nomicfoundation/hardhat-chai-matchers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IBCHandlerInstance, JPYTokenTransferBridgeInstance } from '@test/common/types'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('onChanOpenTry()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance
  let ibcHandler: IBCHandlerInstance

  describe('正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge, ibcHandler } = await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    it('tokenTransferSourceVersion return normally', async () => {
      // Hack: onChanOpenTry of jpyTokenTransferBridge only allow from IBCHandlerMock (_ibcHandler)
      // => but there's no call from _ibcHandler in IBCHandlerMock contract
      // onChanOpenTry also be called from IBCChannelHandshake,
      // => but IBCChannelHandshake is inside yuiContract,
      // => there's no implement of it for testing, so it's not possible to call it directly
      // UNREACABLE: use impersonateAccount to fake call from _ibcHandler
      // TODO: should be refactored when IBCChannelHandshake is implemented and use
      // => or onChanOpenTry is called from IBCHandlerMock
      await helpers.impersonateAccount(await ibcHandler.getAddress())
      await helpers.setBalance(await ibcHandler.getAddress(), 100n ** 18n)
      const fakeIbcHandler = await ethers.getSigner(await ibcHandler.getAddress())

      const res = await jpyTokenTransferBridge.connect(fakeIbcHandler).onChanOpenTry({
        order: 0,
        connectionHops: [BASE.EMPTY_ADDRESS],
        portId: BASE.TOKEN_TRANS_BRIDGE.PORT,
        channelId: BASE.TOKEN_TRANS_BRIDGE.CHANNEL,
        counterparty: {
          port_id: BASE.TOKEN_TRANS_BRIDGE.PORT,
          channel_id: BASE.TOKEN_TRANS_BRIDGE.CHANNEL,
        },
        counterpartyVersion: BASE.TOKEN_TRANS_BRIDGE.VERSION,
      })
      await helpers.stopImpersonatingAccount(await ibcHandler.getAddress())
      await expect(res).to.be.equal(BASE.TOKEN_TRANS_BRIDGE.VERSION)
    })
  })
  describe('準正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge } = await contractFixture<JPYTokenTransferBridgeContractType>())
    })

    it('should revert when caller is not ibc', async () => {
      await expect(
        jpyTokenTransferBridge.onChanOpenTry({
          order: 0,
          connectionHops: [BASE.EMPTY_ADDRESS],
          portId: BASE.TOKEN_TRANS_BRIDGE.PORT,
          channelId: BASE.TOKEN_TRANS_BRIDGE.CHANNEL,
          counterparty: {
            port_id: BASE.TOKEN_TRANS_BRIDGE.PORT,
            channel_id: BASE.TOKEN_TRANS_BRIDGE.CHANNEL,
          },
          counterpartyVersion: BASE.TOKEN_TRANS_BRIDGE.VERSION,
        }),
      ).to.be.revertedWith('IBCAppBase: caller is not the IBC contract')
    })
    it('should revert when msg_.counterpartyVersion is mismatch', async () => {
      // TODO: should be refactored when IBCChannelHandshake is implemented and use
      // => or onChanOpenTry is called from IBCHandlerMock
      await helpers.impersonateAccount(await ibcHandler.getAddress())
      await helpers.setBalance(await ibcHandler.getAddress(), 100n ** 18n)
      const fakeIbcHandler = await ethers.getSigner(await ibcHandler.getAddress())

      await expect(
        jpyTokenTransferBridge.connect(fakeIbcHandler).onChanOpenTry({
          order: 0,
          connectionHops: [BASE.EMPTY_ADDRESS],
          portId: BASE.TOKEN_TRANS_BRIDGE.PORT,
          channelId: BASE.TOKEN_TRANS_BRIDGE.CHANNEL,
          counterparty: {
            port_id: BASE.TOKEN_TRANS_BRIDGE.PORT,
            channel_id: BASE.TOKEN_TRANS_BRIDGE.CHANNEL,
          },
          counterpartyVersion: 'false-version',
        }),
      ).to.be.revertedWith('version mismatch')
      await helpers.stopImpersonatingAccount(await ibcHandler.getAddress())
    })
  })
})
