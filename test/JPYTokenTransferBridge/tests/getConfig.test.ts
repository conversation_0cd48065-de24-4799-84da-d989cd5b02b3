import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { JPYTokenTransferBridgeInstance } from '@test/common/types'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getConfig()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance

  describe('正常系', () => {
    before(async () => {
      ;({ jpyTokenTransferBridge } = await contractFixture<JPYTokenTransferBridgeContractType>())
    })
    describe('初期状態', () => {
      it('version情報を取得できること', async () => {
        const config = await jpyTokenTransferBridge.getConfig()
        assert.equal(config.port, BASE.TOKEN_TRANS_BRIDGE.PORT, 'source port')
        assert.equal(config.channel, BASE.TOKEN_TRANS_BRIDGE.CHANNEL, 'source channel')
        assert.equal(config.version, BASE.TOKEN_TRANS_BRIDGE.VERSION, 'source version')
      })
    })
  })
})
