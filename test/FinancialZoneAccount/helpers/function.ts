import {
  AddAccountLimitType,
  AddCumlativeAmountType,
  CheckTransferType,
  FinancialZoneAccountType,
  GetAccountLimitDataType,
  HasAccountType,
  ModAccountLimitType,
  SubtractCumulativeAmountType,
  SyncBurnType,
  SyncChargeType,
  SyncCumulativeResetType,
  SyncMintType,
  SyncTransferType,
} from './types'

/**
 * financialZoneAccountのイベントを呼ぶ関数を持つobject
 */
export const financialZoneAccountFuncs = {
  version: ({ finAccount }: FinancialZoneAccountType) => {
    return finAccount.version()
  },

  addAccountLimit: ({ finAccount, params }: AddAccountLimitType) => {
    return finAccount.addAccountLimit(...params)
  },

  modAccountLimit: ({ finAccount, params }: ModAccountLimitType) => {
    return finAccount.modAccountLimit(...params)
  },

  syncCumulativeReset: ({ finAccount, params }: SyncCumulativeResetType) => {
    return finAccount.syncCumulativeReset(...params)
  },

  addCumulativeAmount: ({ finAccount, params }: AddCumlativeAmountType) => {
    return finAccount.addCumlativeAmount(...params)
  },

  subtractCumulativeAmount: ({ finAccount, params }: SubtractCumulativeAmountType) => {
    return finAccount.subtractCumulativeAmount(...params)
  },

  syncMint: ({ finAccount, params }: SyncMintType) => {
    return finAccount.syncMint(...params)
  },

  syncBurn: ({ finAccount, params }: SyncBurnType) => {
    return finAccount.syncBurn(...params)
  },

  syncCharge: ({ finAccount, params }: SyncChargeType) => {
    return finAccount.syncCharge(...params)
  },

  syncTransfer: ({ finAccount, params }: SyncTransferType) => {
    return finAccount.syncTransfer(...params)
  },

  hasAccount: ({ finAccount, params }: HasAccountType) => {
    return finAccount.hasAccount(...params)
  },

  getAccountLimitData: ({ finAccount, params }: GetAccountLimitDataType) => {
    return finAccount.getAccountLimitData(...params)
  },

  checkTransfer: ({ finAccount, params }: CheckTransferType) => {
    return finAccount.checkTransfer(...params)
  },
}
