import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  FinancialZoneAccountInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'

export type FinancialZoneAccountContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  financialZoneAccount: FinancialZoneAccountInstance
  token: TokenInstance
  contractManager: ContractManagerInstance
  businessZoneAccount: BusinessZoneAccountInstance
}

export type FinancialZoneAccountType = { finAccount: FinancialZoneAccountInstance }

export type AddAccountLimitType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['addAccountLimit']>
}

export type ModAccountLimitType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['modAccountLimit']>
}

export type SyncCumulativeResetType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['syncCumulativeReset']>
}

export type AddCumlativeAmountType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['addCumlativeAmount']>
}

export type SubtractCumulativeAmountType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['subtractCumulativeAmount']>
}

export type SyncMintType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['syncMint']>
}

export type SyncBurnType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['syncBurn']>
}

export type SyncChargeType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['syncCharge']>
}

export type SyncTransferType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['syncTransfer']>
}

export type HasAccountType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['hasAccount']>
}

export type GetAccountLimitDataType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['getAccountLimitData']>
}

export type CheckTransferType = FinancialZoneAccountType & {
  params: Parameters<FinancialZoneAccountInstance['checkTransfer']>
}
