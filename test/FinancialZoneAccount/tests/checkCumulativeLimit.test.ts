import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { toBytes32 } from '@test/common/utils'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('checkCumulativeLimit', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('exceed limit', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('should return false and error when exceed daily limit', async () => {
        const param = {
          transferLimit: 100,
          chargeLimit: 200,
          mintLimit: 600,
          burnLimit: 400,
          cumulativeLimit: 500,
        }
        const limitAmounts = [
          param.transferLimit,
          param.chargeLimit,
          param.mintLimit,
          param.burnLimit,
          param.cumulativeLimit,
        ]
        await financialZoneAccountFuncs.addAccountLimit({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, limitAmounts, BASE.TRACE_ID],
        })
        const result = await financialZoneAccount.checkTransactionLimits(
          BASE.ACCOUNT.ACCOUNT1.ID,
          600,
          toBytes32(`checkMint`),
        )
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT)
      })
    })
  })
})
