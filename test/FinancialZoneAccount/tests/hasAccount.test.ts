import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('hasAccount()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance
  // #TODO: this hasn't implemented yet, must changed after implementation
  describe('準正常系', () => {
    before(async () => {
      ;({ financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('account is not valid', () => {
      it('should return false and error message if accountId == 0x00', async () => {
        const result = await financialZoneAccount.hasAccount(BASE.ACCOUNT.EMPTY.ID)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_INVALID_VAL, 'err')
      })

      it('should return nothing when accountId != 0x00', async () => {
        const result = await financialZoneAccount.hasAccount(BASE.ACCOUNT.ACCOUNT0.ID)
        utils.assertEqualForEachField(result, {})
      })
    })
  })
})
