import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('syncCharge()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  // TODO: TokenコントラクトにaddCumlativeAmount()関数実装後修正する
  const cumulativeAmount = 200

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('cumulativeAmountが0の状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('日付が変わっていない場合、cumulativeAmountが加算されること', async () => {
        const jstDay = await utils.getJSTDay()
        const amount = 100

        const beforeResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const tx = await financialZoneAccountFuncs.syncCharge({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: Number(beforeResult.accountData.cumulativeAmount) + amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncCharge')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(
          Number(afterResult.accountData.cumulativeAmount),
          Number(beforeResult.accountData.cumulativeAmount) + amount,
        )
      })

      it('日付が変った場合、cumulativeAmountがリセットされた上で加算されること', async () => {
        const amount = 100

        await time.increase(24 * 60 * 60)
        const jstDay = await utils.getJSTDay()

        const tx = await financialZoneAccountFuncs.syncCharge({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })
        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncCharge')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(Number(afterResult.accountData.cumulativeAmount), amount)
      })
    })
  })
})
