import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('addAccountLimit()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('provider, providerRole, issuer, validator, account, tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })
      it('アカウント限度額追加できること', async () => {
        const param = {
          transferLimit: 1100,
          chargeLimit: 1200,
          mintLimit: 1300,
          burnLimit: 1400,
          cumulativeLimit: 1500,
        }
        const limitAmounts = [
          param.transferLimit,
          param.chargeLimit,
          param.mintLimit,
          param.burnLimit,
          param.cumulativeLimit,
        ]
        await financialZoneAccountFuncs.addAccountLimit({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, limitAmounts, BASE.TRACE_ID],
        })
        const result = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(Number(result.accountData.transferLimit), param.transferLimit)
        assert.equal(Number(result.accountData.chargeLimit), param.chargeLimit)
        assert.equal(Number(result.accountData.mintLimit), param.mintLimit)
        assert.equal(Number(result.accountData.burnLimit), param.burnLimit)
        assert.equal(Number(result.accountData.cumulativeLimit), param.cumulativeLimit)
      })
    })
  })
  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('共通領域Accountが登録されていない状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
      })

      it('LimitAmountsのアイテムは5ではない場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, [100, 200, 300, 400], BASE.TRACE_ID],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.LIMIT_AMOUNT_INVALID_COUNT)
      })

      it('limit amount in FinancialZoneAccountLib', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, [100, 200, 300, 400], BASE.TRACE_ID],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.LIMIT_AMOUNT_INVALID_COUNT)
      })
    })
  })
})
