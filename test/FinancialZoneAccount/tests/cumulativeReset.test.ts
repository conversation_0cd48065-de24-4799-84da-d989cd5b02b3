import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance } from '@test/common/types'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('cumulativeReset()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance

  describe('準正常系', () => {
    before(async () => {
      ;({ financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('caller is not valid', () => {
      it('should revert when caller is not issuer contract', async () => {
        await expect(financialZoneAccount.cumulativeReset(BASE.ACCOUNT.ACCOUNT0.ID)).to.be.revertedWith(
          ERR.ISSUER.NOT_ISSUER_CONTRACT,
        )
      })
    })
  })
})

describe('cumulativeReset()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance
  describe('準正常系', () => {
    before(async () => {
      ;({ financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('caller is not valid', () => {
      it('should revert when caller is not issuer contract', async () => {
        await expect(financialZoneAccount.cumulativeReset(BASE.ACCOUNT.ACCOUNT0.ID)).to.be.revertedWith(
          ERR.ISSUER.NOT_ISSUER_CONTRACT,
        )
      })

      it('transferLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, [*************, 200, 300, 400, 500], BASE.TRACE_ID],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_TRANSFER_LIMIT)
      })

      it('chargeLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, [100, *************, 300, 400, 500], BASE.TRACE_ID],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_CHARGE_LIMIT)
      })

      it('mintLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, [100, 200, *************, 400, 500], BASE.TRACE_ID],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_MINT_LIMIT)
      })

      it('burnLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, [100, 200, 300, *************, 500], BASE.TRACE_ID],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_BURN_LIMIT)
      })

      it('cumulativeLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, [100, 200, 300, 400, ****************], BASE.TRACE_ID],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT)
      })
    })
  })
})
