import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  FinancialZoneAccountInstance,
  IssuerInstance,
  ModTokenLimitOption,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import _ from 'lodash'
import { before } from 'mocha'

describe('modAccountLimit', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('itemFlgs is false', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('no data change when itemFlgs is false', async () => {
        const originalLimitAmounts = [1000, 2000, 3000, 4000, 5000]

        const params: ModTokenLimitOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          itemFlgs: [false, false, false, false, false],
          limitAmounts: [100, 100, 1000, 1000, 10],
        }

        const { issuerId, ...expected } = params

        const tx = await issuerFuncs.modTokenLimit({ issuer: issuer, accounts: accounts, options: params })

        await expect(tx)
          .to.emit(issuer, 'ModTokenLimit')
          .withArgs(
            BASE.VALID.VALID0.ID,
            ...Object.values(_.pick(expected, ['accountId', 'itemFlgs'])),
            originalLimitAmounts,
            BASE.TRACE_ID,
          )
      })
    })
  })
})

describe('modAccountLimit()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance

  describe('準正常系', () => {
    before(async () => {
      ;({ financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('caller is not valid', () => {
      it('should revert when caller is not issuer contract', async () => {
        await expect(financialZoneAccount.modAccountLimit(BASE.ACCOUNT.ACCOUNT0.ID, [true], [100])).to.be.revertedWith(
          ERR.ISSUER.NOT_ISSUER_CONTRACT,
        )
      })
    })
  })
})
