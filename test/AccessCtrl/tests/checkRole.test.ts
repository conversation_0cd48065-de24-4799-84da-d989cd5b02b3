import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractInitialize } from '@test/AccessCtrl/helpers/contractInitialize'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType, FuncParamsType } from '@test/AccessCtrl/helpers/types'
import { genRoleByPrefix } from '@test/AccessCtrl/helpers/utils'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { before } from 'mocha'

const ROLLED_EOA_KEY = {
  DEFAULT_ADMIN: 0,
  ADMIN: 1,
  NOT_ADMIN: 8,
}

describe('checkRole()', () => {
  const providerId = BASE.PROV.PROV0.ID
  const PROVIDER_ROLE_EOA_KEY = 2

  describe('正常系', () => {
    let sender
    let accessCtrl: AccessCtrlInstance
    let accounts: SignerWithAddress[]

    before(async () => {
      ;({ accounts } = await contractFixture<AccessCtrlContractType>())
      sender = await accounts[7]
    })

    before(async () => {
      ;({ accessCtrl } = await contractInitialize({
        accounts: accounts,
        customAddress: { provider: await sender.getAddress() },
      }))
    })

    describe('アカウントにProvider権限が付与されている状態', () => {
      let role: string
      let rolledAccount

      before(async () => {
        rolledAccount = await accounts[PROVIDER_ROLE_EOA_KEY]
      })

      before(async () => {
        role = await genRoleByPrefix({
          accessCtrl: accessCtrl,
          getRolePrefixFuncName: 'ROLE_PREFIX_PROV',
          id: providerId,
        })
        await accessCtrlFuncs.addRoleByProv({
          accessCtrl: accessCtrl,
          accounts: accounts,
          providerId: providerId,
          role: role,
          account: await rolledAccount.getAddress(),
          options: {
            sender,
          },
        })
      })

      it('指定した権限がある場合、trueが返されること', async () => {
        const params: FuncParamsType['checkRole'] = {
          accessCtrl: accessCtrl,
          role: role,
          account: await rolledAccount.getAddress(),
          options: { eoaKey: PROVIDER_ROLE_EOA_KEY },
        }
        const result = await accessCtrlFuncs.checkRole(params)

        utils.assertEqualForEachField(result, { has: true, err: '' })
      })

      it('Provider権限でない署名の場合、falseが返されること', async () => {
        const params: FuncParamsType['checkRole'] = {
          accessCtrl: accessCtrl,
          role: role,
          account: await rolledAccount.getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN },
        }
        const result = await accessCtrlFuncs.checkRole(params)

        utils.assertEqualForEachField(result, { has: false, err: '' })
      })

      it('署名期限切れの場合、falseが返されること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()
        const params: FuncParamsType['checkRole'] = {
          accessCtrl: accessCtrl,
          role: role,
          account: await rolledAccount.getAddress(),
          options: { eoaKey: PROVIDER_ROLE_EOA_KEY, deadline: exceededDeadline },
        }

        const result = await accessCtrlFuncs.checkRole(params)

        utils.assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })

      it('署名が無効の場合、falseが返されること', async () => {
        const params: FuncParamsType['checkRole'] = {
          accessCtrl: accessCtrl,
          role: role,
          account: await rolledAccount.getAddress(),
          options: { eoaKey: PROVIDER_ROLE_EOA_KEY, sig: ['0x123456', ''] },
        }
        const result = await accessCtrlFuncs.checkRole(params)

        utils.assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('チェック対象の権限が空（0）の場合、falseが返されること', async () => {
        const params: FuncParamsType['checkRole'] = {
          accessCtrl: accessCtrl,
          role: await utils.toBytes32(''),
          account: await rolledAccount.getAddress(),
          options: { eoaKey: PROVIDER_ROLE_EOA_KEY },
        }
        const result = await accessCtrlFuncs.checkRole(params)

        utils.assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_NOT_ROLE })
      })
    })
  })
})
