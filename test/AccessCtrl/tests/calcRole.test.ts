import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType, FuncParamsType } from '@test/AccessCtrl/helpers/types'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { before } from 'mocha'
import Web3 from 'web3'

// Truffle global vars
declare let web3: Web3

// Chai global vars
declare let assert: Chai.Assert

describe('calcRole()', () => {
  let accessCtrl: AccessCtrlInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accessCtrl } = await contractFixture<AccessCtrlContractType>())
    })

    describe('初期状態', () => {
      it('権限値の算出結果が取得できること', async () => {
        const id = utils.toBytes32('x1')
        const prefix_prov = await accessCtrl.ROLE_PREFIX_PROV()
        const calcRoleProvParams: FuncParamsType['calcRole'] = { accessCtrl: accessCtrl, prefix: prefix_prov, id: id }
        const role_prov = await accessCtrlFuncs.calcRole(calcRoleProvParams)

        const abi_prov = web3.eth.abi.encodeParameters(['bytes32', 'uint256'], [prefix_prov, id])
        assert.equal(role_prov, web3.utils.keccak256(abi_prov), 'calcRole Prov')

        const prefix_issuer = await accessCtrl.ROLE_PREFIX_ISSUER()
        const calcRoleIssuerParams: FuncParamsType['calcRole'] = {
          accessCtrl: accessCtrl,
          prefix: prefix_issuer,
          id: id,
        }
        const role_issuer = await accessCtrlFuncs.calcRole(calcRoleIssuerParams)

        const abi_issuer = web3.eth.abi.encodeParameters(['bytes32', 'uint256'], [prefix_issuer, id])
        assert.equal(role_issuer, web3.utils.keccak256(abi_issuer), 'calcRole Issuer')

        assert.notEqual(role_prov, role_issuer, 'calcRole by same id')
      })
    })
  })
})
