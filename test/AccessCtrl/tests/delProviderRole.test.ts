import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractInitialize } from '@test/AccessCtrl/helpers/contractInitialize'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType, FuncParamsType } from '@test/AccessCtrl/helpers/types'
import { genRoleByPrefix } from '@test/AccessCtrl/helpers/utils'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

const ROLLED_EOA_KEY = {
  DEFAULT_ADMIN: 0,
  ADMIN: 1,
  NOT_ADMIN: 8,
}

describe('delProviderRole()', () => {
  const providerId = BASE.PROV.PROV0.ID

  describe('正常系', () => {
    let sender
    let accessCtrl: AccessCtrlInstance
    let accounts: SignerWithAddress[]

    before(async () => {
      ;({ accounts } = await contractFixture<AccessCtrlContractType>())
      sender = await accounts[7]
    })

    before(async () => {
      ;({ accessCtrl } = await contractInitialize({
        accounts: accounts,
        customAddress: { provider: await sender.getAddress() },
      }))
    })

    describe('アカウントにProvider権限が付与されている状態', () => {
      let role: string
      let rolledAccount

      before(async () => {
        rolledAccount = await accounts[2]
        role = await genRoleByPrefix({
          accessCtrl: accessCtrl,
          getRolePrefixFuncName: 'ROLE_PREFIX_PROV',
          id: providerId,
        })
        await accessCtrlFuncs.addRoleByProv({
          accessCtrl: accessCtrl,
          accounts: accounts,
          providerId: providerId,
          role: role,
          account: await rolledAccount.getAddress(),
          options: {
            sender: sender,
          },
        })
      })

      it('Provider権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと', async () => {
        const params: FuncParamsType['delProviderRole'] = {
          accessCtrl: accessCtrl,
          accounts: accounts,
          providerId: providerId,
          account: await accounts[9].getAddress(),
        }
        const tx = await accessCtrlFuncs.delProviderRole(params)

        await expect(tx).to.not.emit(accessCtrl, 'RoleRevoked')
      })

      it('権限が削除されること', async () => {
        const delProviderRoleParams: FuncParamsType['delProviderRole'] = {
          accessCtrl: accessCtrl,
          accounts: accounts,
          providerId: providerId,
          account: await rolledAccount.getAddress(),
        }
        const tx = await accessCtrlFuncs.delProviderRole(delProviderRoleParams)

        const expectParams = {
          role,
          account: await rolledAccount.getAddress(),
          sender: await accounts[ROLLED_EOA_KEY.DEFAULT_ADMIN].getAddress(),
        }
        await expect(tx)
          .to.emit(accessCtrl, 'RoleRevoked')
          .withArgs(...Object.values(expectParams))
        const hasRoleParams: FuncParamsType['hasRole'] = {
          accessCtrl: accessCtrl,
          role: role,
          account: await rolledAccount.getAddress(),
        }
        assert.notOk(await accessCtrlFuncs.hasRole(hasRoleParams))
      })
    })
  })

  describe('準正常系', () => {
    let sender
    let accessCtrl: AccessCtrlInstance
    let accounts: SignerWithAddress[]

    before(async () => {
      ;({ accounts } = await contractFixture<AccessCtrlContractType>())
      sender = await accounts[7]
    })

    before(async () => {
      ;({ accessCtrl } = await contractInitialize({
        accounts: accounts,
        customAddress: { provider: await sender.getAddress() },
      }))
    })

    describe('アカウントにProvider権限が付与されている状態', () => {
      let role: string
      let rolledAccount

      before(async () => {
        rolledAccount = await accounts[2]
        role = await genRoleByPrefix({
          accessCtrl: accessCtrl,
          getRolePrefixFuncName: 'ROLE_PREFIX_PROV',
          id: providerId,
        })
        await accessCtrlFuncs.addRoleByProv({
          accessCtrl: accessCtrl,
          accounts: accounts,
          providerId: providerId,
          role: role,
          account: await rolledAccount.getAddress(),
          options: {
            sender: sender,
          },
        })
      })

      it('呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること', async () => {
        const params: FuncParamsType['delProviderRole'] = {
          accessCtrl: accessCtrl,
          accounts: accounts,
          providerId: providerId,
          account: await rolledAccount.getAddress(),
          options: { sender: accounts[ROLLED_EOA_KEY.NOT_ADMIN] },
        }
        await expect(accessCtrlFuncs.delProviderRole(params)).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })
    })
  })
})
