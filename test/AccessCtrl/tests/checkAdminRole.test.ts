import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType, FuncParamsType } from '@test/AccessCtrl/helpers/types'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

const ROLLED_EOA_KEY = {
  DEFAULT_ADMIN: 0,
  ADMIN: 1,
  NOT_ADMIN: 8,
}

describe('checkAdminRole()', () => {
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
    })

    describe('アカウントにAdmin権限が付与されている状態', () => {
      before(async () => {
        await accessCtrlFuncs.addAdminRole({
          accessCtrl: accessCtrl,
          from: accounts[9],
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        })
        await accessCtrlFuncs.addAdminRole({
          accessCtrl: accessCtrl,
          from: accounts[9],
          account: await accounts[3].getAddress(),
        })
      })

      it('Admin権限がある場合、trueが返されること', async () => {
        const params: FuncParamsType['checkAdminRole'] = {
          accessCtrl: accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN },
        }
        const result = await accessCtrlFuncs.checkAdminRole(params)

        utils.assertEqualForEachField(result, { has: true, err: '' })
      })

      it('Admin権限でない署名の場合、falseが返されること', async () => {
        const params: FuncParamsType['checkAdminRole'] = {
          accessCtrl: accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.NOT_ADMIN },
        }
        const result = await accessCtrlFuncs.checkAdminRole(params)

        utils.assertEqualForEachField(result, { has: false, err: '' })
      })

      it('署名期限切れの場合、falseが返されること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()
        const params: FuncParamsType['checkAdminRole'] = {
          accessCtrl: accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN, deadline: exceededDeadline },
        }

        const result = await accessCtrlFuncs.checkAdminRole(params)

        utils.assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })

      it('Admin権限でない署名の場合、falseが返されること', async () => {
        const params: FuncParamsType['checkAdminRole'] = {
          accessCtrl: accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.NOT_ADMIN },
        }
        const result = await accessCtrlFuncs.checkAdminRole(params)

        utils.assertEqualForEachField(result, { has: false, err: '' })
      })

      it('署名が無効の場合、falseが返されること', async () => {
        const params: FuncParamsType['checkAdminRole'] = {
          accessCtrl: accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN, sig: ['0x123456', ''] },
        }
        const result = await accessCtrlFuncs.checkAdminRole(params)

        utils.assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })
    })
  })

  describe('準正常系', () => {
    describe('アカウントにAdmin権限が付与されている状態', () => {
      before(async () => {
        await accessCtrlFuncs.addAdminRole({
          accessCtrl: accessCtrl,
          from: accounts[9],
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        })
        await accessCtrlFuncs.addAdminRole({
          accessCtrl: accessCtrl,
          from: accounts[9],
          account: await accounts[3].getAddress(),
        })
      })

      it('署名が無効（not signature）の場合、エラーがスローされること', async () => {
        const bad_sig =
          '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000'
        const params: FuncParamsType['checkAdminRole'] = {
          accessCtrl: accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN, sig: [bad_sig, ''] },
        }
        // ganacheはcallでもrevertする
        await expect(accessCtrlFuncs.checkAdminRole(params)).to.be.reverted // OpenZeppelin revertのため文字列がDCF仕様と異なる
      })
    })
  })
})
