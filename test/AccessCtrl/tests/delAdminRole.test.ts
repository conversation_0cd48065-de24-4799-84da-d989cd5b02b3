import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType, FuncParamsType } from '@test/AccessCtrl/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

const ROLLED_EOA_KEY = {
  DEFAULT_ADMIN: 0,
  ADMIN: 1,
  NOT_ADMIN: 8,
}

describe('delAdminRole()', () => {
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
    })

    describe('アカウントにAdmin権限が付与されている状態', () => {
      before(async () => {
        await accessCtrlFuncs.addAdminRole({
          accessCtrl: accessCtrl,
          from: accounts[9],
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        })
      })

      it('Admin権限のないアカウントを指定した場合、RoleRevokedイベントが発火されないこと', async () => {
        const params: FuncParamsType['delAdminRole'] = {
          accessCtrl: accessCtrl,
          accounts: accounts,
          account: await accounts[9].getAddress(),
        }
        const tx = await accessCtrlFuncs.delAdminRole(params)

        await expect(tx).to.not.emit(accessCtrl, 'RoleRevoked')
      })

      it('権限が削除されること', async () => {
        const delAdminRoleParams: FuncParamsType['delAdminRole'] = {
          accessCtrl: accessCtrl,
          accounts: accounts,
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        }
        const tx = await accessCtrlFuncs.delAdminRole(delAdminRoleParams)

        const expectParams = {
          role: BASE.ROLE.ADMIN,
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
          sender: await accounts[ROLLED_EOA_KEY.DEFAULT_ADMIN].getAddress(),
        }
        await expect(tx)
          .to.emit(accessCtrl, 'RoleRevoked')
          .withArgs(...Object.values(expectParams))
        const hasRoleParams: FuncParamsType['hasRole'] = {
          accessCtrl: accessCtrl,
          role: BASE.ROLE.ADMIN,
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        }
        assert.notOk(await accessCtrlFuncs.hasRole(hasRoleParams))
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
    })

    describe('アカウントにAdmin権限が付与されている状態', () => {
      before(async () => {
        await accessCtrlFuncs.addAdminRole({
          accessCtrl: accessCtrl,
          from: accounts[9],
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        })
      })

      it('権限を削除するアカウントがDEFAULT_ADMIN権限の場合、エラーがスローされること', async () => {
        const params: FuncParamsType['delAdminRole'] = {
          accessCtrl: accessCtrl,
          accounts: accounts,
          account: await accounts[ROLLED_EOA_KEY.DEFAULT_ADMIN].getAddress(),
        }
        await expect(accessCtrlFuncs.delAdminRole(params)).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })

      it('呼び出し元がDEFAULT_ADMIN権限ではない場合、エラーがスローされること', async () => {
        const params: FuncParamsType['delAdminRole'] = {
          accessCtrl: accessCtrl,
          accounts: accounts,
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
          options: { sender: accounts[ROLLED_EOA_KEY.NOT_ADMIN] },
        }
        await expect(accessCtrlFuncs.delAdminRole(params)).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })
    })
  })
})
