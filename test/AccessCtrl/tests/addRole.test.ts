import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType, FuncParamsType } from '@test/AccessCtrl/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

const ROLLED_EOA_KEY = {
  DEFAULT_ADMIN: 0,
  ADMIN: 1,
  NOT_ADMIN: 8,
}

describe('addRole()', () => {
  const role = '0xffeeddccbbaa99887766554433221100ffeeddccbbaa99887766554433221100'
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
    })

    describe('アカウントにAdmin権限が付与されている状態', () => {
      before(async () => {
        await accessCtrlFuncs.addAdminRole({
          accessCtrl: accessCtrl,
          from: accounts[9],
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        })
      })

      it('権限が付与されること', async () => {
        const addRoleParams: FuncParamsType['addRole'] = {
          accessCtrl: accessCtrl,
          from: accounts[9],
          role: role,
          account: await accounts[2].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN },
        }
        const tx = await accessCtrlFuncs.addRole(addRoleParams)

        const expectParams = {
          role,
          account: await accounts[2].getAddress(),
          sender: await accounts[9].getAddress(),
        }
        await expect(tx)
          .to.emit(accessCtrl, 'RoleGranted')
          .withArgs(...Object.values(expectParams))
        const addHasRoleParams: FuncParamsType['hasRole'] = {
          accessCtrl: accessCtrl,
          role: role,
          account: await accounts[2].getAddress(),
        }
        assert.ok(await accessCtrlFuncs.hasRole(addHasRoleParams))
      })
    })

    describe('accounts[2]に権限が付与されている状態', () => {
      it('同一アカウントに同一権限を付与した場合、RoleGrantedイベントが発火されないこと', async () => {
        const params: FuncParamsType['addRole'] = {
          accessCtrl: accessCtrl,
          from: accounts[9],
          role: role,
          account: await accounts[2].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN },
        }
        const tx = await accessCtrlFuncs.addRole(params)

        await expect(tx).to.not.emit(accessCtrl, 'RoleGranted')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
    })

    describe('アカウントにAdmin権限が付与されている状態', () => {
      before(async () => {
        await accessCtrlFuncs.addAdminRole({
          accessCtrl: accessCtrl,
          from: accounts[9],
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        })
      })

      it('権限にAdmin権限を指定した場合、エラーがスローされること', async () => {
        const params: FuncParamsType['addRole'] = {
          accessCtrl: accessCtrl,
          from: accounts[9],
          role: BASE.ROLE.ADMIN,
          account: await accounts[2].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN },
        }
        await expect(accessCtrlFuncs.addRole(params)).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()
        const params: FuncParamsType['addRole'] = {
          accessCtrl: accessCtrl,
          from: accounts[9],
          role: role,
          account: await accounts[2].getAddress(),
          options: { deadline: exceededDeadline, eoaKey: ROLLED_EOA_KEY.ADMIN },
        }

        await expect(accessCtrlFuncs.addRole(params)).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        const params: FuncParamsType['addRole'] = {
          accessCtrl: accessCtrl,
          from: accounts[9],
          role: role,
          account: await accounts[2].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.NOT_ADMIN },
        }
        await expect(accessCtrlFuncs.addRole(params)).to.be.revertedWith(ERR.ACTRL.ACTRL_NOT_ADMIN_ROLE)
      })

      it('署名が無効の場合、エラーがスローされること', async () => {
        const params: FuncParamsType['addRole'] = {
          accessCtrl: accessCtrl,
          from: accounts[9],
          role: role,
          account: await accounts[2].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN, sig: ['0x12345678', ''] },
        }
        await expect(accessCtrlFuncs.addRole(params)).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })
    })
  })
})
