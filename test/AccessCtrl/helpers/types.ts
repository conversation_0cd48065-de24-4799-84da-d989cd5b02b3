import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { AccessCtrlInstance, ContractCallOption, EventParamOptionType, EventReturnType } from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type AccessCtrlContractType = AccessCtrlType & {
  accounts: SignerWithAddress[]
}

type AccessCtrlType = {
  accessCtrl: AccessCtrlInstance
}

type CommonOptions = Partial<
  ContractCallOption & {
    sender?: SignerWithAddress
    sig?: string[]
    deadline?: number
    eoaKey?: number
    hash?: string
  }
>

type BaseAccountType = {
  account: any
}

type BaseRoleType = BaseAccountType & {
  role: string
}

type BaseOptionsType = {
  options?: CommonOptions
}

type BaseSenderType = {
  from: any
}

type WithAccountsType = {
  accounts: SignerWithAddress[]
}

export type FuncParamsType = {
  version: AccessCtrlType
  calcRole: AccessCtrlType & { prefix: string; id: string }
  checkAdminRole: AccessCtrlType &
    BaseAccountType & {
      options?: Partial<EventParamOptionType['AccessCtrl']['CheckAdminRoleOption'] & ContractCallOption>
    }
  checkRole: AccessCtrlType &
    BaseRoleType & {
      options?: Partial<EventParamOptionType['AccessCtrl']['CheckRoleOption'] & ContractCallOption>
    }
  hasRole: AccessCtrlType & BaseRoleType
  addAdminRole: AccessCtrlType & BaseSenderType & BaseAccountType & BaseOptionsType
  addRole: AccessCtrlType & BaseSenderType & BaseAccountType & { role: string } & BaseOptionsType
  addRoleByProv: AccessCtrlType & BaseRoleType & WithAccountsType & { providerId: string } & BaseOptionsType
  addRoleByIssuer: AccessCtrlType & BaseRoleType & WithAccountsType & { issuerId: string } & BaseOptionsType
  addRoleByValidator: AccessCtrlType & BaseRoleType & WithAccountsType & { validatorId: string } & BaseOptionsType
  delAdminRole: AccessCtrlType & BaseAccountType & WithAccountsType & BaseOptionsType
  delProviderRole: AccessCtrlType & BaseAccountType & WithAccountsType & { providerId: string } & BaseOptionsType
  delIssuerRole: AccessCtrlType & BaseAccountType & WithAccountsType & { issuerId: string } & BaseOptionsType
  delValidatorRole: AccessCtrlType & BaseAccountType & WithAccountsType & { validatorId: string } & BaseOptionsType
}

export type FunctionType = {
  version: (args: FuncParamsType['version']) => Promise<string>
  calcRole: (args: FuncParamsType['calcRole']) => Promise<EventReturnType['AccessCtrl']['CalcRole']>
  checkAdminRole: (args: FuncParamsType['checkAdminRole']) => Promise<EventReturnType['AccessCtrl']['CheckAdminRole']>
  checkRole: (args: FuncParamsType['checkRole']) => Promise<EventReturnType['AccessCtrl']['CheckRole']>
  hasRole: (args: FuncParamsType['hasRole']) => Promise<EventReturnType['AccessCtrl']['HasRole']>
  addAdminRole: (args: FuncParamsType['addAdminRole']) => Promise<ContractTransactionResponse>
  addRole: (args: FuncParamsType['addRole']) => Promise<ContractTransactionResponse>
  addRoleByProv: (args: FuncParamsType['addRoleByProv']) => Promise<ContractTransactionResponse>
  addRoleByIssuer: (args: FuncParamsType['addRoleByIssuer']) => Promise<ContractTransactionResponse>
  addRoleByValidator: (args: FuncParamsType['addRoleByValidator']) => Promise<ContractTransactionResponse>
  delAdminRole: (args: FuncParamsType['delAdminRole']) => Promise<ContractTransactionResponse>
  delProviderRole: (args: FuncParamsType['delProviderRole']) => Promise<ContractTransactionResponse>
  delIssuerRole: (args: FuncParamsType['delIssuerRole']) => Promise<ContractTransactionResponse>
  delValidatorRole: (args: FuncParamsType['delValidatorRole']) => Promise<ContractTransactionResponse>
}
