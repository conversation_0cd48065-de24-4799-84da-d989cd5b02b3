import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { FuncParamsType } from '@test/AccessCtrl/helpers/types'
import { AccessCtrlInstance } from '@test/common/types'

// ライブラリとコントラクトのリンクを初回のみ実施するため、グローバル変数としてフラグを利用する
const flag = false

export const genRoleByPrefix = async ({
  accessCtrl,
  getRolePrefixFuncName,
  id,
}: {
  accessCtrl: AccessCtrlInstance
  getRolePrefixFuncName: keyof Pick<
    AccessCtrlInstance,
    'ROLE_PREFIX_PROV' | 'ROLE_PREFIX_ACCOUNT' | 'ROLE_PREFIX_ISSUER' | 'ROLE_PREFIX_VALIDATOR'
  >
  id: string
}) => {
  const role_prefix = await accessCtrl[getRolePrefixFuncName]()
  const params: FuncParamsType['calcRole'] = {
    accessCtrl: accessCtrl,
    prefix: role_prefix,
    id: id,
  }
  return await accessCtrlFuncs.calcRole(params)
}
