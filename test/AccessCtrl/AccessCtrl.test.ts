import '@nomicfoundation/hardhat-chai-matchers'

describe('AccessCtrl', () => {
  require('./tests/version.test')
  require('./tests/addAdminRole.test')
  require('./tests/addRole.test')
  require('./tests/addRoleByProv.test')
  require('./tests/addRoleByIssuer.test')
  require('./tests/addRoleByValidator.test')
  require('./tests/calcRole.test')
  require('./tests/checkAdminRole.test')
  require('./tests/checkRole.test')
  require('./tests/delAdminRole.test')
  require('./tests/delProviderRole.test')
  require('./tests/delIssuerRole.test')
  require('./tests/delValidatorRole.test')
})
