import privateKey from '@/privateKey'
import { BASE } from '@test/common/consts'
import { EventReturnType } from '@test/common/types'
import * as utils from '@test/common/utils'
import {
  BackupRenewableEnergyTokensType,
  CheckTransactionType,
  GetTokenCountType,
  GetTokenListType,
  GetTokenType,
  MintType,
  RenewableEnergyTokenType,
  RestoreRenewableEnergyTokensType,
  TransferType,
} from './types'

/**
 * renewableEnergyTokenのイベントを呼ぶ関数を持つobject
 */
export const renewableEnergyTokenFunc = {
  version: ({ renewableEnergyToken }: RenewableEnergyTokenType) => {
    return renewableEnergyToken.version()
  },
  getTokenList: async ({
    renewableEnergyToken,
    validatorId,
    accountId,
    offset,
    limit,
    sortOrder,
  }: GetTokenListType) => {
    return renewableEnergyToken.getTokenList(validatorId, accountId, offset, limit, sortOrder) as unknown as Promise<
      EventReturnType['RenewableEnergyToken']['GetTokenList']
    >
  },
  getToken: ({ renewableEnergyToken, options }: GetTokenType) => {
    return renewableEnergyToken.getToken(...options) as unknown as Promise<
      EventReturnType['RenewableEnergyToken']['GetToken']
    >
  },
  getTokenCount: ({ renewableEnergyToken, options }: GetTokenCountType) => {
    return renewableEnergyToken.getTokenCount(...options) as unknown as Promise<
      EventReturnType['RenewableEnergyToken']['GetTokenCount']
    >
  },
  checkTransaction: async ({
    renewableEnergyToken,
    sendAccountId,
    fromAccountId,
    toAccountId,
    miscValue1,
    miscValue2,
  }: CheckTransactionType) => {
    return renewableEnergyToken.checkTransaction(
      sendAccountId,
      fromAccountId,
      toAccountId,
      miscValue1,
      miscValue2,
    ) as unknown as Promise<EventReturnType['RenewableEnergyToken']['CheckTransaction']>
  },
  mint: async ({
    renewableEnergyToken,
    tokenId,
    metadataId,
    metadataHash,
    mintAccountId,
    ownerAccountId,
    isLocked,
  }: MintType) => {
    return renewableEnergyToken.mint(
      tokenId,
      metadataId,
      metadataHash,
      mintAccountId,
      ownerAccountId,
      isLocked,
      BASE.TRACE_ID,
    )
  },
  transfer: async ({ renewableEnergyToken, fromAccountId, toAccountId, tokenId }: TransferType) => {
    return renewableEnergyToken.transfer(fromAccountId, toAccountId, tokenId, BASE.TRACE_ID)
  },
  backupRenewableEnergyTokens: async ({
    renewableEnergyToken,
    offset,
    limit,
    options = {},
  }: BackupRenewableEnergyTokensType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_RETOKEN_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return renewableEnergyToken.backupRenewableEnergyTokens(offset, limit, _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['RenewableEnergyToken']['BackupRenewableEnergyTokens']
    >
  },
  restoreRenewableEnergyTokens: async ({
    renewableEnergyToken,
    params,
    options = {},
  }: RestoreRenewableEnergyTokensType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_RETOKEN_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return renewableEnergyToken.restoreRenewableEnergyTokens(params, _deadline, _sig[0])
  },
  backupTokenIdsByAccountIds: async ({
    renewableEnergyToken,
    offset,
    limit,
    options = {},
  }: BackupRenewableEnergyTokensType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_RETOKEN_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return renewableEnergyToken.backupTokenIdsByAccountIds(offset, limit, _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['RenewableEnergyToken']['BackupTokenIdsByAccountIds']
    >
  },
  restoreTokenIdsByAccountId: async ({
    renewableEnergyToken,
    params,
    options = {},
  }: RestoreRenewableEnergyTokensType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_RETOKEN_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return renewableEnergyToken.restoreTokenIdsByAccountId(params, _deadline, _sig[0])
  },
}
