import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlInstance,
  AccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  IssuerInstance,
  ProviderInstance,
  RenewableEnergyTokenInstance,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'

export type RenewableEnergyTokenContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  renewableEnergyToken: RenewableEnergyTokenInstance
  transferProxy: TransferProxyInstance
  contractManager: ContractManagerInstance
  customTransfer1: TransferableMock1Instance
  customTransfer2: TransferableMock2Instance
  customTransfer3: TransferableMock3Instance
  accessCtrl: AccessCtrlInstance
}

export type RenewableEnergyTokenType = { renewableEnergyToken: RenewableEnergyTokenInstance }

export type BaseGetListType = RenewableEnergyTokenType & {
  offset: number
  limit: number
}

export type BaseTransferType = RenewableEnergyTokenType & {
  fromAccountId: string
  toAccountId: string
}

export type GetTokenListType = BaseGetListType & {
  validatorId: string
  accountId: string
  sortOrder: string
}

export type GetTokenType = RenewableEnergyTokenType & {
  options: Parameters<RenewableEnergyTokenInstance['getToken']>
}

export type GetTokenCountType = RenewableEnergyTokenType & {
  options: Parameters<RenewableEnergyTokenInstance['getTokenCount']>
}

export type CheckTransactionType = BaseTransferType & {
  sendAccountId: string
  miscValue1: string
  miscValue2: string
}

export type MintType = RenewableEnergyTokenType & {
  tokenId: string
  metadataId: string
  metadataHash: string
  mintAccountId: string
  ownerAccountId: string
  isLocked: boolean
}

export type TransferType = BaseTransferType & {
  tokenId: string
}

export type BackupRenewableEnergyTokensType = BaseGetListType & {
  options?: Partial<ContractCallOption>
}

export type RestoreRenewableEnergyTokensType = RenewableEnergyTokenType & {
  params: any
  options?: Partial<ContractCallOption>
}
