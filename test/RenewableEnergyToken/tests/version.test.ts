import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { RenewableEnergyTokenInstance } from '@test/common/types'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let renewableEnergyToken: RenewableEnergyTokenInstance
  describe('正常系', () => {
    before(async () => {
      ;({ renewableEnergyToken } = await contractFixture<RenewableEnergyTokenContractType>())
    })

    it('versionが返されること', async () => {
      assert.equal(
        await renewableEnergyTokenFunc.version({ renewableEnergyToken: renewableEnergyToken }),
        BASE.APP.VERSION,
        'version',
      )
    })
  })
})
