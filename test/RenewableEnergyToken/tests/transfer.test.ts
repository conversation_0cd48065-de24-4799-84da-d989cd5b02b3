import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  IssuerInstance,
  ProviderInstance,
  RenewableEnergyTokenInstance,
  StructType,
  TokenStatus,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

describe('transfer', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let renewableEnergyToken: RenewableEnergyTokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, renewableEnergyToken } =
        await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('Tokenが一つMintされている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
      })

      it('Tokenが移転されること', async () => {
        const tx = await renewableEnergyTokenFunc.transfer({
          renewableEnergyToken: renewableEnergyToken,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
        })
        const expectParams = {
          validatorId: BASE.VALID.VALID0.ID,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(renewableEnergyToken, 'TransferRNToken')
          .withArgs(...Object.values(expectParams))
        const beforeResult = await renewableEnergyTokenFunc.getToken({
          renewableEnergyToken: renewableEnergyToken,
          options: [BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32],
        })
        const beforeExpected: StructType['RenewableEnergyTokenDataType'] = {
          tokenStatus: TokenStatus.Active,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          previousAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        }
        utils.assertEqualForEachField(beforeResult.renewableEnergyTokenData, beforeExpected)

        await renewableEnergyTokenFunc.transfer({
          renewableEnergyToken: renewableEnergyToken,
          fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
        })
        const afterResult = await renewableEnergyTokenFunc.getToken({
          renewableEnergyToken: renewableEnergyToken,
          options: [BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32],
        })
        const afterExpected: StructType['RenewableEnergyTokenDataType'] = {
          tokenStatus: TokenStatus.Active,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          previousAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          isLocked: false,
        }
        utils.assertEqualForEachField(afterResult.renewableEnergyTokenData, afterExpected)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, renewableEnergyToken } =
        await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('Tokenが一つMintされている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
      })

      it('移転先と移転元が同じである場合は移転できないこと', async () => {
        await expect(
          renewableEnergyTokenFunc.transfer({
            renewableEnergyToken: renewableEnergyToken,
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          }),
        ).to.be.revertedWith(ERR.RETOKEN.RETOKEN_FROM_TO_ARE_SAME)
      })

      it('移転元がNFTを所有していない場合は移転できないこと', async () => {
        await expect(
          renewableEnergyTokenFunc.transfer({
            renewableEnergyToken: renewableEnergyToken,
            fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          }),
        ).to.be.revertedWith(ERR.RETOKEN.RETOKEN_NOT_OWNER)
      })

      it('存在しないNFTを指定した場合は移転できないこと', async () => {
        await expect(
          renewableEnergyTokenFunc.transfer({
            renewableEnergyToken: renewableEnergyToken,
            fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            tokenId: BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_BYTES32,
          }),
        ).to.be.revertedWith(ERR.RETOKEN.RETOKEN_NOT_EXIST)
      })
    })
  })
})
