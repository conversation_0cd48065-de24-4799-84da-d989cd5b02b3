import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  IssuerInstance,
  ProviderInstance,
  RenewableEnergyTokenInstance,
  StructType,
  TokenStatus,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32, toHex } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'
// Chai global vars
declare let assert: Chai.Assert

describe('getTokenList()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let renewableEnergyToken: RenewableEnergyTokenInstance
  let accounts: SignerWithAddress[]

  const createParams = (num: number) =>
    [...Array(num).keys()].map((index) => {
      return {
        TOKEN_ID: toBytes32(String(index + 1)),
        METADATA_ID: toBytes32(`12${index}`),
        METADATA_HASH: toBytes32(`0x00${index}`),
      }
    })
  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, renewableEnergyToken } =
        await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('TokenがMintされていない状態', () => {
      it('空の配列が返されること', async () => {
        const offset = 0
        const limit = 10
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        const expected: StructType['RenewableEnergyTokenDataType'][] = []
        utils.assertEqualForEachField(result.renewableEnergyTokenList, expected)
      })
    })

    describe('getRenewableEnergyTokenが登録されている状態 /* DCPF-21262', () => {
      const params = createParams(20)
      const assertList = (
        result: PromiseType<ReturnType<typeof renewableEnergyTokenFunc.getTokenList>>,
        expected: typeof params,
      ) => {
        assert.strictEqual(result.renewableEnergyTokenList.length, expected.length, 'account count')
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.renewableEnergyTokenList[i], {
            tokenStatus: TokenStatus.Active,
            metadataId: v.METADATA_ID,
            metadataHash: v.METADATA_HASH,
            mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            previousAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            isLocked: false,
          })
        })
      }

      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        for (let i = 0; i < params.length; i++) {
          await renewableEnergyTokenFunc.mint({
            renewableEnergyToken: renewableEnergyToken,
            tokenId: params[i].TOKEN_ID,
            metadataId: params[i].METADATA_ID,
            metadataHash: params[i].METADATA_HASH,
            mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            isLocked: false,
          })
        }
      })

      it('offset0, limit10を指定した場合、1要素目から10件取得できること', async () => {
        const offset = 0
        const limit = 10
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        assertList(result, params.slice(0, 10))
      })

      it('複数のgetTokenList呼び出しが同時に行われた場合、それぞれ正しい結果を返すこと', async () => {
        const sortOrder = 'asc'

        const requests = [
          renewableEnergyTokenFunc.getTokenList({
            renewableEnergyToken: renewableEnergyToken,
            validatorId: BASE.VALID.VALID0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            offset: 0,
            limit: 3,
            sortOrder: sortOrder,
          }),
          renewableEnergyTokenFunc.getTokenList({
            renewableEnergyToken: renewableEnergyToken,
            validatorId: BASE.VALID.VALID0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            offset: 2,
            limit: 4,
            sortOrder: sortOrder,
          }),
          renewableEnergyTokenFunc.getTokenList({
            renewableEnergyToken: renewableEnergyToken,
            validatorId: BASE.VALID.VALID0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            offset: 5,
            limit: 5,
            sortOrder: sortOrder,
          }),
        ]
        const results = await Promise.all(requests)

        utils.assertEqualForEachField(results[0], { totalCount: params.length, err: '' })
        assertList(results[0], params.slice(0, 3))

        utils.assertEqualForEachField(results[1], { totalCount: params.length, err: '' })
        assertList(results[1], params.slice(2, 6))

        utils.assertEqualForEachField(results[2], { totalCount: params.length, err: '' })
        assertList(results[2], params.slice(5, 10))
      })

      it('offset1, limit10を指定した場合、2要素目から10件取得できること', async () => {
        const offset = 1
        const limit = 10
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length, err: '' })
        assertList(result, params.slice(1, 11))
      })

      it('offset2, limit2を指定した場合、3要素目から2件取得できること', async () => {
        const offset = 2
        const limit = 2
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length, err: '' })
        assertList(result, [params[2], params[3]])
      })

      it('最後の1件が取得できること', async () => {
        const offset = 19
        const limit = 1
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length, err: '' })
        assertList(result, [params[params.length - 1]])
      })

      it('limitが取得上限(99件)以下の場合、データが取得ができること', async () => {
        const offset = 0
        const limit = 99
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length, err: '' })
        assertList(result, params)
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: '' })
        assertList(result, [])
      })

      // it('offset0, limit20を指定し、"decs"を選択した場合、1ページ目1項目目から20件を降順で取得できること', async () => {
      //   const offset = 0;
      //   const limit = 20;
      //   const sortOrder = 'desc';
      //   const result = await renewableEnergyTokenFunc.getTokenList(
      //     renewableEnergyToken,
      //     BASE.VALID.VALID0.ID,
      //     BASE.ACCOUNT.ACCOUNT1.ID,
      //     offset,
      //     limit,
      //     sortOrder,
      //   );
      //   utils.assertEqualForEachField(result, { totalCount: params.length, err: '' });
      //   assertList(result, params.slice(0, 20).reverse());
      // });

      // it('offset0, limit10を指定し、"decs"を選択した場合、2ページ目1項目目から10件を降順で取得できること', async () => {
      //   const offset = 0;
      //   const limit = 10;
      //   const sortOrder = 'desc';
      //   const result = await renewableEnergyTokenFunc.getTokenList(
      //     renewableEnergyToken,
      //     BASE.VALID.VALID0.ID,
      //     BASE.ACCOUNT.ACCOUNT1.ID,
      //     offset,
      //     limit,
      //     sortOrder,
      //   );
      //   utils.assertEqualForEachField(result, { totalCount: params.length, err: '' });
      //   assertList(result, params.slice(10, 20).reverse());
      // });

      it('所有トークンのうち一つのtokenを別ユーザに移転している場合、トークンの総数が減少すること', async () => {
        await renewableEnergyTokenFunc.transfer({
          renewableEnergyToken: renewableEnergyToken,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
        })

        const offset = 0
        const limit = 20
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        utils.assertEqualForEachField(result, { totalCount: 19, err: '' })
      })

      it('複数のtokenを別ユーザーに移転した場合、移転したtokenが別ユーザの所有トークンリストとして取得できること', async () => {
        await renewableEnergyTokenFunc.transfer({
          renewableEnergyToken: renewableEnergyToken,
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          tokenId: BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_BYTES32,
        })

        const offset = 0
        const limit = 20
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        params.slice(0, 2).forEach((v, i) => {
          utils.assertEqualForEachField(result.renewableEnergyTokenList[i], {
            tokenStatus: TokenStatus.Active,
            metadataId: v.METADATA_ID,
            metadataHash: v.METADATA_HASH,
            mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            ownerAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            previousAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            isLocked: false,
          })
        })
      })
    })

    describe('RenewableEnergyTokenが追加で5つMintされている状態', () => {
      const params = [
        {
          TOKEN_ID: toHex(120),
          METADATA_ID: toBytes32(`12`),
          METADATA_HASH: toBytes32(`0x00`),
        },
        {
          TOKEN_ID: toHex(121),
          METADATA_ID: toBytes32(`12`),
          METADATA_HASH: toBytes32(`0x00`),
        },
        {
          TOKEN_ID: toHex(122),
          METADATA_ID: toBytes32(`12`),
          METADATA_HASH: toBytes32(`0x00`),
        },
        {
          TOKEN_ID: toHex(123),
          METADATA_ID: toBytes32(`12`),
          METADATA_HASH: toBytes32(`0x00`),
        },
        {
          TOKEN_ID: toHex(124),
          METADATA_ID: toBytes32(`12`),
          METADATA_HASH: toBytes32(`0x00`),
        },
      ]

      const assertList = (
        result: PromiseType<ReturnType<typeof renewableEnergyTokenFunc.getTokenList>>,
        expected: typeof params,
      ) => {
        assert.strictEqual(result.renewableEnergyTokenList.length, expected.length, 'account count')
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.renewableEnergyTokenList[i], {
            tokenStatus: TokenStatus.Active,
            metadataId: v.METADATA_ID,
            metadataHash: v.METADATA_HASH,
            mintAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
            ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            previousAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
            isLocked: false,
          })
        })
      }

      const assertListAfterTransfer = (
        result: PromiseType<ReturnType<typeof renewableEnergyTokenFunc.getTokenList>>,
        expected: typeof params,
      ) => {
        assert.strictEqual(result.renewableEnergyTokenList.length, expected.length, 'account count')
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.renewableEnergyTokenList[i], {
            tokenStatus: TokenStatus.Active,
            metadataId: v.METADATA_ID,
            metadataHash: v.METADATA_HASH,
            mintAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
            ownerAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            previousAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            isLocked: false,
          })
        })
      }

      before(async () => {
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT3.ID },
        })
        for (const token of params) {
          await renewableEnergyTokenFunc.mint({
            renewableEnergyToken: renewableEnergyToken,
            tokenId: token.TOKEN_ID,
            metadataId: token.METADATA_ID,
            metadataHash: token.METADATA_HASH,
            mintAccountId: BASE.ACCOUNT.ACCOUNT3.ID,
            ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            isLocked: false,
          })
        }
      })

      it('mintを行ったユーザーが自身がmintしたtokenの一覧を取得できること', async () => {
        const offset = 0
        const limit = 5
        const sortOrder = 'desc'

        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })

        assertList(result, params)
      })

      it('mintを行ったユーザーが自身がmintしたtokenの一覧を取得できること(offset:2, limit:3)', async () => {
        const offset = 2
        const limit = 3
        const sortOrder = 'asc'

        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })

        assertList(result, params.slice(2, 5))
      })

      it('mintを行ったユーザーが自身がmintしたtokenの一覧を取得できること(offset:2, limit:10)', async () => {
        const offset = 2
        const limit = 10
        const sortOrder = 'asc'

        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })

        assertList(result, params.slice(2, 5))
      })

      it('mintを行ったユーザーが自身がmintしたtokenの一覧を取得できること(offset:4, limit:1)', async () => {
        const offset = 4
        const limit = 1
        const sortOrder = 'desc'

        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })

        assertList(result, [params[params.length - 1]])
      })

      it('mintを行ったtokenがtransferされた場合でも、mintしたtokenの一覧を取得できること', async () => {
        for (const token of params) {
          await renewableEnergyTokenFunc.transfer({
            renewableEnergyToken: renewableEnergyToken,
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            tokenId: token.TOKEN_ID,
          })
        }

        const offset = 0
        const limit = 10
        const sortOrder = 'asc'

        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT3.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })

        assertListAfterTransfer(result, params)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, renewableEnergyToken } =
        await contractFixture<RenewableEnergyTokenContractType>())
    })
    describe('getRenewableEnergyTokenが登録されている状態 /* DCPF-21262', () => {
      const params = createParams(20)
      const assertList = (
        result: PromiseType<ReturnType<typeof renewableEnergyTokenFunc.getTokenList>>,
        expected: typeof params,
      ) => {
        assert.strictEqual(result.renewableEnergyTokenList.length, expected.length, 'account count')
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.renewableEnergyTokenList[i], {
            tokenStatus: TokenStatus.Active,
            metadataId: v.METADATA_ID,
            metadataHash: v.METADATA_HASH,
            mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            previousAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            isLocked: false,
          })
        })
      }

      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        for (const v of params) {
          await renewableEnergyTokenFunc.mint({
            renewableEnergyToken: renewableEnergyToken,
            tokenId: v.TOKEN_ID,
            metadataId: v.METADATA_ID,
            metadataHash: v.METADATA_HASH,
            mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            isLocked: false,
          })
        }
      })

      it('存在しないアカウントを指定した場合、エラーが返却されること', async () => {
        const offset = 0
        const limit = 10
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT9.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'Error')
        assert.equal(result.totalCount, 0, 'Total Count')
      })

      it('不正なアカウントIDを指定した場合、エラーが返却されること', async () => {
        const offset = 0
        const limit = 10
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.EMPTY.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_INVALID_VAL, 'Error')
        assert.equal(result.totalCount, 0, 'Total Count')
      })

      it('limitが取得上限(1000件)より大きい場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 1001
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.RETOKEN.RETOKEN_TOO_LARGE_LIMIT,
        })
      })

      it('offsetが登録されたtokenIdより大きい場合、エラーが返されること', async () => {
        const offset = 100
        const limit = 50
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        utils.assertEqualForEachField(result, {
          renewableEnergyTokenList: [],
          totalCount: 20,
          err: ERR.RETOKEN.RETOKEN_OFFSET_OUT_OF_INDEX,
        })
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        const offset = params.length + 1
        const limit = 20
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        utils.assertEqualForEachField(result, {
          err: ERR.RETOKEN.RETOKEN_OFFSET_OUT_OF_INDEX,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length })
      })

      it('offsetが登録されている件数と同じ場合、エラーが返されること', async () => {
        const offset = params.length
        const limit = 20
        const sortOrder = 'asc'
        const result = await renewableEnergyTokenFunc.getTokenList({
          renewableEnergyToken: renewableEnergyToken,
          validatorId: BASE.VALID.VALID0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          offset: offset,
          limit: limit,
          sortOrder: sortOrder,
        })
        utils.assertEqualForEachField(result, {
          err: ERR.RETOKEN.RETOKEN_OFFSET_OUT_OF_INDEX,
        })
        utils.assertEqualForEachField(result, { totalCount: params.length })
      })
    })
  })
})
