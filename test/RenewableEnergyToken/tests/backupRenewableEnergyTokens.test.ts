import '@nomicfoundation/hardhat-chai-matchers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { RenewableEnergyTokenInstance, TokenStatus } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32, toHex } from '@test/common/utils'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'
// Chai global vars
declare let assert: Chai.Assert

describe('backupRenewableEnergyTokens()', () => {
  let renewableEnergyToken: RenewableEnergyTokenInstance

  const createParams = (num: number) =>
    [...Array(num).keys()].map((index) => {
      return {
        TOKEN_ID: toHex(index + 1),
        METADATA_ID: toBytes32(`12${index}`),
        METADATA_HASH: toBytes32(`0x00${index}`),
      }
    })
  describe('正常系', () => {
    before(async () => {
      ;({ renewableEnergyToken } = await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('RenewableEnergyTokenが登録されていない状態', () => {
      it('空リストが取得できること', async () => {
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: 0,
          limit: 1000,
        })
        utils.assertEqualForEachField(result, {
          renewableEnergyTokenAll: [],
          totalCount: 0,
          err: '',
        })
      })
    })

    describe('RenewableEnergyTokenが登録されている状態', () => {
      const prams = createParams(20)
      const assertList = (
        result: PromiseType<ReturnType<typeof renewableEnergyTokenFunc.backupRenewableEnergyTokens>>,
        expected: typeof prams,
      ) => {
        assert.strictEqual(result.renewableEnergyTokenAll.length, expected.length, 'account count')
        for (let i; i < expected.length; i++) {
          utils.assertEqualForEachField(result.renewableEnergyTokenAll[i], {
            tokenId: expected[i].TOKEN_ID,
            renewableEnergyTokenData: {
              tokenStatus: TokenStatus.Active,
              metadataId: expected[i].METADATA_ID,
              metadataHash: expected[i].METADATA_HASH,
              mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
              ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
              previousAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
              isLocked: false,
            },
          })
        }
      }

      before(async () => {
        for (let i = 0; i < prams.length; i++) {
          await renewableEnergyTokenFunc.mint({
            renewableEnergyToken: renewableEnergyToken,
            tokenId: prams[i].TOKEN_ID,
            metadataId: prams[i].METADATA_ID,
            metadataHash: prams[i].METADATA_HASH,
            mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            isLocked: false,
          })
        }
      })

      it('offset0, limit10を指定した場合、1ページ目1項目目から10件取得できること', async () => {
        const offset = 0
        const limit = 10
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' })
        assertList(result, prams.slice(0, 10))
      })

      it('offset1, limit10を指定した場合、2ページ目1項目目から10件取得できること', async () => {
        const offset = 1
        const limit = 10
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' })
        assertList(result, prams.slice(10, 20))
      })

      it('offset1, limit2を指定した場合、2ページ目2項目目から2件取得できること', async () => {
        const offset = 2
        const limit = 2
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' })
        assertList(result, [prams[4], prams[5]])
      })

      it('最後の1件が取得できること', async () => {
        const offset = 19
        const limit = 1
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' })
        assertList(result, [prams[prams.length - 1]])
      })

      it('limitが取得上限(1000件)以下の場合、データが取得ができること', async () => {
        const offset = 0
        const limit = 1000
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: prams.length, err: '' })
        assertList(result, prams)
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: '' })
        assertList(result, [])
      })
    })
  })
  describe('準正常系', () => {
    before(async () => {
      ;({ renewableEnergyToken } = await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('RenewableEnergyTokenが登録されている状態', () => {
      const prams = createParams(20)
      const assertList = (
        result: PromiseType<ReturnType<typeof renewableEnergyTokenFunc.backupRenewableEnergyTokens>>,
        expected: typeof prams,
      ) => {
        assert.strictEqual(result.renewableEnergyTokenAll.length, expected.length, 'account count')
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.renewableEnergyTokenAll[i], {
            tokenId: v.TOKEN_ID,
            renewableEnergyTokenData: {
              tokenStatus: TokenStatus.Active,
              metadataId: v.METADATA_ID,
              metadataHash: v.METADATA_HASH,
              mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
              ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
              previousAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
              isLocked: false,
            },
          })
        })
      }

      before(async () => {
        for (let i = 0; i < prams.length; i++) {
          await renewableEnergyTokenFunc.mint({
            renewableEnergyToken: renewableEnergyToken,
            tokenId: prams[i].TOKEN_ID,
            metadataId: prams[i].METADATA_ID,
            metadataHash: prams[i].METADATA_HASH,
            mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
            ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            isLocked: false,
          })
        }
      })

      it('limitが取得上限(1000件)より大きい場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 1001
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.RETOKEN.RETOKEN_TOO_LARGE_LIMIT,
        })
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        const offset = prams.length + 1
        const limit = 20
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.RETOKEN.RETOKEN_OFFSET_OUT_OF_INDEX,
        })
      })

      it('Admin権限がない場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
          options: {
            eoaKey: 9,
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_ROLE })
      })

      it('署名無効の場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
          options: {
            sig: ['0x1234', ''],
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('署名期限切れの場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const now = await utils.getExceededDeadline()
        const result = await renewableEnergyTokenFunc.backupRenewableEnergyTokens({
          renewableEnergyToken: renewableEnergyToken,
          offset: offset,
          limit: limit,
          options: {
            deadline: now,
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })
    })
  })
})
