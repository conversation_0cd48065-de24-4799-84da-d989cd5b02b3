import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, RenewableEnergyTokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toHex } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'
// Chai global vars
declare let assert: Chai.Assert

describe('restoreTokenIdsByAccountId()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let renewableEnergyToken: RenewableEnergyTokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, renewableEnergyToken } =
        await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('初期状態', () => {
      const tokenIds = [...Array(20).keys()].map((index) => {
        return toHex(index + 1)
      })
      const prams = [
        {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          tokenIds: tokenIds,
        },
      ]

      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 20)
      let pramsAddAccounts
      before(async () => {
        pramsAddAccounts = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await utils.getDeadline()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({
          validator: validator,
          accounts: accounts,
          options: {
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })

        for (let i = 0; i < pramsAddAccounts.length; i++) {
          await validatorFuncs.addAccount({
            validator: validator,
            accounts: accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: pramsAddAccounts[i].accountId,
              accountName: pramsAddAccounts[i].accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer: issuer,
            accounts: accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: pramsAddAccounts[i].accountId,
              deadline: deadline + 60,
            },
          })
        }
      })

      it('TokenIdsByAccountId(20件)が登録できること', async () => {
        await renewableEnergyTokenFunc.restoreTokenIdsByAccountId({
          renewableEnergyToken: renewableEnergyToken,
          params: prams,
        })
        const result = await renewableEnergyTokenFunc.backupTokenIdsByAccountIds({
          renewableEnergyToken: renewableEnergyToken,
          offset: 0,
          limit: 20,
        })
        for (let i = 0; i < pramsAddAccounts.length; i++) {
          assert.equal(pramsAddAccounts[i].accountId, result.tokenIdsByAccountIdAll[i].accountId)
          for (let j = 0; j < result.tokenIdsByAccountIdAll[i].tokenIds.length; j++) {
            assert.equal(tokenIds[j], result.tokenIdsByAccountIdAll[i].tokenIds[j])
          }
        }
      })
    })
  })

  describe('準正常系', () => {
    const tokenIds = [...Array(20).keys()].map((index) => {
      return toHex(index + 1)
    })
    const prams = [
      {
        accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        tokenIds: tokenIds,
      },
    ]

    before(async () => {
      ;({ renewableEnergyToken } = await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('初期状態', () => {
      it('Admin権限がない場合、エラーがスローされること', async () => {
        await expect(
          renewableEnergyTokenFunc.restoreTokenIdsByAccountId({
            renewableEnergyToken: renewableEnergyToken,
            params: prams,
            options: {
              eoaKey: BASE.EOA.ISSUER1,
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          renewableEnergyTokenFunc.restoreTokenIdsByAccountId({
            renewableEnergyToken: renewableEnergyToken,
            params: prams,
            options: {
              sig: ['0x1234', ''],
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        await expect(
          renewableEnergyTokenFunc.restoreTokenIdsByAccountId({
            renewableEnergyToken: renewableEnergyToken,
            params: prams,
            options: { deadline: now },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const ivalidTokenPram = [
          {
            accountId: '123',
            tokenIds: [],
          },
        ]
        await expect(
          renewableEnergyTokenFunc.restoreTokenIdsByAccountId({
            renewableEnergyToken: renewableEnergyToken,
            params: ivalidTokenPram,
          }),
        ).to.be.throw
      })
    })
  })
})
