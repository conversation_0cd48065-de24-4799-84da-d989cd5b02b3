import '@nomicfoundation/hardhat-chai-matchers'

describe('RenewableEnergyToken', () => {
  require('./tests/version.test')
  require('./tests/mint.test')
  require('./tests/getToken.test')
  require('./tests/getTokenCount.test')
  require('./tests/getTokenList.test')
  require('./tests/transfer.test')
  require('./tests/customTransfer.test')
  require('./tests/checkTransaction.test')
  require('./tests/backupRenewableEnergyTokens.test')
  require('./tests/restoreRenewableEnergyTokens.test')
  require('./tests/backupTokenIdsByAccountIds.test')
  require('./tests/restoreTokenIdsByAccountId.test')
})
