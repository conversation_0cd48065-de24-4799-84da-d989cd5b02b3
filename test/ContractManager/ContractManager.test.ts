import '@nomicfoundation/hardhat-chai-matchers'

describe('ContractManager', () => {
  require('./tests/version.test')
  require('./tests/setContracts.test')
  require('./tests/setIbcApp.test')
  require('./tests/accessCtrl.test')
  require('./tests/provider.test')
  require('./tests/account.test')
  require('./tests/validator.test')
  require('./tests/issuer.test')
  require('./tests/token.test')
  require('./tests/transferProxy.test')
  require('./tests/balanceSyncBridge.test')
  require('./tests/ibcApp.test')
})
