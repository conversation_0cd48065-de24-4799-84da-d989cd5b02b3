import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance } from '@test/common/types'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('ibcApp()', () => {
  let contractManager: ContractManagerInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, contractManager } = await contractFixture<ContractManagerContractType>())
      await contractManagerFuncs.setIbcApp({
        contractManager: contractManager,
        accounts: accounts,
        ibcAddress: await accounts[5].getAddress(),
        ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
      })
    })

    it('ibcAppインスタンスのアドレスが取得できること', async () => {
      const result = await contractManagerFuncs.ibcApp({
        contractManager: contractManager,
        ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
      })

      assert.equal(result, await accounts[5].getAddress())
    })
  })
})
