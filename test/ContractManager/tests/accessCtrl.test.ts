import '@nomicfoundation/hardhat-chai-matchers'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance, ContractManagerInstance } from '@test/common/types'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('accessCtrl()', () => {
  let contractManager: ContractManagerInstance
  let accessCtrl: AccessCtrlInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accessCtrl, contractManager } = await contractFixture<ContractManagerContractType>())
    })

    it('accessCtrlインスタンスのアドレスが取得できること', async () => {
      const result = await contractManagerFuncs.accessCtrl({ contractManager: contractManager })

      assert.equal(result, await accessCtrl.getAddress())
    })
  })
})
