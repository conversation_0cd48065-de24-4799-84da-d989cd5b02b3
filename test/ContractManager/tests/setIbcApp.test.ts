import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { contractsInitialize } from '@test/ContractManager/helpers/contractsInitialize'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('setIbcApp()', () => {
  let contractManager: ContractManagerInstance
  let accounts: SignerWithAddress[]

  let ibcAddress
  let ibcAddressString
  describe('正常系', () => {
    before(async () => {
      ;({ accounts, contractManager } = await helpers.loadFixture(contractsInitialize))
      ibcAddress = await accounts[5]
      ibcAddressString = await ibcAddress.getAddress()
      ;({ contractManager } = await contractFixture<ContractManagerContractType>())
    })

    it('任意のアドレスでibcのコントラクトアドレスが設定されること', async () => {
      await contractManagerFuncs.setIbcApp({
        contractManager: contractManager,
        accounts: accounts,
        ibcAddress: ibcAddressString,
        ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
      })

      assert.equal(
        await contractManagerFuncs.ibcApp({
          contractManager: contractManager,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        }),
        ibcAddressString,
      )
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ contractManager } = await contractFixture<ContractManagerContractType>())
    })

    it('Admin権限ではない署名の場合、エラーがスローされること', async () => {
      await expect(
        contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
          options: {
            eoaKey: BASE.EOA.PROV1,
          },
        }),
      ).to.be.revertedWith(ERR.ACTRL.ACTRL_NOT_ADMIN_ROLE)
    })

    it('署名期限切れの場合、エラーがスローされること', async () => {
      const exceededDeadline = await utils.getExceededDeadline()

      await expect(
        contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
          options: {
            deadline: exceededDeadline,
          },
        }),
      ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
    })

    it('署名無効の場合、エラーがスローされること', async () => {
      await expect(
        contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
          options: {
            sig: ['0x1234', ''],
          },
        }),
      ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
    })
  })
})
