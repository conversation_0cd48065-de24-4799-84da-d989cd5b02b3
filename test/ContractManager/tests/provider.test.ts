import '@nomicfoundation/hardhat-chai-matchers'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, ProviderInstance } from '@test/common/types'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('provider()', () => {
  let contractManager: ContractManagerInstance
  let provider: ProviderInstance

  describe('正常系', () => {
    before(async () => {
      ;({ provider, contractManager } = await contractFixture<ContractManagerContractType>())
    })

    it('providerインスタンスのアドレスが取得できること', async () => {
      const result = await contractManager['provider()']() // DCPF-25734 Fix function overloading (https://github.com/ethers-io/ethers.js/issues/407)

      assert.equal(result, await provider.getAddress())
    })
  })
})
