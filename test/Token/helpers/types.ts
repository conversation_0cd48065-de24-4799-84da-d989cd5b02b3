import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlInstance,
  AccountInstance,
  ApproveOption,
  BurnCancelOption,
  BurnOption,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  CustomTransferOption,
  IBCTokenInstance,
  IssuerInstance,
  MintOption,
  ProviderInstance,
  SetTokenEnabledOption,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  TransferSingleOption,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { PromiseType } from 'utility-types'

export type TokenContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  ibcToken: IBCTokenInstance
  transferProxy: TransferProxyInstance
  contractManager: ContractManagerInstance
  customTransfer1: TransferableMock1Instance
  customTransfer2: TransferableMock2Instance
  customTransfer3: TransferableMock3Instance
  accessCtrl: AccessCtrlInstance
  businessZoneAccount: BusinessZoneAccountInstance
}

export type TokenType = { token: TokenInstance }

export type BaseTransferType = {
  token: TokenInstance
  accounts: SignerWithAddress[]
  amount: number
  miscValue1: string
  miscValue2: string
}

export type BaseAmountType = {
  token: TokenInstance
  accounts: SignerWithAddress[]
  amount: number
}

export type GetAllowanceType = TokenType & { prams: Parameters<TokenInstance['getAllowance']> }

export type GetAllowanceListType = TokenType & {
  prams: Parameters<TokenInstance['getAllowanceList']>
}

export type GetBalanceListType = TokenType & {
  prams: Parameters<TokenInstance['getBalanceList']>
}

export type HasTokenType = TokenType & { prams: Parameters<TokenInstance['hasToken']> }

export type GetTokenType = TokenType & { prams: Parameters<TokenInstance['getToken']> }

export type CheckApproveType = TokenType & {
  validatorId: string
  ownerId: string
  spenderId: string
  amount: number
  sigInfo: PromiseType<ReturnType<typeof utils.siginfoGenerator>>
  options?: Partial<
    {
      accountSignature: Parameters<TokenInstance['checkApprove']>[4]
    } & ContractCallOption
  >
}

export type SetTokenEnabledType = TokenType & {
  accounts: SignerWithAddress[]
  enabled: boolean
  options?: Partial<SetTokenEnabledOption & ContractCallOption>
}

export type ApproveType = BaseAmountType & {
  spenderId: string
  options?: Partial<ApproveOption & ContractCallOption>
}

export type MintType = BaseAmountType & {
  options?: Partial<MintOption & ContractCallOption>
}

export type BurnType = BaseAmountType & {
  options?: Partial<BurnOption & ContractCallOption>
}

export type BurnCancelType = BaseAmountType & {
  blockTimestamp: string | number
  options?: Partial<BurnCancelOption & ContractCallOption>
}

export type TransferSingleType = BaseTransferType & {
  options?: Partial<TransferSingleOption>
}

export type CustomTransferType = BaseTransferType & {
  options?: Partial<CustomTransferOption>
}

export type SetTokenAllType = TokenType & {
  prams: any
  options: Partial<ContractCallOption>
}
