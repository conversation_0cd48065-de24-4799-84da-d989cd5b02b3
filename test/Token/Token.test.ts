import '@nomicfoundation/hardhat-chai-matchers'

describe('Token', () => {
  require('./tests/version.test')
  require('./tests/initialize.test')
  require('./tests/addToken.test')
  require('./tests/addTotalSupply.test')
  require('./tests/approve.test')
  require('./tests/getAllowance.test')
  require('./tests/getAllowanceList.test')
  require('./tests/getBalanceList.test')
  require('./tests/mint.test')
  require('./tests/burn.test')
  require('./tests/burnCancel.test')
  require('./tests/customTransfer.test')
  require('./tests/transferSingle.test')
  require('./tests/setTokenEnabled.test')
  require('./tests/subTotalSupply.test')
})

// HACK: このファイルのみのカバレッジでもタイムアウトが起きてしまう。テスト対象をskipしやすいようにdescribeを分けてテストを定義している
describe('Token2', () => {
  require('./tests/hasToken.test')
  require('./tests/getToken.test')
  require('./tests/checkApprove.test')
  /**
   * コントラクトサイズオーバー対策のため一時的にコメントアウト
   * TODO: バックアップ・リストア関数の切り分け方式決定後に再度実装する
   */
  // describe('getTokenAll()', () => {
  //   describe('正常系', () => {
  //     const emptyToken = {
  //       tokenId: BASE.TOKEN.EMPTY,
  //       name: BASE.TOKEN.EMPTY,
  //       symbol: BASE.TOKEN.EMPTY,
  //       totalSupply: '0',
  //       enabled: false,
  //     };
  //
  //     before(async () => {
  //       ({ accounts, provider, token } = await contractFixture<TokenContractType>());
  //     });
  //
  //     describe('tokenが登録されていない状態', () => {
  //       it('取得したtoken情報が初期値であること', async () => {
  //         const result = await tokenFuncs.getTokenAll(token);
  //         utils.assertEqualForEachField(result['token'], { ...emptyToken });
  //         utils.assertEqualForEachField(result, { err: '' });
  //       });
  //     });
  //
  //     describe('token情報が登録されている状態', () => {
  //       before(async () => {
  //         await providerFuncs.addProvider({provider:provider, accounts:accounts});
  //         await providerFuncs.addProviderRole(provider, accounts);
  //         await providerFuncs.addToken(provider, accounts);
  //       });
  //
  //       it('token情報が取得できること', async () => {
  //         const result = await tokenFuncs.getTokenAll(token);
  //         utils.assertEqualForEachField(result['token'], {
  //           tokenId: BASE.TOKEN.TOKEN1.ID,
  //           name: BASE.TOKEN.TOKEN1.NAME,
  //           symbol: BASE.TOKEN.TOKEN1.SYMBOL,
  //           totalSupply: '0',
  //           enabled: true,
  //         });
  //         utils.assertEqualForEachField(result, { err: '' });
  //       });
  //
  //       it('署名期限切れの場合、取得したtoken情報が初期値であること', async () => {
  //         const exceededDeadline = await utils.getExceededDeadline();
  //         const result = await tokenFuncs.getTokenAll(token, { deadline: exceededDeadline });
  //         utils.assertEqualForEachField(result['token'], { ...emptyToken });
  //         utils.assertEqualForEachField(result, { err: ERR.ACTRL.ACTRL_SIG_TIMEOUT });
  //       });
  //
  //       it('Admin権限ではない署名の場合、取得したtoken情報が初期値であること', async () => {
  //         const result = await tokenFuncs.getTokenAll(token, { eoaKey: BASE.EOA.PROV1 });
  //         utils.assertEqualForEachField(result['token'], { ...emptyToken });
  //         utils.assertEqualForEachField(result, { err: ERR.ACTRL.ACTRL_BAD_ROLE });
  //       });
  //     });
  //   });
  // });

  /**
   * コントラクトサイズオーバー対策のため一時的にコメントアウト
   * TODO: バックアップ・リストア関数の切り分け方式決定後に再度実装する
   */
  // describe('setTokenAll()', () => {
  //   describe('正常系', () => {
  //     const params = {
  //       tokenId: BASE.TOKEN.TOKEN1.ID,
  //       name: BASE.TOKEN.TOKEN1.NAME,
  //       symbol: BASE.TOKEN.TOKEN1.SYMBOL,
  //       totalSupply: 3000,
  //       enabled: true,
  //     };
  //
  //     before(async () => {
  //       ({ accounts, provider, token } = await contractFixture<TokenContractType>());
  //     });
  //
  //     describe('tokenが登録されていない状態', () => {
  //       it('すべてのtokenが登録できること', async () => {
  //         await tokenFuncs.setTokenAll(token, params);
  //
  //         const getTokenAll = await tokenFuncs.getTokenAll(token);
  //         utils.assertEqualForEachField(getTokenAll['token'], {
  //           tokenId: BASE.TOKEN.TOKEN1.ID,
  //           name: BASE.TOKEN.TOKEN1.NAME,
  //           symbol: BASE.TOKEN.TOKEN1.SYMBOL,
  //           totalSupply: '3000',
  //           enabled: true,
  //         });
  //         utils.assertEqualForEachField(getTokenAll, { err: '' });
  //       });
  //     });
  //   });
  //
  //   describe('準正常系', () => {
  //     const params = {
  //       tokenId: BASE.TOKEN.TOKEN1.ID,
  //       name: BASE.TOKEN.TOKEN1.NAME,
  //       symbol: BASE.TOKEN.TOKEN1.SYMBOL,
  //       totalSupply: 3000,
  //       enabled: true,
  //     };
  //
  //     before(async () => {
  //       ({ accounts, provider, token } = await contractFixture<TokenContractType>());
  //     });
  //
  //     describe('tokenが登録されていない状態', () => {
  //       it('署名無効の場合、エラーがスローされること', async () => {
  //         await expect(tokenFuncs.setTokenAll(token, params, { sig: ['0x1234', ''] })).revertedWith(
  //           ERR.ACTRL.ACTRL_BAD_SIG,
  //         );
  //       });
  //
  //       it('署名期限切れの場合、エラーがスローされること', async () => {
  //         const exceededDeadline = await utils.getExceededDeadline();
  //         await expect(tokenFuncs.setTokenAll(token, params, { deadline: exceededDeadline })).revertedWith(
  //           ERR.ACTRL.ACTRL_SIG_TIMEOUT,
  //         );
  //       });
  //
  //       it('異常な値が入力された時にfails', async () => {
  //         await expect(tokenFuncs.setTokenAll(token, { tokenId: '123' })).reverted;
  //       });
  //     });
  //   });
  // });
})
