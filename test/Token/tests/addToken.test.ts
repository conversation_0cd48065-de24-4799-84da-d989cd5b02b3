import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AddTokenOption, ProviderInstance, TokenInstance } from '@test/common/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addToken', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let token: TokenInstance

  // addTokenのテストはprovider.addTokenから呼ばれるためproviderのテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, token } = await contractFixture<TokenContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がProviderではない場合、エラーがスローされること', async () => {
        await expect(
          token
            .connect(accounts[1])
            .addToken(BASE.TOKEN.TOKEN1.ID, BASE.TOKEN.TOKEN1.NAME, BASE.TOKEN.TOKEN1.SYMBOL, BASE.TRACE_ID),
        ).revertedWith(ERR.PROV.NOT_PROVIDER_CONTRACT)
      })
    })

    describe('tokenが登録されている状態', () => {
      const params: AddTokenOption = {
        tokenId: BASE.TOKEN.TOKEN1.ID,
        providerId: BASE.PROV.PROV0.ID,
        name: BASE.TOKEN.TOKEN1.NAME,
        symbol: BASE.TOKEN.TOKEN1.SYMBOL,
      }

      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: params })
      })

      it('should revert when tokenId is not valid', async () => {
        const newParams: AddTokenOption = {
          ...params,
          tokenId: '0x0000000000000000000000000000000000000000000000000000000000000000',
          name: BASE.TOKEN.TOKEN2.NAME,
          symbol: BASE.TOKEN.TOKEN2.SYMBOL,
        }

        await expect(
          providerFuncs.addToken({ provider: provider, accounts: accounts, options: newParams }),
        ).revertedWith(ERR.TOKEN.TOKEN_INVALID_VAL)
      })
    })
  })
})
