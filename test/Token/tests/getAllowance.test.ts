import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getAllowance()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance

  describe('正常系', () => {
    before(async () => {
      ;({ provider, issuer, validator, token, accounts } = await contractFixture<TokenContractType>())
    })

    describe('allowanceが設定されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.ACCOUNT.ACCOUNT2.ID]) {
          await validatorFuncs.addAccount({ validator: validator, accounts: accounts, options: { accountId } })
        }
        await tokenFuncs.approve({
          token: token,
          accounts: accounts,
          spenderId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: 200,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            ownerId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('approveで指定した額が返されること', async () => {
        const result = await tokenFuncs.getAllowance({
          token: token,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        utils.assertEqualForEachField(result, { allowance: 200 })
      })

      it('approveで設定していないaccountIdを指定した場合、0が返されること', async () => {
        const result = await tokenFuncs.getAllowance({
          token: token,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT2.ID],
        })

        utils.assertEqualForEachField(result, { allowance: 0 })
      })

      it('未登録accountIdをspenderに指定した場合、エラーがスローされること', async () => {
        const result = await tokenFuncs.getAllowance({
          token: token,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT3.ID],
        })

        assert.equal(result.err, ERR.ACCOUNT.SPENDER_NOT_EXIST, 'Error')
      })

      it('未登録accountIdをownerに指定した場合、エラーがスローされること', async () => {
        const result = await tokenFuncs.getAllowance({
          token: token,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT3.ID, BASE.ACCOUNT.ACCOUNT0.ID],
        })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'Error')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, token } = await contractFixture<TokenContractType>())
    })

    describe('validatorが登録されていない状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
      })

      it('validatorIdが未入力である場合、エラーがスローされること', async () => {
        const result = await tokenFuncs.getAllowance({
          token: token,
          prams: [BASE.VALID.EMPTY.ID, BASE.ACCOUNT.ACCOUNT3.ID, BASE.ACCOUNT.ACCOUNT0.ID],
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_INVALID_VAL, 'Error')
      })

      it('存在しないvalidatorを指定した場合、エラーがスローされること', async () => {
        const result = await tokenFuncs.getAllowance({
          token: token,
          prams: [BASE.VALID.VALID9.ID, BASE.ACCOUNT.ACCOUNT3.ID, BASE.ACCOUNT.ACCOUNT0.ID],
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_ID_NOT_EXIST, 'Error')
      })
    })
  })
})
