import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getBalanceList()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, contractManager, validator, token, ibcToken, businessZoneAccount } =
        await contractFixture<TokenContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, account, 二つのbusinessZoneAccountが登録されている状態', () => {
      let ibcAddress
      let ibcAddressString

      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await await providerFuncs.addBizZone({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID2,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount: businessZoneAccount,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator: validator,
          accounts: accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount: businessZoneAccount,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator: validator,
          accounts: accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount: businessZoneAccount,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator: validator,
          accounts: accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID2,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await ibcTokenFuncs.transferToEscrow({
          ibcToken: ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('BusinessZoneのアカウント残高の一覧が取得できること', async () => {
        const zoneIds = [BASE.ZONE_ID.ID1, BASE.ZONE_ID.ID2]
        const zoneNames = [BASE.ZONE_NAME.NAME1, BASE.ZONE_NAME.NAME2]
        const balances = [300, 0]
        const accountNames = [BASE.ACCOUNT.ACCOUNT1.NAME, BASE.ACCOUNT.ACCOUNT1.NAME]
        const accountStatus = [BASE.STATUS.ACTIVE, BASE.STATUS.ACTIVE]
        const totalBalance = 300

        const result = await tokenFuncs.getBalanceList({ token: token, prams: [BASE.ACCOUNT.ACCOUNT1.ID] })

        utils.assertEqualForEachField(result.zoneIds, zoneIds)
        utils.assertEqualForEachField(result.zoneNames, zoneNames)
        utils.assertEqualForEachField(result.balances, balances)
        utils.assertEqualForEachField(result.accountNames, accountNames)
        utils.assertEqualForEachField(result.accountStatus, accountStatus)
        assert.equal(result.totalBalance, totalBalance)
      })

      it('存在しないアカウントIDで実行した場合、エラーが返却されること', async () => {
        const result = await tokenFuncs.getBalanceList({ token: token, prams: [BASE.ACCOUNT.ACCOUNT20.ID] })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('空のアカウントIDで実行した場合、エラーが返却されること', async () => {
        const result = await tokenFuncs.getBalanceList({ token: token, prams: [BASE.ACCOUNT.EMPTY.ID] })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })
    })
  })

  describe('準正常系', () => {
    let ibcAddress: string
    before(async () => {
      ;({ accounts, provider, issuer, validator, token, ibcToken, contractManager, businessZoneAccount } =
        await contractFixture<TokenContractType>())
      ibcAddress = await accounts[0].getAddress()
    })

    describe('account is terminated', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddress,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddress,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await await providerFuncs.addBizZone({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID2,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount: businessZoneAccount,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator: validator,
          accounts: accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount: businessZoneAccount,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID2,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator: validator,
          accounts: accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID2,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await validatorFuncs.setBizZoneTerminated({
          validator: validator,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
      })

      it('should return data of accountId without any changed when account is terminated ', async () => {
        const result = await tokenFuncs.getBalanceList({ token: token, prams: [BASE.ACCOUNT.ACCOUNT1.ID] })
        utils.assertEqualForEachField(result.zoneIds, [])
      })
    })
  })
})
