import '@nomicfoundation/hardhat-chai-matchers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, TokenInstance } from '@test/common/types'
import { TokenContractType } from '@test/Token/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('initialize()', () => {
  let token: TokenInstance
  let contractManager: ContractManagerInstance

  describe('正常系', () => {
    before(async () => {
      ;({ token, contractManager } = await contractFixture<TokenContractType>())
    })

    it('should revert when initialized', async () => {
      await expect(token.initialize(await contractManager.getAddress())).to.be.revertedWith(
        ERR.INITIALIZER.ALREADY_INIT,
      )
    })
  })
})
