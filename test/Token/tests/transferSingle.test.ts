import { contractManagerFuncs } from '@/test/ContractManager/helpers/function'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { accountFuncs } from '@test/Account/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccountInstance,
  ContractManagerInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { transferProxyFuncs } from '@test/TransferProxy/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

const TERMINATED_ACCOUNT1 = utils.toBytes32('x490')

describe('transferSingle()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance
  let transferProxy: TransferProxyInstance
  let contractManager: ContractManagerInstance
  let customTransfer1: TransferableMock1Instance
  let customTransfer2: TransferableMock2Instance
  let customTransfer3: TransferableMock3Instance

  describe('正常系', () => {
    before(async () => {
      ;({
        accounts,
        provider,
        issuer,
        validator,
        account,
        token,
        transferProxy,

        customTransfer1,
        customTransfer2,
        customTransfer3,
      } = await contractFixture<TokenContractType>())
    })

    describe('account, カスタムコントラクトが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const account of [
          { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT2.ID, accountName: BASE.ACCOUNT.ACCOUNT2.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT3.ID, accountName: BASE.ACCOUNT.ACCOUNT3.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT10.ID, accountName: BASE.ACCOUNT.ACCOUNT10.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT11.ID, accountName: BASE.ACCOUNT.ACCOUNT11.NAME },
        ]) {
          await validatorFuncs.addAccount({
            validator: validator,
            accounts: accounts,
            options: {
              accountId: account.accountId,
              accountName: account.accountName,
            },
          })
        }
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await customTransfer1.getAddress(),
          position: 0,
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await customTransfer2.getAddress(),
          position: 1,
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await customTransfer3.getAddress(),
          position: 2,
        })
      })

      it('miscValueが空指定の場合、指定されたamountでTransferが実行されること', async () => {
        const fromAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }
        const toAccount = { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME }
        const sendAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }
        const amount = 100
        const miscValue1 = utils.toBytes32('')
        const miscValue2 = ''

        const tx = await tokenFuncs.transferSingle({
          token: token,
          accounts: accounts,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
          options: {
            sendAccountId: sendAccount.accountId,
            fromAccountId: fromAccount.accountId,
            toAccountId: toAccount.accountId,
          },
        })
        await expect(tx)
          .emit(token, 'Transfer')
          .withArgs(
            [
              ...Object.values({
                transferType: BASE.TOKEN.TRANSFER_TYPE.TRANSFER,
                zoneId: BigInt(BASE.ZONE_ID.ID0),
                fromValidatorId: BASE.VALID.VALID0.ID,
                toValidatorId: BASE.VALID.VALID0.ID,
                fromAccountBalance: BigInt(300 - 100),
                toAccountBalance: BigInt(0 + 100),
                businessZoneBalance: BigInt(0),
                bizZoneId: BigInt(0),
                sendAccountId: sendAccount.accountId,
                fromAccountId: fromAccount.accountId,
                fromAccountName: fromAccount.accountName,
                toAccountId: toAccount.accountId,
                toAccountName: toAccount.accountName,
                amount: BigInt(amount),
                miscValue1: miscValue1,
                miscValue2: miscValue2,
                memo: BASE.MEMO,
              }),
            ],
            BASE.TRACE_ID,
          )
        const fromAccountData = await accountFuncs.getAccount({ account: account, params: [fromAccount.accountId] })
        utils.assertEqualForEachField(fromAccountData.accountData, { balance: 300 - 100 })
        const toAccountData = await accountFuncs.getAccount({ account: account, params: [toAccount.accountId] })
        utils.assertEqualForEachField(toAccountData.accountData, { balance: 0 + 100 })
      })

      it('miscValue1にcustomTransferで設定している値を指定した場合、customTransferで指定されたamountでTransferが実行されること', async () => {
        const fromAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }
        const toAccount = { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME }
        const sendAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }
        const amount = 100
        const miscValue1 = utils.toBytes32('DeCurret1')
        const miscValue2 = utils.toBytes32('0')

        const tx = await tokenFuncs.transferSingle({
          token: token,
          accounts: accounts,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
          options: {
            sendAccountId: sendAccount.accountId,
            fromAccountId: fromAccount.accountId,
            toAccountId: toAccount.accountId,
          },
        })
        // 100送金しようとするがカスタムコントラクトにより10しか送金されない事を確認する。
        await expect(tx)
          .emit(token, 'Transfer')
          .withArgs(
            [
              ...Object.values({
                transferType: BASE.TOKEN.TRANSFER_TYPE.CUSTOM_TRANSFER,
                zoneId: BigInt(BASE.ZONE_ID.ID0),
                fromValidatorId: BASE.VALID.VALID0.ID,
                toValidatorId: BASE.VALID.VALID0.ID,
                fromAccountBalance: BigInt(200 - 10),
                toAccountBalance: BigInt(100 + 10),
                businessZoneBalance: BigInt(0),
                bizZoneId: BigInt(0),
                sendAccountId: sendAccount.accountId,
                fromAccountId: fromAccount.accountId,
                fromAccountName: fromAccount.accountName,
                toAccountId: toAccount.accountId,
                toAccountName: toAccount.accountName,
                amount: BigInt(10),
                miscValue1: miscValue1,
                miscValue2: miscValue2,
                memo: BASE.MEMO,
              }),
            ],
            BASE.TRACE_ID,
          )
        const fromAccountData = await accountFuncs.getAccount({ account: account, params: [fromAccount.accountId] })
        utils.assertEqualForEachField(fromAccountData.accountData, { balance: 200 - 10 })
        const toAccountData = await accountFuncs.getAccount({ account: account, params: [toAccount.accountId] })
        utils.assertEqualForEachField(toAccountData.accountData, { balance: 100 + 10 })
      })

      it('miscValue2に値が入力された場合、指定されたamountでTransferが実行されること', async () => {
        const fromAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }
        const toAccount = { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME }
        const sendAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }
        const amount = 100
        // DCPF-24253: utils.toBytes32('0') return 0x3000000000000000000000000000000000000000000000000000000000000000 not 0x00
        // Use constant value instead of utils.toBytes32('0')
        const miscValue1 = '0x0000000000000000000000000000000000000000000000000000000000000000'
        const miscValue2 = utils.toBytes32('DeCurret1')

        const tx = await tokenFuncs.transferSingle({
          token: token,
          accounts: accounts,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
          options: {
            sendAccountId: sendAccount.accountId,
            fromAccountId: fromAccount.accountId,
            toAccountId: toAccount.accountId,
          },
        })

        await expect(tx)
          .emit(token, 'Transfer')
          .withArgs(
            [
              ...Object.values({
                transferType: BASE.TOKEN.TRANSFER_TYPE.CUSTOM_TRANSFER,
                zoneId: BigInt(BASE.ZONE_ID.ID0),
                fromValidatorId: BASE.VALID.VALID0.ID,
                toValidatorId: BASE.VALID.VALID0.ID,
                fromAccountBalance: BigInt(190 - 100),
                toAccountBalance: BigInt(110 + 100),
                businessZoneBalance: BigInt(0),
                bizZoneId: BigInt(0),
                sendAccountId: sendAccount.accountId,
                fromAccountId: fromAccount.accountId,
                fromAccountName: fromAccount.accountName,
                toAccountId: toAccount.accountId,
                toAccountName: toAccount.accountName,
                amount: BigInt(amount),
                miscValue1: miscValue1,
                miscValue2: miscValue2,
                memo: BASE.MEMO,
              }),
            ],
            BASE.TRACE_ID,
          )
        const fromAccountData = await accountFuncs.getAccount({ account: account, params: [fromAccount.accountId] })
        utils.assertEqualForEachField(fromAccountData.accountData, { balance: 190 - 100 })
        const toAccountData = await accountFuncs.getAccount({ account: account, params: [toAccount.accountId] })
        utils.assertEqualForEachField(toAccountData.accountData, { balance: 110 + 100 })
      })
    })

    describe('allowanceが設定されている状態', () => {
      before(async () => {
        await tokenFuncs.approve({
          token: token,
          accounts: accounts,
          spenderId: BASE.ACCOUNT.ACCOUNT2.ID,
          amount: 100,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            ownerId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('(senderとfromが異なるaccountを指定した場合)allowanceが設定されている場合、指定されたamountでTransferが実行されること', async () => {
        const fromAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }
        const toAccount = { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME }
        const sendAccount = { accountId: BASE.ACCOUNT.ACCOUNT2.ID, accountName: BASE.ACCOUNT.ACCOUNT2.NAME } //SenderAccount != FromAccount
        const amount = 90
        const miscValue1 = utils.toBytes32('0')
        const miscValue2 = utils.toBytes32('0')

        const tx = await tokenFuncs.transferSingle({
          token: token,
          accounts: accounts,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
          options: {
            sendAccountId: sendAccount.accountId,
            fromAccountId: fromAccount.accountId,
            toAccountId: toAccount.accountId,
          },
        })
        await expect(tx)
          .emit(token, 'Transfer')
          .withArgs(
            [
              ...Object.values({
                transferType: BASE.TOKEN.TRANSFER_TYPE.CUSTOM_TRANSFER,
                zoneId: BASE.ZONE_ID.ID0,
                fromValidatorId: BASE.VALID.VALID0.ID,
                toValidatorId: BASE.VALID.VALID0.ID,
                fromAccountBalance: BigInt(90 - 90),
                toAccountBalance: BigInt(210 + 90),
                businessZoneBalance: BigInt(0),
                bizZoneId: BigInt(0),
                sendAccountId: sendAccount.accountId,
                fromAccountId: fromAccount.accountId,
                fromAccountName: fromAccount.accountName,
                toAccountId: toAccount.accountId,
                toAccountName: toAccount.accountName,
                amount: BigInt(amount),
                miscValue1: miscValue1,
                miscValue2: miscValue2,
                memo: BASE.MEMO,
              }),
            ],
            BASE.TRACE_ID,
          )
        const fromAccountData = await accountFuncs.getAccount({ account: account, params: [fromAccount.accountId] })
        utils.assertEqualForEachField(fromAccountData.accountData, { balance: 90 - 90 })
        const toAccountData = await accountFuncs.getAccount({ account: account, params: [toAccount.accountId] })
        utils.assertEqualForEachField(toAccountData.accountData, { balance: 210 + 90 })
      })
    })

    describe('allowanceに無制限送金を許可する値が設定されている状態', () => {
      const maxAllowanceValue = ***************

      before(async () => {
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })

        await tokenFuncs.approve({
          token: token,
          accounts: accounts,
          spenderId: BASE.ACCOUNT.ACCOUNT2.ID,
          amount: maxAllowanceValue,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            ownerId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('(senderとfromが異なるaccountを指定した場合)allowanceにallowanceに無制限送金を許可する値が設定されている場合、Transferを実行してもAllowance量が変化しないこと', async () => {
        const fromAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }
        const toAccount = { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME }
        const sendAccount = { accountId: BASE.ACCOUNT.ACCOUNT2.ID, accountName: BASE.ACCOUNT.ACCOUNT2.NAME } //SenderAccount != FromAccount
        const amount = 90
        const miscValue1 = utils.toBytes32('0')
        const miscValue2 = utils.toBytes32('0')

        const tx = await tokenFuncs.transferSingle({
          token: token,
          accounts: accounts,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
          options: {
            sendAccountId: sendAccount.accountId,
            fromAccountId: fromAccount.accountId,
            toAccountId: toAccount.accountId,
          },
        })

        await expect(tx)
          .emit(token, 'Transfer')
          .withArgs(
            [
              ...Object.values({
                transferType: BASE.TOKEN.TRANSFER_TYPE.CUSTOM_TRANSFER,
                zoneId: BigInt(BASE.ZONE_ID.ID0),
                fromValidatorId: BASE.VALID.VALID0.ID,
                toValidatorId: BASE.VALID.VALID0.ID,
                fromAccountBalance: BigInt(300 - 90),
                toAccountBalance: BigInt(300 + 90),
                businessZoneBalance: BigInt(0),
                bizZoneId: BigInt(0),
                sendAccountId: sendAccount.accountId,
                fromAccountId: fromAccount.accountId,
                fromAccountName: fromAccount.accountName,
                toAccountId: toAccount.accountId,
                toAccountName: toAccount.accountName,
                amount: BigInt(amount),
                miscValue1: miscValue1,
                miscValue2: miscValue2,
                memo: BASE.MEMO,
              }),
            ],
            BASE.TRACE_ID,
          )
        const fromAccountData = await accountFuncs.getAccount({ account: account, params: [fromAccount.accountId] })
        utils.assertEqualForEachField(fromAccountData.accountData, { balance: 300 - 90 })
        const toAccountData = await accountFuncs.getAccount({ account: account, params: [toAccount.accountId] })
        utils.assertEqualForEachField(toAccountData.accountData, { balance: 300 + 90 })
        const resultAllowance = await tokenFuncs.getAllowance({
          token: token,
          prams: [BASE.VALID.VALID0.ID, fromAccount.accountId, sendAccount.accountId],
        })

        // Allowanceが当初の設定額から変化していないことを確認
        assert.equal(resultAllowance.allowance.toString(), maxAllowanceValue.toString(), 'allowance')
      })
    })

    describe('allowanceに無制限送金を許可する値より1小さい値が設定されている状態', () => {
      const allowanceValue = ***************

      before(async () => {
        await tokenFuncs.transferSingle({
          token: token,
          accounts: accounts,
          amount: 90,
          miscValue1: utils.toBytes32('0'),
          miscValue2: utils.toBytes32('0'),
          options: {
            sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })

        await tokenFuncs.approve({
          token: token,
          accounts: accounts,
          spenderId: BASE.ACCOUNT.ACCOUNT2.ID,
          amount: allowanceValue,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            ownerId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('(senderとfromが異なるaccountを指定した場合)allowanceにallowanceに無制限送金を許可する値が設定されている場合、Transferを実行した後に許可額が送金額分減額されること', async () => {
        const fromAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }
        const toAccount = { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME }
        const sendAccount = { accountId: BASE.ACCOUNT.ACCOUNT2.ID, accountName: BASE.ACCOUNT.ACCOUNT2.NAME } //SenderAccount != FromAccount
        const amount = 90
        const miscValue1 = utils.toBytes32('0')
        const miscValue2 = utils.toBytes32('0')

        const tx = await tokenFuncs.transferSingle({
          token: token,
          accounts: accounts,
          amount: amount,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
          options: {
            sendAccountId: sendAccount.accountId,
            fromAccountId: fromAccount.accountId,
            toAccountId: toAccount.accountId,
          },
        })

        await expect(tx)
          .emit(token, 'Transfer')
          .withArgs(
            [
              ...Object.values({
                transferType: BASE.TOKEN.TRANSFER_TYPE.CUSTOM_TRANSFER,
                zoneId: BigInt(BASE.ZONE_ID.ID0),
                fromValidatorId: BASE.VALID.VALID0.ID,
                toValidatorId: BASE.VALID.VALID0.ID,
                fromAccountBalance: BigInt(300 - 90),
                toAccountBalance: BigInt(300 + 90),
                businessZoneBalance: BigInt(0),
                bizZoneId: BigInt(0),
                sendAccountId: sendAccount.accountId,
                fromAccountId: fromAccount.accountId,
                fromAccountName: fromAccount.accountName,
                toAccountId: toAccount.accountId,
                toAccountName: toAccount.accountName,
                amount: BigInt(amount),
                miscValue1: miscValue1,
                miscValue2: miscValue2,
                memo: BASE.MEMO,
              }),
            ],
            BASE.TRACE_ID,
          )
        const fromAccountData = await accountFuncs.getAccount({ account: account, params: [fromAccount.accountId] })
        utils.assertEqualForEachField(fromAccountData.accountData, { balance: 300 - 90 })
        const toAccountData = await accountFuncs.getAccount({ account: account, params: [toAccount.accountId] })
        utils.assertEqualForEachField(toAccountData.accountData, { balance: 300 + 90 })
        const resultAllowance = await tokenFuncs.getAllowance({
          token: token,
          prams: [BASE.VALID.VALID0.ID, fromAccount.accountId, sendAccount.accountId],
        })

        // Allowanceの値が取引額分減額されていることを確認
        assert.equal(resultAllowance.allowance.toString(), (allowanceValue - amount).toString(), 'allowance')
      })
    })

    describe('一回の操作限度額を1000, 累積限度額を5000のアカウントを作成した場合', () => {
      const fromAccount = { accountId: BASE.ACCOUNT.ACCOUNT10.ID, accountName: BASE.ACCOUNT.ACCOUNT10.NAME }
      const toAccount = { accountId: BASE.ACCOUNT.ACCOUNT11.ID, accountName: BASE.ACCOUNT.ACCOUNT11.NAME }
      const sendAccount = { accountId: BASE.ACCOUNT.ACCOUNT10.ID, accountName: BASE.ACCOUNT.ACCOUNT10.NAME }
      const miscValue1 = utils.toBytes32('0')
      const miscValue2 = utils.toBytes32('0')

      before(async () => {
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 2000,
          options: { accountId: fromAccount.accountId },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 2000,
          options: { accountId: fromAccount.accountId },
        })
      })

      it('日跨りで一日の累積限度額を2回目の送金で超えている場合、balancが加算されること', async () => {
        const amount = 1000
        await tokenFuncs.transferSingle({
          token: token,
          accounts: accounts,
          amount: amount,
          miscValue1: utils.toBytes32('0'),
          miscValue2: utils.toBytes32('0'),
          options: {
            sendAccountId: sendAccount.accountId,
            fromAccountId: fromAccount.accountId,
            toAccountId: toAccount.accountId,
          },
        })
        // 1日（24時間）時間を進める
        await time.increase(24 * 60 * 60)

        const transferTx = await tokenFuncs.transferSingle({
          token: token,
          accounts: accounts,
          amount: amount,
          miscValue1: utils.toBytes32('0'),
          miscValue2: utils.toBytes32('0'),
          options: {
            sendAccountId: sendAccount.accountId,
            fromAccountId: fromAccount.accountId,
            toAccountId: toAccount.accountId,
          },
        })
        await expect(transferTx)
          .emit(token, 'Transfer')
          .withArgs(
            [
              ...Object.values({
                transferType: BASE.TOKEN.TRANSFER_TYPE.CUSTOM_TRANSFER,
                zoneId: BigInt(BASE.ZONE_ID.ID0),
                fromValidatorId: BASE.VALID.VALID0.ID,
                toValidatorId: BASE.VALID.VALID0.ID,
                fromAccountBalance: BigInt(2000 + 2000 - 1000 - 1000),
                toAccountBalance: BigInt(1000 + 1000),
                businessZoneBalance: BigInt(0),
                bizZoneId: BigInt(0),
                sendAccountId: sendAccount.accountId,
                fromAccountId: fromAccount.accountId,
                fromAccountName: fromAccount.accountName,
                toAccountId: toAccount.accountId,
                toAccountName: toAccount.accountName,
                amount: BigInt(amount),
                miscValue1: miscValue1,
                miscValue2: miscValue2,
                memo: BASE.MEMO,
              }),
            ],
            BASE.TRACE_ID,
          )
        const accountData = await accountFuncs.getAccount({ account: account, params: [toAccount.accountId] })
        utils.assertEqualForEachField(accountData.accountData, { balance: 2000 })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token } = await contractFixture<TokenContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const account of [
          { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT2.ID, accountName: BASE.ACCOUNT.ACCOUNT2.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT3.ID, accountName: BASE.ACCOUNT.ACCOUNT3.NAME },
          { accountId: TERMINATED_ACCOUNT1, accountName: BASE.ACCOUNT.ACCOUNT9.NAME },
        ]) {
          await validatorFuncs.addAccount({
            validator: validator,
            accounts: accounts,
            options: {
              accountId: account.accountId,
              accountName: account.accountName,
            },
          })
        }
        await validatorFuncs.setTerminated({
          validator: validator,
          accounts: accounts,
          options: { accountId: TERMINATED_ACCOUNT1 },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 100,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
      })

      // it('未登録accountIdをsenderに指定した場合、エラーがスローされること', async () => {
      //   await truffleAssert.reverts(
      //     tokenFuncs.transferSingle(token, accounts, 100, utils.toBytes32('0'), utils.toBytes32('0'), {
      //       sendAccountId: BASE.ACCOUNT.ACCOUNT15.ID,
      //       fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
      //       toAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
      //     }),
      //     ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
      //   );
      // });

      // it('未登録accountIdをtoに指定した場合、エラーがスローされること', async () => {
      //   await truffleAssert.reverts(
      //     tokenFuncs.transferSingle(token, accounts, 100, utils.toBytes32('0'), utils.toBytes32('0'), {
      //       sendAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
      //       fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
      //       toAccountId: BASE.ACCOUNT.ACCOUNT15.ID,
      //     }),
      //     ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST,
      //   );
      // });

      it('balanceが不足している場合、エラーがスローされること', async () => {
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const amount = 1000

        await expect(
          tokenFuncs.transferSingle({
            token: token,
            accounts: accounts,
            amount: amount,
            miscValue1: utils.toBytes32('0'),
            miscValue2: utils.toBytes32('0'),
            options: {
              sendAccountId,
              fromAccountId,
              toAccountId,
            },
          }),
        ).revertedWith(ERR.TOKEN.TOKEN_BALANCE_NOT_ENOUGH)
      })
    })

    describe('allowanceが設定されている状態', () => {
      before(async () => {
        await tokenFuncs.approve({
          token: token,
          accounts: accounts,
          spenderId: BASE.ACCOUNT.ACCOUNT2.ID,
          amount: 200,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            ownerId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('(senderとfromが異なるaccountを指定した場合)allowanceが不足している場合、エラーがスローされること', async () => {
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const sendAccountId = BASE.ACCOUNT.ACCOUNT2.ID //SenderAccount != FromAccount
        const amount = 201

        await expect(
          tokenFuncs.transferSingle({
            token: token,
            accounts: accounts,
            amount: amount,
            miscValue1: utils.toBytes32('0'),
            miscValue2: utils.toBytes32('0'),
            options: {
              sendAccountId,
              fromAccountId,
              toAccountId,
            },
          }),
        ).revertedWith(ERR.ACCOUNT.ALLOWANCE_NOT_ENOUGH)
      })

      it('(senderとfromが異なるaccountを指定した場合)fromで指定したaccountのbalanceが不足している場合、エラーがスローされること', async () => {
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const sendAccountId = BASE.ACCOUNT.ACCOUNT2.ID //SenderAccount != FromAccount
        const amount = 101

        await expect(
          tokenFuncs.transferSingle({
            token: token,
            accounts: accounts,
            amount: amount,
            miscValue1: utils.toBytes32('0'),
            miscValue2: utils.toBytes32('0'),
            options: {
              sendAccountId,
              fromAccountId,
              toAccountId,
            },
          }),
        ).revertedWith(ERR.TOKEN.TOKEN_BALANCE_NOT_ENOUGH)
      })

      it('AccountLib should revert when allowance is not enough from fake token call', async () => {
        // Hack: fake token call to check allowance
        // UNREACHABLE:  This test only for coverage, not for real case
        const fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const sendAccountId = BASE.ACCOUNT.ACCOUNT2.ID //SenderAccount != FromAccount
        const amount = 201

        await helpers.impersonateAccount(await token.getAddress())
        await helpers.setBalance(await token.getAddress(), 100n ** 18n)
        await expect(
          account
            .connect(await ethers.getSigner(await token.getAddress()))
            .calcAllowance(fromAccountId, sendAccountId, amount),
        ).revertedWith(ERR.ACCOUNT.ALLOWANCE_NOT_ENOUGH)
      })
    })
  })

  describe('Normal case', () => {
    before(async () => {
      ;({
        accounts,
        provider,
        issuer,
        validator,
        account,
        token,
        transferProxy,
        contractManager,
        customTransfer1,
        customTransfer2,
        customTransfer3,
      } = await contractFixture<TokenContractType>())
    })

    describe('zoneId is not _FINANCIAL_ZONE', () => {
      let ibcAddress: string
      const fromAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }
      const toAccount = { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME }
      const sendAccount = { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME }

      before(async () => {
        ibcAddress = await accounts[0].getAddress()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const account of [
          { accountId: BASE.ACCOUNT.ACCOUNT0.ID, accountName: BASE.ACCOUNT.ACCOUNT0.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT1.ID, accountName: BASE.ACCOUNT.ACCOUNT1.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT2.ID, accountName: BASE.ACCOUNT.ACCOUNT2.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT3.ID, accountName: BASE.ACCOUNT.ACCOUNT3.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT10.ID, accountName: BASE.ACCOUNT.ACCOUNT10.NAME },
          { accountId: BASE.ACCOUNT.ACCOUNT11.ID, accountName: BASE.ACCOUNT.ACCOUNT11.NAME },
        ]) {
          await validatorFuncs.addAccount({
            validator: validator,
            accounts: accounts,
            options: {
              accountId: account.accountId,
              accountName: account.accountName,
            },
          })
        }
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 1000,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await customTransfer1.getAddress(),
          position: 0,
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await customTransfer2.getAddress(),
          position: 1,
        })
        await transferProxyFuncs.addRule({
          transferProxy: transferProxy,
          rule: await customTransfer3.getAddress(),
          position: 2,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddress,
          ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
        })
      })

      it('should sync transfer and run normally', async () => {
        const amount = 100
        // This should revert with empty reason because there is no BalanceSyncBridge contract in this repo
        // This test mean transferSingle should reach to else case when zoneId != 3000
        await expect(
          tokenFuncs.transferSingle({
            token: token,
            accounts: accounts,
            amount: amount,
            miscValue1: utils.toBytes32('0'),
            miscValue2: utils.toBytes32('0'),
            options: {
              sendAccountId: sendAccount.accountId,
              fromAccountId: fromAccount.accountId,
              toAccountId: toAccount.accountId,
            },
          }),
        ).reverted
      })
    })
  })
})
