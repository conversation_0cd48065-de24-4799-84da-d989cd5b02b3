import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

describe('approve()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let token: TokenInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, token } = await contractFixture<TokenContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID, BASE.ACCOUNT.ACCOUNT2.ID]) {
          await validatorFuncs.addAccount({ validator: validator, accounts: accounts, options: { accountId } })
        }
      })

      it('allowanceが設定されること', async () => {
        const ownerId = BASE.ACCOUNT.ACCOUNT0.ID
        const spenderId = BASE.ACCOUNT.ACCOUNT1.ID
        const validatorId = BASE.VALID.VALID0.ID
        const amount = 100

        const tx = await tokenFuncs.approve({
          token: token,
          accounts: accounts,
          spenderId: spenderId,
          amount: amount,
          options: {
            validatorId: validatorId,
            ownerId: ownerId,
          },
        })
        await expect(tx)
          .emit(token, 'Approval')
          .withArgs(
            validatorId,
            ...Object.values({
              ownerId,
              spenderId,
              amount,
            }),
            BASE.TRACE_ID,
          )
        const result = await tokenFuncs.getAllowance({ token: token, prams: [validatorId, ownerId, spenderId] })
        utils.assertEqualForEachField(result, { allowance: amount })
      })
    })

    describe('accountにallowanceが設定されている状態', () => {
      it('異なる宛先に対しapproveを行なった場合、異なるallowanceが設定されること', async () => {
        const ownerId = BASE.ACCOUNT.ACCOUNT0.ID
        const spenderId1 = BASE.ACCOUNT.ACCOUNT1.ID
        const spenderId2 = BASE.ACCOUNT.ACCOUNT2.ID
        const validatorId = BASE.VALID.VALID0.ID
        const amount = 150

        const tx = await tokenFuncs.approve({
          token: token,
          accounts: accounts,
          spenderId: spenderId2,
          amount: amount,
          options: {
            validatorId: validatorId,
            ownerId: ownerId,
          },
        })

        await expect(tx)
          .emit(token, 'Approval')
          .withArgs(
            validatorId,
            ...Object.values({
              ownerId,
              spenderId: spenderId2,
              amount,
            }),
            BASE.TRACE_ID,
          )
        const toAccount1 = await tokenFuncs.getAllowance({ token: token, prams: [validatorId, ownerId, spenderId1] })
        utils.assertEqualForEachField(toAccount1, { allowance: 100 })
        const toAccount2 = await tokenFuncs.getAllowance({ token: token, prams: [validatorId, ownerId, spenderId2] })
        utils.assertEqualForEachField(toAccount2, { allowance: amount })
      })

      it('金額を変更しapproveを行なった場合、allowanceが更新されること', async () => {
        const ownerId = BASE.ACCOUNT.ACCOUNT0.ID
        const spenderId = BASE.ACCOUNT.ACCOUNT1.ID
        const validatorId = BASE.VALID.VALID0.ID
        const amount = 200

        const tx = await tokenFuncs.approve({
          token: token,
          accounts: accounts,
          spenderId: spenderId,
          amount: amount,
          options: {
            validatorId: validatorId,
            ownerId: ownerId,
          },
        })

        await expect(tx)
          .emit(token, 'Approval')
          .withArgs(
            validatorId,
            ...Object.values({
              ownerId,
              spenderId,
              amount,
            }),
            BASE.TRACE_ID,
          )
        const result = await tokenFuncs.getAllowance({ token: token, prams: [validatorId, ownerId, spenderId] })
        utils.assertEqualForEachField(result, { allowance: amount })
      })
    })
  })
})
