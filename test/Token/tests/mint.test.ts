import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { accountFuncs } from '@test/Account/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

// This MAX_UINT256 is used to test overflow handling
// Hex: 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
const MAX_UINT256 = 115792089237316195423570985008687907853269984665640564039457584007913129639935n

describe('mint()', () => {
  let accounts: SignerWithAddress[]
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token } = await contractFixture<TokenContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT1.ID]) {
          await validatorFuncs.addAccount({ validator: validator, accounts: accounts, options: { accountId } })
        }
      })

      it('指定された額がMintされること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const amount = 100
        const validatorId = BASE.VALID.VALID0.ID

        const mintTx = await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: amount,
          options: { accountId },
        })

        await expect(mintTx)
          .emit(token, 'Mint')
          .withArgs(BASE.ZONE_ID.ID0, validatorId, issuerId, accountId, accountName, amount, 100n, BASE.TRACE_ID)

        // TODO アカウントコントラクトのUT完了後に確認。
        // アカウントデータが配列で返却されている。
        // balanceがstringになっている。
        const accountData = await accountFuncs.getAccount({ account: account, params: [accountId] })
        utils.assertEqualForEachField(accountData.accountData, { balance: 100 })
        const totalSupply = await providerFuncs.getToken({ provider: provider, options: [BASE.PROV.PROV0.ID] })
        utils.assertEqualForEachField(totalSupply, { totalSupply: 100 })
      })
    })

    describe('mintがされている状態(balance(100), totalSupply(100))', () => {
      it('Mintされた額が加算されること、totalSupplyが加算されること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const amount = 100
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const validatorId = BASE.VALID.VALID0.ID

        const mintTx = await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: amount,
          options: { accountId },
        })

        await expect(mintTx)
          .emit(token, 'Mint')
          .withArgs(BASE.ZONE_ID.ID0, validatorId, issuerId, accountId, accountName, amount, 200n, BASE.TRACE_ID)

        const accountData = await accountFuncs.getAccount({ account: account, params: [accountId] })
        utils.assertEqualForEachField(accountData.accountData, { balance: 100 + 100 })
        const totalSupply = await providerFuncs.getToken({ provider: provider, options: [BASE.PROV.PROV0.ID] })
        utils.assertEqualForEachField(totalSupply, { totalSupply: 200 })
      })

      it('(amount=0のため)値が変動されないこと', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const amount = 0
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const validatorId = BASE.VALID.VALID0.ID

        const mintTx = await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: amount,
          options: { accountId },
        })

        await expect(mintTx)
          .emit(token, 'Mint')
          .withArgs(BASE.ZONE_ID.ID0, validatorId, issuerId, accountId, accountName, amount, 200n, BASE.TRACE_ID)
        const accountData = await accountFuncs.getAccount({ account: account, params: [accountId] })
        utils.assertEqualForEachField(accountData.accountData, { balance: 200 })
        const totalSupply = await providerFuncs.getToken({ provider: provider, options: [BASE.PROV.PROV0.ID] })
        utils.assertEqualForEachField(totalSupply, { totalSupply: 200 })
      })

      it('異なるaccountに対してMintした場合でも、totalSupplyが加算されること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT1.ID
        const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
        const issuerId = BASE.ISSUER.ISSUER0.ID
        const amount = 100
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const validatorId = BASE.VALID.VALID0.ID

        const mintTx = await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: amount,
          options: { accountId },
        })

        await expect(mintTx)
          .emit(token, 'Mint')
          .withArgs(BASE.ZONE_ID.ID0, validatorId, issuerId, accountId, accountName, amount, 100n, BASE.TRACE_ID)

        const accountData = await accountFuncs.getAccount({ account: account, params: [accountId] })
        utils.assertEqualForEachField(accountData.accountData, { balance: 100 })
        const totalSupply = await providerFuncs.getToken({ provider: provider, options: [BASE.PROV.PROV0.ID] })
        utils.assertEqualForEachField(totalSupply, { totalSupply: 200 + 100 })
      })
    })

    describe('一回の操作限度額を200, 累積限度額を300のアカウントを作成した場合', () => {
      const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
      const issuerId = BASE.ISSUER.ISSUER0.ID
      const accountId = BASE.ACCOUNT.ACCOUNT10.ID
      const limitAmounts = [200, 200, 200, 200, 300]
      const validatorId = BASE.VALID.VALID0.ID

      before(async () => {
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId, limitAmounts },
        })
      })

      it('日跨りで一日の累積限度額を2回目の発行で超えている場合、balancが加算されること', async () => {
        const amount = 200
        await tokenFuncs.mint({ token: token, accounts: accounts, amount: amount, options: { accountId } })
        // 1日（24時間）時間を進める
        await time.increase(24 * 60 * 60)
        const mintTx = await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: amount,
          options: { accountId },
        })

        await expect(mintTx)
          .emit(token, 'Mint')
          .withArgs(BASE.ZONE_ID.ID0, validatorId, issuerId, accountId, accountName, amount, 400n, BASE.TRACE_ID)

        const accountData = await accountFuncs.getAccount({ account: account, params: [accountId] })
        utils.assertEqualForEachField(accountData.accountData, { balance: 400 })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token } = await contractFixture<TokenContractType>())
    })

    describe('issuerRoleが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuer({
          issuer: issuer,
          accounts: accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          },
        })
        for (const _issuer of [BASE.ISSUER.ISSUER0, BASE.ISSUER.ISSUER1]) {
          await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts, options: { issuerId: _issuer.ID } })
        }
        await validatorFuncs.addValidator({
          validator: validator,
          accounts: accounts,
          options: {
            validatorId: BASE.VALID.VALID1.ID,
            issuerId: BASE.ISSUER.ISSUER1.ID,
          },
        })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
            validatorId: BASE.VALID.VALID1.ID,
          },
        })
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.EMPTY.ID
        const amount = 100

        await expect(
          tokenFuncs.mint({ token: token, accounts: accounts, amount: amount, options: { accountId } }),
        ).revertedWith(ERR.TOKEN.INVALID_ACCOUNT_ID)
      })

      it('空issuerIdを指定した場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const issuerId = BASE.ISSUER.EMPTY.ID
        const amount = 100

        await expect(
          tokenFuncs.mint({ token: token, accounts: accounts, amount: amount, options: { issuerId, accountId } }),
        ).revertedWith(ERR.TOKEN.INVALID_ORGANIZATION_ID)
      })
    })
    // provider.getZoneでエラーがあるケースは、事前準備ができないため未実施
  })

  describe('Not normal', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token } = await contractFixture<TokenContractType>())
    })

    describe('getZone return some error', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
      })

      it('should revert when token is overflow', async () => {
        // amount is never negative, use MAX_UINT256+1 to get overflow case
        // This revert will return "Arithmetic operation overflowed" instead of our message
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 100,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })

        const issuerId = BASE.ISSUER.ISSUER0.ID
        const accountId = BASE.ACCOUNT.ACCOUNT1.ID

        const _deadline = await utils.getDeadline()
        const signer = privateKey.key[BASE.EOA.ISSUER1]
        const _sig = privateKey.sig(
          signer,
          ['bytes32', 'bytes32', 'uint256', 'uint256'],
          [issuerId, accountId, MAX_UINT256, _deadline],
        )

        await expect(token.connect(accounts[0]).mint(issuerId, accountId, MAX_UINT256, BASE.TRACE_ID)).reverted
      })
      // TODO:後で修正
      // it('should revert when getZone return error provider not exist', async () => {
      //   const paramsSetProvider: SetProviderAllOption = {
      //     providerId: BASE.PROV.EMPTY.ID,
      //     role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
      //     name: BASE.PROV.PROV0.NAME,
      //     zoneId: BASE.ZONE_ID.ID0,
      //     zoneName: BASE.ZONE_NAME.NAME0,
      //     enabled: true,
      //     providerEoa: accounts[BASE.EOA.PROV1],
      //   };
      //   await providerFuncs.setProviderAll(provider, paramsSetProvider);

      //   const accountId = BASE.ACCOUNT.ACCOUNT0.ID;
      //   const amount = 100;

      //   await truffleAssert.reverts(tokenFuncs.mint(token, accounts, amount, { accountId }), ERR.PROV.PROV_NOT_EXIST);
      // });

      // it('should revert when getZone return error zoneId is 0', async () => {
      //   const paramsSetProvider: SetProviderAllOption = {
      //     providerId: BASE.PROV.PROV0.ID,
      //     role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
      //     name: BASE.PROV.PROV0.NAME,
      //     zoneId: BASE.ZONE_ID.EMPTY_ID,
      //     zoneName: BASE.ZONE_NAME.NAME0,
      //     enabled: true,
      //     providerEoa: accounts[BASE.EOA.PROV1],
      //   };
      //   await providerFuncs.setProviderAll(provider, paramsSetProvider);

      //   const accountId = BASE.ACCOUNT.ACCOUNT0.ID;
      //   const amount = 100;

      //   await truffleAssert.reverts(
      //     tokenFuncs.mint(token, accounts, amount, { accountId }),
      //     ERR.PROV.ZONEID_NOT_EXIST,
      //   );
      // });
    })
  })
})
