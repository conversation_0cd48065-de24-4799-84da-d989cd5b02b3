import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { TokenInstance } from '@test/common/types'
import { tokenFuncs } from '@test/Token/helpers/function'
import { TokenContractType } from '@test/Token/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let token: TokenInstance

  describe('正常系', () => {
    before(async () => {
      ;({ token } = await contractFixture<TokenContractType>())
    })

    it('versionが返されること', async () => {
      assert.equal(await tokenFuncs.version({ token: token }), BASE.APP.VERSION, 'version')
    })
  })
})
