import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { TransferProxyInstance } from '@test/common/types'
import { transferProxyFuncs } from '@test/TransferProxy/helpers/function'
import { TransferProxyContractType } from '@test/TransferProxy/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('isRegistered', () => {
  let accounts: SignerWithAddress[]
  let transferProxy: TransferProxyInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, transferProxy } = await contractFixture<TransferProxyContractType>())
    })

    describe('Ruleが2件登録されている状態', () => {
      before(async () => {
        for (let i = 0; i < 2; i++) {
          const rule = await accounts[i].getAddress()
          const position = i
          await transferProxyFuncs.addRule({ transferProxy: transferProxy, rule: rule, position: position })
        }
      })

      for (let i = 0; i < 2; i++) {
        it(`登録済の${i + 1}件目のAddressを確認する`, async function () {
          const rule = await accounts[i].getAddress()
          const result = await transferProxyFuncs.isRegistered({ transferProxy: transferProxy, rule: rule })
          assert.equal(result, true, 'isRegistered')
        })
      }
      it('未登録のAddressの場合はFalseを返す', async function () {
        const rule = await accounts[6].getAddress()
        const result = await transferProxyFuncs.isRegistered({ transferProxy: transferProxy, rule: rule })
        assert.equal(result, false, 'isRegistered')
      })
    })
  })
})
