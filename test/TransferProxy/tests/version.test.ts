import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { TransferProxyInstance } from '@test/common/types'
import { transferProxyFuncs } from '@test/TransferProxy/helpers/function'
import { TransferProxyContractType } from '@test/TransferProxy/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let transferProxy: TransferProxyInstance

  describe('正常系', () => {
    before(async () => {
      ;({ transferProxy } = await contractFixture<TransferProxyContractType>())
    })

    it('versionが取得できること', async () => {
      assert.equal(await transferProxyFuncs.version({ transferProxy: transferProxy }), BASE.APP.VERSION, 'version')
    })
  })
})
