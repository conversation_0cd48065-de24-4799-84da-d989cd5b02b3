import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'

export type TransferProxyContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  transferProxy: TransferProxyInstance
  contractManager: ContractManagerInstance
  customTransfer1: TransferableMock1Instance
  customTransfer2: TransferableMock2Instance
  customTransfer3: TransferableMock3Instance
  ibcToken: IBCTokenInstance
}

export type TransferProxyType = { transferProxy: TransferProxyInstance }

export type RuleType = TransferProxyType & { rule: string }

export type AddRuleType = RuleType & {
  position: number
}

export type CustomTransferType = TransferProxyType & {
  sendAccountId: string
  fromAccountId: string
  toAccountId: string
  amount: number
  miscValue1: string
  miscValue2: string
}
