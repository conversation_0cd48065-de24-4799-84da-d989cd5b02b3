import { BASE } from '@test/common/consts'
import { AddRuleType, CustomTransferType, RuleType, TransferProxyType } from './types'

/**
 * transferProxyのイベントを呼ぶ関数を持つobject
 */
export const transferProxyFuncs = {
  version: ({ transferProxy }: TransferProxyType) => {
    return transferProxy.version()
  },
  addRule: async ({ transferProxy, rule, position }: AddRuleType) => {
    return transferProxy.addRule(rule, position)
  },
  deleteRule: async ({ transferProxy, rule }: RuleType) => {
    return transferProxy.deleteRule(rule)
  },
  isRegistered: ({ transferProxy, rule }: RuleType) => {
    return transferProxy.isRegistered(rule)
  },
  customTransfer: async ({
    transferProxy,
    sendAccountId,
    fromAccountId,
    toAccountId,
    amount,
    miscValue1,
    miscValue2,
  }: CustomTransferType) => {
    return await transferProxy.customTransfer(
      sendAccountId,
      fromAccountId,
      toAccountId,
      amount,
      miscValue1,
      miscValue2,
      BASE.MEMO,
      BASE.TRACE_ID,
    )
  },
  findAll: ({ transferProxy }: TransferProxyType) => {
    return transferProxy.findAll()
  },
  clearRule: ({ transferProxy }: TransferProxyType) => {
    return transferProxy.clearRule()
  },
}
