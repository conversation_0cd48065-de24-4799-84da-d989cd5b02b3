import privateKey from '@/privateKey'
import { BASE } from '@test/common/consts'
import { EventReturnType } from '@test/common/types'
import * as utils from '@test/common/utils'
import Web3 from 'web3'

declare let web3: Web3

import {
  CheckAdminRoleType,
  IBCTokenType,
  InitAccountBalanceType,
  IssueVoucherType,
  RedeemVoucherType,
  SyncBusinessZoneBalanceType,
  TransferFromEscrowType,
  TransferToEscrowType,
} from './types'

/**
 * ibcTokenのイベントを呼ぶ関数を持つobject
 */
export const ibcTokenFuncs = {
  version: ({ ibcToken }: IBCTokenType) => {
    return ibcToken.version()
  },
  checkAdminRole: async ({ ibcToken, accounts, options = {} }: CheckAdminRoleType) => {
    const { deadline, sig, eoaKey = BASE.EOA.ADMIN, tokenId } = options
    const _tokenId = tokenId ?? '0x3000' + web3.utils.randomHex(16).slice(2)
    const dstZoneID = _tokenId.slice(0, 6)
    const userID = 300
    const _deadline = deadline ?? (await utils.getDeadline())
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(signer, ['bytes32', 'uint256', 'uint256', 'uint256'], [_tokenId, dstZoneID, userID, _deadline])

    return ibcToken.connect(accounts[0]).checkAdminRole(_sig[1], _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['IBCToken']['CheckAdminRole']
    >
  },
  transferFromEscrow: async ({ ibcToken, from, amount, options = {} }: TransferFromEscrowType) => {
    const {
      sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      toAccountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    return ibcToken
      .connect(from)
      .transferFromEscrow(BASE.ZONE_ID.ID1, sendAccountId, fromAccountId, toAccountId, amount, BASE.TRACE_ID)
  },
  transferToEscrow: async ({ ibcToken, from, amount, options = {} }: TransferToEscrowType) => {
    const {
      fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      toAccountId = BASE.ACCOUNT.ACCOUNT1.ID,
      zoneId = BASE.ZONE_ID.ID1,
    } = options
    return ibcToken.connect(from).transferToEscrow(zoneId, fromAccountId, toAccountId, amount, BASE.TRACE_ID)
  },
  syncBusinessZoneBalance: async ({ ibcToken, from, prams }: SyncBusinessZoneBalanceType) => {
    return ibcToken.connect(from).syncBusinessZoneBalance(prams)
  },
  initAccountBalance: async ({ ibcToken, from, accountId }: InitAccountBalanceType) => {
    return ibcToken.connect(from).initAccountBalance(accountId)
  },
  redeemVoucher: async ({ ibcToken, from, amount, options = {} }: RedeemVoucherType) => {
    const { accountId = BASE.ACCOUNT.ACCOUNT0.ID } = options
    return ibcToken.connect(from).redeemVoucher(accountId, amount, BASE.TRACE_ID)
  },
  issueVoucher: async ({ ibcToken, from, amount, options = {} }: IssueVoucherType) => {
    const { accountId = BASE.ACCOUNT.ACCOUNT0.ID } = options
    return ibcToken.connect(from).issueVoucher(accountId, amount, BASE.TRACE_ID)
  },
}
