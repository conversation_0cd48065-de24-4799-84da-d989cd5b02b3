import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlInstance,
  AccountInstance,
  BusinessZoneAccountInstance,
  CheckAdminRoleOption,
  ContractCallOption,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  IssueVoucherOption,
  ProviderInstance,
  RedeemVoucherOption,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferFromEscrowOption,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'

export type IBCTokenContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  ibcToken: IBCTokenInstance
  transferProxy: TransferProxyInstance
  contractManager: ContractManagerInstance
  customTransfer1: TransferableMock1Instance
  customTransfer2: TransferableMock2Instance
  customTransfer3: TransferableMock3Instance
  accessCtrl: AccessCtrlInstance
  businessZoneAccount: BusinessZoneAccountInstance
}

export type IBCTokenType = { ibcToken: IBCTokenInstance }

export type BaseFromType = IBCTokenType & {
  from: SignerWithAddress
}

export type BaseAmountType = BaseFromType & {
  amount: number
}

export type CheckAdminRoleType = IBCTokenType & {
  accounts: any
  options?: Partial<CheckAdminRoleOption & ContractCallOption>
}

export type TransferFromEscrowType = BaseAmountType & {
  options?: Partial<TransferFromEscrowOption>
}

export type TransferToEscrowType = BaseAmountType & {
  options: Partial<TransferFromEscrowOption>
}

export type SyncBusinessZoneBalanceType = BaseFromType & {
  prams: any
}

export type InitAccountBalanceType = BaseFromType & {
  accountId: string
}

export type RedeemVoucherType = BaseAmountType & {
  options?: Partial<RedeemVoucherOption>
}

export type IssueVoucherType = BaseAmountType & {
  options?: Partial<IssueVoucherOption>
}
