import { accountFuncs } from '@test/Account/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import {
  AccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IBCTokenContractType } from '@test/IBCToken/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

const TERMINATED_ACCOUNT1 = utils.toBytes32('x490')

describe('redeemVoucher()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let token: TokenInstance
  let ibcToken: IBCTokenInstance
  let contractManager: ContractManagerInstance
  let accounts: SignerWithAddress[]
  let ibcAddress
  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token, ibcToken, contractManager } =
        await contractFixture<IBCTokenContractType>())
      ibcAddress = await accounts[0]
    })

    describe('account, ibcAppAddressが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        // await tokenFuncs.mint(token, accounts, 300, { accountId: BASE.ACCOUNT.ACCOUNT0.ID });
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: await ibcAddress.getAddress(),
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await ibcTokenFuncs.issueVoucher({
          ibcToken: ibcToken,
          from: ibcAddress,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
      })

      it('balance, totalSupplyが減算されること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const accountName = BASE.ACCOUNT.ACCOUNT1.NAME
        const amount = 100

        const tx = await ibcTokenFuncs.redeemVoucher({
          ibcToken: ibcToken,
          from: ibcAddress,
          amount: amount,
          options: { accountId },
        })

        const expectParams = {
          zoneId: BASE.ZONE_ID.ID0,
          validatorId: BASE.VALID.VALID0.ID,
          accountId,
          accountName,
          amount,
          balance: 300 - 100,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(ibcToken, 'RedeemVoucher')
          .withArgs(...Object.values(expectParams))
        const fromAccountData = await accountFuncs.getAccount({ account: account, params: [accountId] })
        utils.assertEqualForEachField(fromAccountData.accountData, { balance: 300 - 100 })
        const totalSupply = await providerFuncs.getToken({ provider: provider, options: [BASE.PROV.PROV0.ID] })
        utils.assertEqualForEachField(totalSupply, { totalSupply: 300 - 100 })
      })
    })
  })

  describe('準正常系', () => {
    let ibcAddress

    before(async () => {
      ;({ accounts, provider, issuer, validator, account, token, ibcToken, contractManager } =
        await contractFixture<IBCTokenContractType>())
      ibcAddress = await accounts[0]
    })

    describe('account, ibcAppAddressが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const accountId of [BASE.ACCOUNT.ACCOUNT0.ID, TERMINATED_ACCOUNT1]) {
          await validatorFuncs.addAccount({ validator: validator, accounts: accounts, options: { accountId } })
        }
        await validatorFuncs.setTerminated({
          validator: validator,
          accounts: accounts,
          options: { accountId: TERMINATED_ACCOUNT1 },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 100,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: await ibcAddress.getAddress(),
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
      })

      it('呼び出し元が登録したibcAppAddressではない場合、エラーがスローされること', async () => {
        await expect(
          ibcTokenFuncs.redeemVoucher({
            ibcToken: ibcToken,
            from: accounts[1],
            amount: 100,
          }),
        ).to.be.revertedWith(ERR.IBC.NOT_IBC_CONTRACT)
      })

      it('balanceが不足している場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const amount = 1000

        await expect(
          ibcTokenFuncs.redeemVoucher({
            ibcToken: ibcToken,
            from: ibcAddress,
            amount: amount,
            options: { accountId },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.BALANCE_NOT_ENOUGH)
      })
    })
    // provider.getZoneでエラーがあるケースは、事前準備ができないため未実施
  })
})
