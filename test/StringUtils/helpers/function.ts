import { StringUtilsMockInstance } from '@test/common/types'

/**
 * StringUtilsの関数を間接的に呼び出すUtil関数
 * Libraryを直接呼び出しができない関係上、StringUtilsMockを経由して呼び出す
 */
export const stringUtilsFuncs = {
  stringToBytes32: async (stringUtilsMock: StringUtilsMockInstance, source: string) => {
    return await stringUtilsMock.stringToBytes32(source)
  },
  slice: async (stringUtilsMock: StringUtilsMockInstance, source: string, delimiter: string) => {
    return await stringUtilsMock.slice(source, delimiter)
  },
  substring: async (stringUtilsMock: StringUtilsMockInstance, str: string, startIndex: number, endIndex: number) => {
    return await stringUtilsMock.substring(str, startIndex, endIndex)
  },
}
