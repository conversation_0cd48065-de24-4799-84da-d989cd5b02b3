import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { StringUtilsMockInstance } from '@test/common/types'
import { stringUtilsFuncs } from '@test/StringUtils/helpers/function'
import { StringUtilsContractType } from '@test/StringUtils/helpers/types'
import { expect } from 'chai'

describe('substring()', () => {
  let stringUtilsMock: StringUtilsMockInstance

  before(async () => {
    ;({ stringUtilsMock } = await contractFixture<StringUtilsContractType>())
  })

  describe('正常系', () => {
    it('指定された範囲の文字列を取得すること', async () => {
      const str = BASE.STRING_UTILS.STRING_HELLO_WORLD
      const startIndex = BASE.STRING_UTILS.INDEX_START_HELLO
      const endIndex = BASE.STRING_UTILS.INDEX_END_WORLD
      const res = await stringUtilsFuncs.substring(stringUtilsMock, str, startIndex, endIndex)
      expect(res).to.equal('World')
    })

    it('startIndexが0の場合', async () => {
      const str = BASE.STRING_UTILS.STRING_SAMPLE
      const startIndex = BASE.STRING_UTILS.INDEX_START_ZERO
      const endIndex = BASE.STRING_UTILS.INDEX_END_TWO
      const res = await stringUtilsFuncs.substring(stringUtilsMock, str, startIndex, endIndex)
      expect(res).to.equal('de')
    })

    it('endIndexが文字列の長さと等しい場合', async () => {
      const str = BASE.STRING_UTILS.STRING_SAMPLE
      const startIndex = BASE.STRING_UTILS.INDEX_START_TWO
      const endIndex = BASE.STRING_UTILS.INDEX_END_FIVE
      const res = await stringUtilsFuncs.substring(stringUtilsMock, str, startIndex, endIndex)
      expect(res).to.equal('cur')
    })

    it('startIndexとendIndexが同じ場合、空文字列を返すこと', async () => {
      const str = BASE.STRING_UTILS.STRING_SAMPLE
      const startIndex = BASE.STRING_UTILS.INDEX_START_TWO
      const endIndex = BASE.STRING_UTILS.INDEX_START_TWO
      const res = await stringUtilsFuncs.substring(stringUtilsMock, str, startIndex, endIndex)
      expect(res).to.equal('')
    })

    it('マルチバイト文字列でのsubstring', async () => {
      const str = BASE.STRING_UTILS.STRING_MULTIBYTE
      const startIndex = BASE.STRING_UTILS.INDEX_START_FIFTEEN
      const endIndex = BASE.STRING_UTILS.INDEX_END_TWENTYONE
      const res = await stringUtilsFuncs.substring(stringUtilsMock, str, startIndex, endIndex)
      expect(res).to.equal('世界')
    })
  })

  describe('準正常系', () => {
    it('endIndexが文字列の長さを超える場合、リバートすること', async () => {
      const str = BASE.STRING_UTILS.STRING_SAMPLE
      const startIndex = BASE.STRING_UTILS.INDEX_START_ZERO
      const endIndex = 10
      await expect(stringUtilsFuncs.substring(stringUtilsMock, str, startIndex, endIndex)).to.be.reverted
    })

    it('startIndexが文字列の長さを超える場合、リバートすること', async () => {
      const str = BASE.STRING_UTILS.STRING_SAMPLE
      const startIndex = 10
      const endIndex = 12
      await expect(stringUtilsFuncs.substring(stringUtilsMock, str, startIndex, endIndex)).to.be.reverted
    })

    it('startIndexがendIndexより大きい場合、リバートすること', async () => {
      const str = BASE.STRING_UTILS.STRING_SAMPLE
      const startIndex = 3
      const endIndex = 2
      await expect(stringUtilsFuncs.substring(stringUtilsMock, str, startIndex, endIndex)).to.be.reverted
    })
  })
})
