import '@nomicfoundation/hardhat-chai-matchers'

describe('Issuer', () => {
  require('./tests/version.test')
  require('./tests/initialize.test')
  require('./tests/addIssuer.test')
  require('./tests/addAccountId.test')
  require('./tests/addIssuerRole.test')
  require('./tests/getAccountList.test')
  require('./tests/addAccountRole.test')
  require('./tests/hasAccount.test')
  require('./tests/modIssuer.test')
  require('./tests/hasIssuer.test')
  require('./tests/getIssuer.test')
  require('./tests/getIssuerCount.test')
  require('./tests/getIssuerId.test')
  require('./tests/getIssuerList.test')
  require('./tests/checkRole.test')
  require('./tests/checkMint.test')
  require('./tests/isFrozen.test')
  require('./tests/checkBurn.test')
  require('./tests/getAccount.test')
  require('./tests/modTokenLimit.test')
  require('./tests/cumulativeReset.test')
  require('./tests/setAccountStatus.test')
  require('./tests/forceBurn.test')

  // describe('getIssuersAll()', () => {
  //   describe('正常系', () => {
  //     before(async () => {
  //       [issuer, validator, provider, account] = await contractFixture<IssuerContractType>();
  //     });

  //     describe('issuerが登録されていない状態', () => {
  //       it('空リストが取得できること', async () => {
  //         const result = await issuerFuncs.getIssuersAll(issuer, 0, 1000);
  //         utils.assertEqualForEachField(result, {
  //           issuers: [],
  //           totalCount: 0,
  //           err: '',
  //         });
  //       });
  //     });

  //     describe('Issuersが登録されている状態', () => {
  //       const addIssuerAndAccountParams = [
  //         { validator: BASE.VALID.VALID0, issuer: BASE.ISSUER.ISSUER0, accounts: [], issuerEoa: accounts[0] },
  //         { validator: BASE.VALID.VALID1, issuer: BASE.ISSUER.ISSUER1, accounts: [], issuerEoa: accounts[1] },
  //         {
  //           validator: BASE.VALID.VALID2,
  //           issuer: BASE.ISSUER.ISSUER2,
  //           accounts: [BASE.ACCOUNT.ACCOUNT1, BASE.ACCOUNT.ACCOUNT2],
  //           issuerEoa: accounts[2],
  //         },
  //         { validator: BASE.VALID.VALID3, issuer: BASE.ISSUER.ISSUER3, accounts: [], issuerEoa: accounts[3] },
  //         { validator: BASE.VALID.VALID4, issuer: BASE.ISSUER.ISSUER4, accounts: [], issuerEoa: accounts[4] },
  //         { validator: BASE.VALID.VALID5, issuer: BASE.ISSUER.ISSUER5, accounts: [], issuerEoa: accounts[5] },
  //         { validator: BASE.VALID.VALID6, issuer: BASE.ISSUER.ISSUER6, accounts: [], issuerEoa: accounts[6] },
  //         { validator: BASE.VALID.VALID7, issuer: BASE.ISSUER.ISSUER7, accounts: [], issuerEoa: accounts[7] },
  //         { validator: BASE.VALID.VALID8, issuer: BASE.ISSUER.ISSUER8, accounts: [], issuerEoa: accounts[8] },
  //         { validator: BASE.VALID.VALID9, issuer: BASE.ISSUER.ISSUER9, accounts: [], issuerEoa: accounts[9] },
  //         { validator: BASE.VALID.VALID10, issuer: BASE.ISSUER.ISSUER10, accounts: [], issuerEoa: accounts[10] },
  //         { validator: BASE.VALID.VALID11, issuer: BASE.ISSUER.ISSUER11, accounts: [], issuerEoa: accounts[11] },
  //         { validator: BASE.VALID.VALID12, issuer: BASE.ISSUER.ISSUER12, accounts: [], issuerEoa: accounts[12] },
  //         { validator: BASE.VALID.VALID13, issuer: BASE.ISSUER.ISSUER13, accounts: [], issuerEoa: accounts[13] },
  //         { validator: BASE.VALID.VALID14, issuer: BASE.ISSUER.ISSUER14, accounts: [], issuerEoa: accounts[14] },
  //         { validator: BASE.VALID.VALID15, issuer: BASE.ISSUER.ISSUER15, accounts: [], issuerEoa: accounts[15] },
  //         { validator: BASE.VALID.VALID16, issuer: BASE.ISSUER.ISSUER16, accounts: [], issuerEoa: accounts[16] },
  //         { validator: BASE.VALID.VALID17, issuer: BASE.ISSUER.ISSUER17, accounts: [], issuerEoa: accounts[17] },
  //         { validator: BASE.VALID.VALID18, issuer: BASE.ISSUER.ISSUER18, accounts: [], issuerEoa: accounts[18] },
  //         { validator: BASE.VALID.VALID19, issuer: BASE.ISSUER.ISSUER19, accounts: [], issuerEoa: accounts[19] },
  //       ];

  //       const assertList = (
  //         result: PromiseType<ReturnType<typeof issuerFuncs.getIssuersAll>>,
  //         expected: typeof addIssuerAndAccountParams,
  //       ) => {
  //         assert.strictEqual(result.issuers.length, expected.length, 'issuers count');
  //         expected.forEach((v, i) => {
  //           assert.isString(result.issuers[i].role, 'role');
  //           utils.assertEqualForEachField(result.issuers[i], {
  //             issuerId: v.issuer.ID,
  //             bankCode: v.issuer.BANK_CODE.toString(),
  //             name: v.issuer.NAME,
  //             issuerIdExistence: true,
  //             issuerEoa: v.issuerEoa,
  //           });
  //           v.accounts.forEach((v2, i2) => {
  //             utils.assertEqualForEachField(result.issuers[i].issuerAccountExistence[i2], {
  //               accountId: v2.ID,
  //               accountIdExistenceByIssuerId: true,
  //             });
  //           });
  //         });
  //       };

  //       before(async () => {
  //         const deadline = await utils.getDeadline();
  //         await providerFuncs.addProvider({provider:provider, accounts:accounts});
  //         await providerFuncs.addProviderRole(provider, accounts);
  //         await Promise.all(
  //           addIssuerAndAccountParams.map(async (v) => {
  //             // 60件分のaddIssuerを連続実行することで "sig timeout" が発生するためdeadlineにさらに60秒加算している
  //             await issuerFuncs.addIssuer(issuer, accounts, {
  //               issuerId: v.issuer.ID,
  //               bankCode: v.issuer.BANK_CODE,
  //               name: v.issuer.NAME,
  //               deadline: deadline + 60,
  //             });
  //             await issuerFuncs.addIssuerRole(issuer, accounts, {
  //               issuerId: v.issuer.ID,
  //               issuerEoa: v.issuerEoa,
  //               deadline: deadline + 60,
  //             });
  //             await validatorFuncs.addValidator(validator, accounts, {
  //               validatorId: v.validator.ID,
  //               name: v.validator.NAME,
  //               issuerId: v.issuer.ID,
  //               deadline: deadline + 60,
  //             });
  //           }),
  //         );
  //         await providerFuncs.addToken(provider, accounts);

  //         await Promise.all(
  //           addIssuerAndAccountParams.flatMap((v) => {
  //             return v.accounts.map((account) => {
  //               validatorFuncs.addAccount(validator, accounts, { validatorId: v.validator.ID, accountId: account.ID });
  //             });
  //           }),
  //         );
  //       });

  //       it('offset0, limit10を指定した場合、1ページ目1項目目から10件取得できること', async () => {
  //         const offset = 0;
  //         const limit = 10;
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: addIssuerAndAccountParams.length, err: '' });
  //         assertList(result, addIssuerAndAccountParams.slice(0, 10));
  //       });

  //       it('offset1, limit10を指定した場合、2ページ目1項目目から10件取得できること', async () => {
  //         const offset = 1;
  //         const limit = 10;
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: addIssuerAndAccountParams.length, err: '' });
  //         assertList(result, addIssuerAndAccountParams.slice(10, 20));
  //       });

  //       it('offset1, limit2を指定した場合、2ページ目2項目目から2件取得できること', async () => {
  //         const offset = 2;
  //         const limit = 2;
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: addIssuerAndAccountParams.length, err: '' });
  //         assertList(result, [addIssuerAndAccountParams[4], addIssuerAndAccountParams[5]]);
  //       });

  //       it('最後の1件が取得できること', async () => {
  //         const offset = 19;
  //         const limit = 1;
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: addIssuerAndAccountParams.length, err: '' });
  //         assertList(result, [addIssuerAndAccountParams[addIssuerAndAccountParams.length - 1]]);
  //       });

  //       it('limitが取得上限(1000件)以下の場合、データが取得ができること', async () => {
  //         const offset = 0;
  //         const limit = 1000;
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: addIssuerAndAccountParams.length, err: '' });
  //         assertList(result, addIssuerAndAccountParams);
  //       });

  //       it('limitが0の場合、空リストが取得できること', async () => {
  //         const offset = 2;
  //         const limit = 0;
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit);
  //         utils.assertEqualForEachField(result, { totalCount: addIssuerAndAccountParams.length, err: '' });
  //         assertList(result, []);
  //       });

  //       it('limitが取得上限(1000件)より大きい場合、エラーが返されること', async () => {
  //         const offset = 0;
  //         const limit = 1001;
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit);
  //         utils.assertEqualForEachField(result, {
  //           totalCount: addIssuerAndAccountParams.length,
  //           err: ERR.ISSUER.ISSUER_TOO_LARGE_LIMIT,
  //         });
  //       });

  //       it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
  //         const offset = 20;
  //         const limit = 20;
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit);
  //         utils.assertEqualForEachField(result, {
  //           totalCount: addIssuerAndAccountParams.length,
  //           err: ERR.ISSUER.ISSUER_OFFSET_OUT_OF_INDEX,
  //         });
  //       });

  //       it('Admin権限がない場合、エラーが返されること', async () => {
  //         const offset = 0;
  //         const limit = 20;
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit, { eoaKey: 9 });
  //         utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_ROLE });
  //       });

  //       it('署名無効の場合、エラーが返されること', async () => {
  //         const offset = 0;
  //         const limit = 20;
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit, { sig: ['0x1234', ''] });
  //         utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_SIG });
  //       });

  //       it('署名期限切れの場合、エラーが返されること', async () => {
  //         const offset = 0;
  //         const limit = 20;
  //         const now = await utils.getExceededDeadline();
  //         const result = await issuerFuncs.getIssuersAll(issuer, offset, limit, { deadline: now });
  //         utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT });
  //       });
  //     });
  //   });
  // });

  // describe('setIssuersAll()', () => {
  //   const createParams = (accounts: any, num: number) =>
  //     [...Array(num).keys()].map((index) => {
  //       return {
  //         issuerId: toBytes32(`x${index}`),
  //         bankCode: index,
  //         role: toBytes32(`ROLE${index}`),
  //         name: toBytes32(`NAME${index}`),
  //         enabled: true,
  //         issuerIdExistence: true,
  //         issuerEoa: accounts[index],
  //         issuerAccountExistence: [
  //           {
  //             accountId: toBytes32(`ISSUER${index}_ACCOUNT1`),
  //             accountIdExistenceByIssuerId: true,
  //           },
  //           {
  //             accountId: toBytes32(`ISSUER${index}_ACCOUNT2`),
  //             accountIdExistenceByIssuerId: true,
  //           },
  //         ],
  //       };
  //     });
  //   describe('正常系', () => {
  //     before(async () => {
  //       [issuer] = await contractFixture<IssuerContractType>();
  //     });

  //     describe('初期状態', () => {
  //       const issuersPram = createParams(accounts, 20);
  //       const assertList = (
  //         result: PromiseType<ReturnType<typeof issuerFuncs.getIssuersAll>>,
  //         expected: typeof issuersPram,
  //       ) => {
  //         assert.strictEqual(result.issuers.length, expected.length, 'issuers count');
  //         expected.forEach((v, i) => {
  //           assert.isString(result.issuers[i].role, 'role');
  //           utils.assertEqualForEachField(result.issuers[i], {
  //             issuerId: v.issuerId,
  //             bankCode: v.bankCode.toString(),
  //             name: v.name,
  //             issuerIdExistence: v.issuerIdExistence,
  //             issuerEoa: v.issuerEoa,
  //           });
  //           v.issuerAccountExistence.forEach((v2, i2) => {
  //             utils.assertEqualForEachField(result.issuers[i].issuerAccountExistence[i2], {
  //               accountId: v2.accountId,
  //               accountIdExistenceByIssuerId: v2.accountIdExistenceByIssuerId,
  //             });
  //           });
  //         });
  //       };
  //       it('全てのissuers(20件)が登録できること', async () => {
  //         await issuerFuncs.setIssuersAll(issuer, issuersPram, accounts);
  //         const result = await issuerFuncs.getIssuersAll(issuer, 0, 1000);
  //         assertList(result, issuersPram);
  //       });

  //       // 「一括登録の上限数を超えた場合、エラーがスローされること」のテストは
  //       // gasの上限に達してエラーになるため未実施(Error: Returned error: base fee exceeds gas limit)
  //     });
  //   });

  //   describe('準正常系', () => {
  //     const issuersPram = createParams(accounts, 20);

  //     before(async () => {
  //       [issuer] = await contractFixture<IssuerContractType>();
  //     });

  //     describe('初期状態', () => {
  //       it('Admin権限がない場合、エラーがスローされること', async () => {
  //         await truffleAssert.reverts(
  //           issuerFuncs.setIssuersAll(issuer, issuersPram, accounts, { eoaKey: BASE.EOA.ISSUER1 }),
  //           ERR.ACTRL.ACTRL_BAD_ROLE,
  //         );
  //       });

  //       it('署名無効の場合、エラーがスローされること', async () => {
  //         await truffleAssert.reverts(
  //           issuerFuncs.setIssuersAll(issuer, issuersPram, accounts, { sig: ['0x1234', ''] }),
  //           ERR.ACTRL.ACTRL_BAD_SIG,
  //         );
  //       });

  //       it('署名期限切れの場合、エラーがスローされること', async () => {
  //         const now = await utils.getExceededDeadline();
  //         await truffleAssert.reverts(
  //           issuerFuncs.setIssuersAll(issuer, issuersPram, accounts, { deadline: now }),
  //           ERR.ACTRL.ACTRL_SIG_TIMEOUT,
  //         );
  //       });

  //       it('異常な値が入力された時にfails', async () => {
  //         const issuersIvalid = [
  //           {
  //             issuerId: '123',
  //             role: '456',
  //             name: 'name',
  //             accountIds: [],
  //             enabled: true,
  //             issuerEoa: '',
  //           },
  //         ];
  //         await truffleAssert.fails(issuerFuncs.setIssuersAll(issuer, issuersIvalid, accounts));
  //       });
  //     });
  //   });
  // });
})
