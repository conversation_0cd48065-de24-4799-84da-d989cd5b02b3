import privateKey from '@/privateKey'
import { BASE } from '@test/common/consts'
import { EventReturnType } from '@test/common/types'
import * as utils from '@test/common/utils'
import {
  AddAccountIdType,
  AddAccountRoleType,
  AddIssuerRoleType,
  AddIssuerType,
  CheckBurnType,
  CheckMintType,
  CheckRoleType,
  CumulativeResetType,
  ForceBurnType,
  GetAccountListType,
  GetAccountType,
  GetIssuerIdType,
  GetIssuerListType,
  GetIssuerType,
  HasAccountType,
  HasIssuerType,
  IssuerType,
  ModIssuerType,
  ModTokenLimitType,
  SetAccountStatusType,
} from './types'

/**
 * issuerのイベントを呼ぶ関数を持つobject
 */
export const issuerFuncs = {
  version: ({ issuer }: IssuerType) => {
    return issuer.version()
  },
  getIssuerList: ({ issuer, params }: GetIssuerListType) => {
    return issuer.getIssuerList(...params) as unknown as Promise<EventReturnType['Issuer']['GetIssuerList']>
  },
  getIssuer: ({ issuer, params }: GetIssuerType) => {
    return issuer.getIssuer(...params) as unknown as Promise<EventReturnType['Issuer']['GetIssuer']>
  },
  getIssuerId: ({ issuer, params }: GetIssuerIdType) => {
    return issuer.getIssuerId(...params) as unknown as Promise<EventReturnType['Issuer']['GetIssuerId']>
  },
  getIssuerCount: ({ issuer }: IssuerType) => {
    return issuer.getIssuerCount() as unknown as Promise<EventReturnType['Issuer']['GetIssuerCount']>
  },
  hasIssuer: ({ issuer, params }: HasIssuerType) => {
    return issuer.hasIssuer(...params) as unknown as Promise<EventReturnType['Issuer']['HasIssuer']>
  },
  checkRole: async ({ issuer, options = {} }: CheckRoleType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ISSUER1, issuerId = BASE.ISSUER.ISSUER0.ID, hash = 'morning' } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'string', 'uint256'], [issuerId, hash, _deadline])

    return issuer.checkRole(issuerId, _sig[1], _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Issuer']['CheckRole']
    >
  },
  checkMint: async ({ issuer, amount, options = {} }: CheckMintType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(signer, ['bytes32', 'bytes32', 'uint256', 'uint256'], [issuerId, accountId, amount, _deadline])
    return issuer.checkMint(issuerId, accountId, amount, _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Issuer']['CheckMint']
    >
  },
  checkBurn: async ({ issuer, amount, options = {} }: CheckBurnType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(signer, ['bytes32', 'bytes32', 'uint256', 'uint256'], [issuerId, accountId, amount, _deadline])
    return issuer.checkBurn(issuerId, accountId, amount, _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Issuer']['CheckBurn']
    >
  },
  addIssuer: async ({ issuer, accounts, options = {} }: AddIssuerType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      bankCode = BASE.ISSUER.ISSUER0.BANK_CODE,
      name = BASE.ISSUER.ISSUER0.NAME,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['bytes32', 'uint16', 'string', 'uint256'],
        [issuerId, bankCode, name, _deadline],
      )

    return issuer.connect(accounts[9]).addIssuer(issuerId, bankCode, name, BASE.TRACE_ID, _deadline, _sig[0])
  },
  addAccountId: async ({ issuer, accounts, options = {} }: AddAccountIdType) => {
    const { issuerId = BASE.ISSUER.ISSUER0.ID, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    return issuer.connect(accounts[9]).addAccountId(issuerId, accountId, BASE.TRACE_ID)
  },
  modIssuer: async ({ issuer, accounts, options = {} }: ModIssuerType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      name = BASE.ISSUER.ISSUER0.NAME,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'string', 'uint256'], [issuerId, name, _deadline])

    return issuer.connect(accounts[9]).modIssuer(issuerId, name, BASE.TRACE_ID, _deadline, _sig[0])
  },
  addIssuerRole: async ({ issuer, accounts, options = {} }: AddIssuerRoleType) => {
    let { sig, deadline, eoaKey = BASE.EOA.ADMIN, issuerId = BASE.ISSUER.ISSUER0.ID, issuerEoa } = options
    issuerEoa = issuerEoa ?? (await accounts[BASE.EOA.ISSUER1].getAddress())
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'address', 'uint256'], [issuerId, issuerEoa, _deadline])

    return issuer.connect(accounts[9]).addIssuerRole(issuerId, issuerEoa, BASE.TRACE_ID, _deadline, _sig[0])
  },
  modTokenLimit: async ({ issuer, accounts, options = {} }: ModTokenLimitType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      itemFlgs = [true, true, true, true, true],
      limitAmounts = [100, 100, 1000, 1000, 10],
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['bytes32', 'uint256', 'bool[] memory', 'uint256[] memory', 'uint256'],
        [issuerId, accountId, itemFlgs, limitAmounts, _deadline],
      )

    return issuer
      .connect(accounts[0])
      .modTokenLimit(issuerId, accountId, itemFlgs, limitAmounts, BASE.TRACE_ID, _deadline, _sig[0])
  },
  cumulativeReset: async ({ issuer, accounts, options = {} }: CumulativeResetType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'uint256', 'uint256'], [issuerId, accountId, _deadline])

    return issuer.connect(accounts[0]).cumulativeReset(issuerId, accountId, BASE.TRACE_ID, _deadline, _sig[0])
  },
  setAccountStatus: async ({ issuer, accounts, accountStatus, options = {} }: SetAccountStatusType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['bytes32', 'uint256', 'bytes32', 'uint256'],
        [issuerId, accountId, BASE.REASON_CODE1, _deadline],
      )

    return issuer
      .connect(accounts[0])
      .setAccountStatus(issuerId, accountId, accountStatus, BASE.REASON_CODE1, BASE.TRACE_ID, _deadline, _sig[0])
  },
  getAccountList: ({ issuer, params }: GetAccountListType) => {
    return issuer.getAccountList(...params) as unknown as Promise<EventReturnType['Issuer']['GetAccountList']>
  },
  getAccount: ({ issuer, params }: GetAccountType) => {
    return issuer.getAccount(...params) as unknown as Promise<EventReturnType['Issuer']['GetAccount']>
  },
  hasAccount: ({ issuer, params }: HasAccountType) => {
    return issuer.hasAccount(...params) as unknown as Promise<EventReturnType['Issuer']['HasAccount']>
  },
  addAccountRole: async ({ issuer, accounts, options = {} }: AddAccountRoleType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      accountEoa = BASE.ACCOUNT.EOA_ADDRESS,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['bytes32', 'bytes32', 'address', 'uint256'],
        [issuerId, accountId, accountEoa, _deadline],
      )

    return issuer
      .connect(accounts[0])
      .addAccountRole(issuerId, accountId, accountEoa, BASE.TRACE_ID, _deadline, _sig[0])
  },
  forceBurn: async ({ issuer, accounts, options = {} }: ForceBurnType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'bytes32', 'uint256'], [issuerId, accountId, _deadline])

    return issuer.connect(accounts[9]).forceBurn(issuerId, accountId, BASE.TRACE_ID, _deadline, _sig[0])
  },
  // getIssuersAll: async (
  //   issuer: IssuerInstance,
  //   offset: number,
  //   limit: number,
  //   {
  //     sig,
  //     deadline,
  //     eoaKey = BASE.EOA.ADMIN,
  //     hash = BASE.SALTS.GET_ISSUERS_ALL,
  //   }: Partial<GetIssuersAllOption & ContractCallOption> = {},
  // ) => {
  //   const _deadline = deadline ?? (await utils.getDeadline());
  //   const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline]);

  //   return issuer.getIssuersAll(offset, limit, _deadline, _sig[0]) as unknown as Promise<
  //     EventReturnType['Issuer']['GetIssuersAll']
  //   >;
  // },
  // setIssuersAll: async (
  //   issuer: IssuerInstance,
  //   issuers: any,
  //   accounts: SignerWithAddress[],
  //   {
  //     sig,
  //     deadline,
  //     eoaKey = BASE.EOA.ADMIN,
  //     hash = BASE.SALTS.SET_ISSUERS_ALL,
  //   }: Partial<SetIssuersAllOption & ContractCallOption> = {},
  // ) => {
  //   const _deadline = deadline ?? (await utils.getDeadline());
  //   const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline]);

  //   return issuer.setIssuersAll(issuers, _deadline, _sig[0], { from: accounts[9] });
  // },
}
