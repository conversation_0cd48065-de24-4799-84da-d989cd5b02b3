import { BASE, ERR } from '@test/common/consts'
import { IssuerInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert
const NOT_REG_ISSUER_ID = utils.toBytes32('x399')

describe('getIssuer()', () => {
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
    })

    describe('issuerが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuer({
          issuer: issuer,
          accounts: accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
            name: BASE.ISSUER.EMPTY.NAME,
          },
        })
      })

      it('issuer情報が取得できること /* DCPF-21196', async () => {
        const result = await issuerFuncs.getIssuer({ issuer: issuer, params: [BASE.ISSUER.ISSUER0.ID] })

        utils.assertEqualForEachField(result, {
          bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
          name: BASE.ISSUER.ISSUER0.NAME,
          err: '',
        })
      })

      it('空issuerNameが取得できること', async () => {
        const result = await issuerFuncs.getIssuer({ issuer: issuer, params: [BASE.ISSUER.ISSUER1.ID] })

        utils.assertEqualForEachField(result, {
          bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          name: BASE.ISSUER.EMPTY.NAME,
          err: '',
        })
      })

      it('未登録issuerId指定する場合、エラーが返されること', async () => {
        const result = await issuerFuncs.getIssuer({ issuer: issuer, params: [NOT_REG_ISSUER_ID] })

        assert.equal(result.err, ERR.ISSUER.ISSUER_ID_NOT_EXIST, 'err')
      })
    })
  })
})
