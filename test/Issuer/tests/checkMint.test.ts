import { BASE, ERR } from '@test/common/consts'
import {
  FinancialZoneAccountInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { before } from 'mocha'

const TERMINATED_ACCOUNT_ID = BASE.ACCOUNT.ACCOUNT6.ID

describe('checkMint()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let token: TokenInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider, token } = await contractFixture<IssuerContractType>())
    })

    describe('issuer, provider, providerRole, tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })

        await tokenFuncs.mint({ token: token, accounts: accounts, amount: 100 })
      })

      it('mintRoleがある場合、trueが取得できること', async () => {
        const result = await issuerFuncs.checkMint({ issuer: issuer, amount: 100 })

        utils.assertEqualForEachField(result, { success: true, err: '' })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ issuer, validator, provider, token, financialZoneAccount } = await contractFixture<IssuerContractType>())
    })

    describe('issuer, provider, providerRole, tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })

        await tokenFuncs.mint({ token: token, accounts: accounts, amount: 100 })
      })

      it('should return error when exceeded mint limit', async () => {
        const param = {
          transferLimit: 1100,
          chargeLimit: 1200,
          mintLimit: 90,
          burnLimit: 1400,
          cumulativeLimit: 80,
        }
        const limitAmounts = [
          param.transferLimit,
          param.chargeLimit,
          param.mintLimit,
          param.burnLimit,
          param.cumulativeLimit,
        ]

        await financialZoneAccountFuncs.addAccountLimit({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, limitAmounts, BASE.TRACE_ID],
        })

        const result = await issuerFuncs.checkMint({ issuer: issuer, amount: 100 })

        utils.assertEqualForEachField(result, { success: false, err: ERR.FINACCOUNT.EXCEEDED_MINT_LIMIT })
      })

      it('should return error when exceed daily limit', async () => {
        const result = await issuerFuncs.checkMint({ issuer: issuer, amount: 85 })

        utils.assertEqualForEachField(result, { success: false, err: ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT })
      })

      it('should return error when account is terminated', async () => {
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })
        await validatorFuncs.setTerminated({
          validator: validator,
          accounts: accounts,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })

        const result = await issuerFuncs.checkMint({
          issuer: issuer,
          amount: 100,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_DISABLED })
      })
    })

    describe('issuerRoleが登録されていない状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({
          issuer: issuer,
          accounts: accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER2.ID,
            bankCode: BASE.ISSUER.ISSUER2.BANK_CODE,
          },
        })
      })

      it('issuer権限がない場合、エラーをスローすること', async () => {
        const result = await issuerFuncs.checkMint({
          issuer: issuer,
          amount: 100,
          options: {
            issuerId: BASE.ISSUER.ISSUER2.ID,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ISSUER.ISSUER_NOT_ROLE })
      })
    })

    describe('issuerRoleが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuer({
          issuer: issuer,
          accounts: accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          },
        })
        for (const _issuer of [BASE.ISSUER.ISSUER0, BASE.ISSUER.ISSUER1]) {
          await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts, options: { issuerId: _issuer.ID } })
        }
        await validatorFuncs.addValidator({
          validator: validator,
          accounts: accounts,
          options: {
            validatorId: BASE.VALID.VALID1.ID,
            issuerId: BASE.ISSUER.ISSUER1.ID,
          },
        })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT5.ID,
            validatorId: BASE.VALID.VALID1.ID,
          },
        })
      })

      it('issuerに紐付けられていないaccountIdを指定した場合、エラーがスローされること', async () => {
        const result = await issuerFuncs.checkMint({
          issuer: issuer,
          amount: 100,
          options: { accountId: BASE.ACCOUNT.ACCOUNT7.ID },
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST })
      })

      it('解約されたaccountIdを指定した場合、エラーがスローされること', async () => {
        const result = await issuerFuncs.checkMint({
          issuer: issuer,
          amount: 100,
          options: { accountId: BASE.ACCOUNT.ACCOUNT6.ID },
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACCOUNT.ACCOUNT_DISABLED })
      })

      it('署名が不正である場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const amount = 100
        const result = await issuerFuncs.checkMint({ issuer: issuer, amount: 100, options: { sig: ['0x1234', ''] } })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const accountId = BASE.ACCOUNT.ACCOUNT0.ID
        const amount = 100
        const exceededDeadline = await utils.getExceededDeadline()

        const result = await issuerFuncs.checkMint({
          issuer: issuer,
          amount: 100,
          options: { deadline: exceededDeadline },
        })

        utils.assertEqualForEachField(result, { success: false, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })
    })
  })
})
