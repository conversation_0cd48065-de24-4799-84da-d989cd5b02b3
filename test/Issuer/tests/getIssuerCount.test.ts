import { BASE } from '@test/common/consts'
import { IssuerInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getIssuerCount()', () => {
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  const addIssuerParams = [BASE.ISSUER.ISSUER0, BASE.ISSUER.ISSUER1, BASE.ISSUER.ISSUER2]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
    })

    describe('issuerが登録されている状態', () => {
      before(async () => {
        const deadline = await utils.getDeadline()
        for (const param of addIssuerParams) {
          await issuerFuncs.addIssuer({
            issuer: issuer,
            accounts: accounts,
            options: {
              deadline: deadline + 30,
              issuerId: param.ID,
              bankCode: param.BANK_CODE,
              name: param.NAME,
            },
          })
        }
      })

      it('登録されているissuer数が取得できること', async () => {
        const result = await issuerFuncs.getIssuerCount({ issuer: issuer })

        assert.equal(result, 3, 'issuer count')
      })
    })
  })
})
