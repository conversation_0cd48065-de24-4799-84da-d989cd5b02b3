import { BASE, ERR } from '@test/common/consts'
import { IssuerInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getIssuerId()', () => {
  let issuer: IssuerInstance
  let accounts: SignerWithAddress[]

  const addIssuerParams = [BASE.ISSUER.ISSUER0, BASE.ISSUER.ISSUER1, BASE.ISSUER.ISSUER2]
  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
    })

    describe('issuerが登録されている状態', () => {
      before(async () => {
        const deadline = await utils.getDeadline()
        for (let i = 0; i < addIssuerParams.length; i++) {
          await issuerFuncs.addIssuer({
            issuer: issuer,
            accounts: accounts,
            options: {
              deadline: deadline + 30,
              issuerId: addIssuerParams[i].ID,
              bankCode: addIssuerParams[i].BANK_CODE,
              name: addIssuerParams[i].NAME,
            },
          })
        }
      })

      it('issuer情報が取得できること', async () => {
        const result = await issuerFuncs.getIssuerId({ issuer: issuer, params: [1] })

        utils.assertEqualForEachField(result, { issuerId: BASE.ISSUER.ISSUER1.ID, err: '' })
      })

      it('無効状態のissuer情報が取得できること', async () => {
        const result = await issuerFuncs.getIssuerId({ issuer: issuer, params: [2] })

        utils.assertEqualForEachField(result, { issuerId: BASE.ISSUER.ISSUER2.ID, err: '' })
      })

      it('範囲外のIndex指定の場合、エラーが返されること', async () => {
        const result = await issuerFuncs.getIssuerId({ issuer: issuer, params: [3] })

        assert.equal(result.err, ERR.ISSUER.ISSUER_OUT_OF_INDEX, 'err')
      })
    })
  })
})
