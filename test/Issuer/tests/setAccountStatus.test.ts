import { BASE, ERR } from '@test/common/consts'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

const NOT_REG_ISSUER_ID = utils.toBytes32('x399')

describe('setAccountStatus()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('Accountを凍結できること', async () => {
        await issuerFuncs.setAccountStatus({ issuer: issuer, accounts: accounts, accountStatus: BASE.STATUS.FROZEN })

        const result = await issuerFuncs.getAccount({
          issuer: issuer,
          params: [BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        // アカウントステータスが"frozen"となっていることを確認
        utils.assertEqualForEachField(result, {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          accountStatus: BASE.STATUS.FROZEN,
          err: '',
        })
      })
      it('Accountを凍結解除できること', async () => {
        await issuerFuncs.setAccountStatus({ issuer: issuer, accounts: accounts, accountStatus: BASE.STATUS.ACTIVE })

        const result = await issuerFuncs.getAccount({
          issuer: issuer,
          params: [BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        // アカウントステータスが"frozen"となっていることを確認
        utils.assertEqualForEachField(result, {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          balance: 0,
          reasonCode: BASE.REASON_CODE1,
          accountStatus: BASE.STATUS.ACTIVE,
          err: '',
        })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('should revert when issuerId is not valid', async () => {
        await expect(
          issuerFuncs.setAccountStatus({
            issuer: issuer,
            accounts: accounts,
            accountStatus: BASE.STATUS.FROZEN,
            options: { issuerId: BASE.ISSUER.EMPTY.ID },
          }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_INVALID_VAL)
      })

      it('Accountが存在しない場合、凍結に失敗すること', async () => {
        await expect(
          issuerFuncs.setAccountStatus({
            issuer: issuer,
            accounts: accounts,
            accountStatus: BASE.STATUS.FROZEN,
            options: {
              accountId: BASE.ACCOUNT.ACCOUNT11.ID,
            },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('Accountを強制償却済みからアクティブにできること', async () => {
        await expect(
          issuerFuncs.setAccountStatus({ issuer: issuer, accounts: accounts, accountStatus: BASE.STATUS.FORCE_BURNED }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })

      it('Accountが凍結以外の状態の場合、凍結解除に失敗すること', async () => {
        await expect(
          issuerFuncs.setAccountStatus({ issuer: issuer, accounts: accounts, accountStatus: BASE.STATUS.ACTIVE }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_NOT_FROZEN_OR_FORCE_BURNED)
      })

      it('Accountがアクティブ以外の状態の場合、凍結に失敗すること', async () => {
        await issuerFuncs.setAccountStatus({ issuer: issuer, accounts: accounts, accountStatus: BASE.STATUS.FROZEN })
        await expect(
          issuerFuncs.setAccountStatus({ issuer: issuer, accounts: accounts, accountStatus: BASE.STATUS.FROZEN }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_DISABLED)
      })

      it('未登録issuerIdを指定した場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.cumulativeReset({ issuer: issuer, accounts: accounts, options: { issuerId: NOT_REG_ISSUER_ID } }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_NOT_EXIST)
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.setAccountStatus({
            issuer: issuer,
            accounts: accounts,
            accountStatus: BASE.STATUS.ACTIVE,
            options: { accountId: BASE.ACCOUNT.EMPTY.ID },
          }),
        ).to.be.revertedWith(ERR.COMMON.INVALID_ACCOUNT_ID)
      })

      it('Issuer署名が不正である場合、凍結に失敗すること', async () => {
        await expect(
          issuerFuncs.setAccountStatus({
            issuer: issuer,
            accounts: accounts,
            accountStatus: BASE.STATUS.FROZEN,
            options: { sig: ['0x1234', ''] },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('Issuer署名が不正である場合、凍結解除に失敗すること', async () => {
        await expect(
          issuerFuncs.setAccountStatus({
            issuer: issuer,
            accounts: accounts,
            accountStatus: BASE.STATUS.ACTIVE,
            options: { sig: ['0x1234', ''] },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })
      it('issuer権限がない場合、エラーをスローすること', async () => {
        await expect(
          issuerFuncs.setAccountStatus({
            issuer: issuer,
            accounts: accounts,
            accountStatus: BASE.STATUS.ACTIVE,
            options: { eoaKey: BASE.EOA.ADMIN },
          }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ROLE)
      })
    })
  })
})
