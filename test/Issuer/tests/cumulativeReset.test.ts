import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { CumulativeResetOption, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

const NOT_REG_ISSUER_ID = utils.toBytes32('x399')

describe('cumulativeReset()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('Accountの累積限度額が初期化されること', async () => {
        const params: CumulativeResetOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        }

        const tx = await issuerFuncs.cumulativeReset({ issuer: issuer, accounts: accounts, options: params })

        const result = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        // 累積限度額が初期化されていることを確認
        utils.assertEqualForEachField(result.accountData.cumulativeLimit.toString, 0)

        await expect(tx)
          .to.emit(issuer, 'CumulativeReset')
          .withArgs(...Object.values(params), anyValue, anyValue)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
    })

    describe('issuerRoleが登録されている状態, accountが登録されていない状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
      })

      it('未登録accountIdを指定した場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.cumulativeReset({
            issuer: issuer,
            accounts: accounts,
            options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('空issuerIdを指定した場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.cumulativeReset({
            issuer: issuer,
            accounts: accounts,
            options: { issuerId: BASE.ISSUER.EMPTY.ID },
          }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_INVALID_VAL)
      })

      it('未登録issuerIdを指定した場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.cumulativeReset({ issuer: issuer, accounts: accounts, options: { issuerId: NOT_REG_ISSUER_ID } }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_NOT_EXIST)
      })

      it('空accountIdを指定した場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.cumulativeReset({
            issuer: issuer,
            accounts: accounts,
            options: { accountId: BASE.ACCOUNT.EMPTY.ID },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })

      it('署名が不正である場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.cumulativeReset({ issuer: issuer, accounts: accounts, options: { sig: ['0x1234', ''] } }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('issuer権限がない場合、エラーをスローすること', async () => {
        await expect(
          issuerFuncs.cumulativeReset({ issuer: issuer, accounts: accounts, options: { eoaKey: BASE.EOA.ADMIN } }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ROLE)
      })
    })

    describe('issuerが有効の状態, accountが解約状態', () => {
      before(async () => {
        await validatorFuncs.setTerminated({ validator: validator, accounts: accounts })
      })

      it('解約状態のaccountIdを指定した場合、エラーがスローされること', async () => {
        await expect(issuerFuncs.cumulativeReset({ issuer: issuer, accounts: accounts })).to.be.revertedWith(
          ERR.ACCOUNT.ACCOUNT_TERMINATED,
        )
      })
      // account.isTerminatedの戻り値のerrがあるケースは、事前準備ができないため未実施
    })

    describe('accountId exist on Issuer but not Account', () => {
      it('should revert when isTerminated returns error when accountId exist on Issuer but not Account', async () => {
        // UNREACHABLE: accountId already checked in the previous require,
        // this case happen when accountId exist on Issuer but not Account

        // Hack: fake call from validator to add accountId on Issuer only (bypass Account)
        await helpers.impersonateAccount(await validator.getAddress())
        await helpers.setBalance(await validator.getAddress(), 100n ** 18n)
        const fakeValidator = await ethers.getSigner(await validator.getAddress())
        await issuer
          .connect(fakeValidator)
          .addAccountId(BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT5.ID, BASE.TRACE_ID)

        const params: CumulativeResetOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT5.ID,
        }

        await expect(
          issuerFuncs.cumulativeReset({ issuer: issuer, accounts: accounts, options: params }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })
  })
})
