import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { ethers } from 'hardhat'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

const TERMINATED_ACCOUNT_ID = BASE.ACCOUNT.ACCOUNT6.ID

describe('getAccount()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })
        await validatorFuncs.setTerminated({
          validator: validator,
          accounts: accounts,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })
      })

      it('issuerに紐づくaccountの情報が取得できること', async () => {
        const result = await issuerFuncs.getAccount({
          issuer: issuer,
          params: [BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        utils.assertEqualForEachField(result, {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          balance: 0,
          accountStatus: BASE.STATUS.ACTIVE,
          reasonCode: BASE.REASON_CODE1,
          err: '',
        })
      })

      it('解約済みAccountの情報が取得できること', async () => {
        const result = await issuerFuncs.getAccount({
          issuer: issuer,
          params: [BASE.ISSUER.ISSUER0.ID, TERMINATED_ACCOUNT_ID],
        })

        utils.assertEqualForEachField(result, {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          balance: 0,
          reasonCode: BASE.REASON_CODE2,
          accountStatus: BASE.STATUS.TERMINATED,
          err: '',
        })
      })

      it('空accountIdを指定した場合、エラーが返されること', async () => {
        const result = await issuerFuncs.getAccount({
          issuer: issuer,
          params: [BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.EMPTY.ID],
        })

        assert.equal(result.err, ERR.COMMON.INVALID_ACCOUNT_ID, 'err')
      })

      it('未登録Account場合、エラーが返されること', async () => {
        const accountId = utils.toBytes32('x900')
        const issuerId = BASE.ISSUER.ISSUER0.ID

        const result = await issuerFuncs.getAccount({ issuer: issuer, params: [issuerId, accountId] })

        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'err')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider } = await contractFixture<IssuerContractType>())
    })

    describe('accountId exist on Issuer but not Account or vice versa', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })
        await validatorFuncs.setTerminated({
          validator: validator,
          accounts: accounts,
          options: { accountId: TERMINATED_ACCOUNT_ID },
        })
      })

      it('should return error and empty data when accountId exist on Account but not Issuer', async () => {
        // UNREACHABLE: accountId already checked in the previous require,
        // this case happen when accountId exist on Account but not Issuer

        // Hack: fake call from validator to add accountId on Issuer only (bypass Account)
        await helpers.impersonateAccount(await validator.getAddress())
        await helpers.setBalance(await validator.getAddress(), 100n ** 18n)
        const fakeValidator = await ethers.getSigner(await validator.getAddress())
        await issuer
          .connect(fakeValidator)
          .addAccountId(BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT5.ID, BASE.TRACE_ID)
        await helpers.stopImpersonatingAccount(await validator.getAddress())
        const tx = await issuer.getAccount(BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT5.ID)
        assert.equal(tx.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'err')
      })
    })
  })
})
