import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

describe('isFrozen()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider, token } = await contractFixture<IssuerContractType>())
    })

    describe('issuer, provider, providerRole, tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })

        await tokenFuncs.mint({ token: token, accounts: accounts, amount: 100 })
      })

      it('should return false and empty err when account is not frozen', async () => {
        const result = await issuer.isFrozen(BASE.ACCOUNT.ACCOUNT1.ID)

        utils.assertEqualForEachField(result, { frozen: false, err: '' })
      })

      it('should return true and empty error when account is frozen', async () => {
        await issuerFuncs.setAccountStatus({ issuer: issuer, accounts: accounts, accountStatus: BASE.STATUS.FROZEN })

        await issuerFuncs.getAccount({ issuer: issuer, params: [BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT1.ID] })

        const result = await issuer.isFrozen(BASE.ACCOUNT.ACCOUNT1.ID)
        utils.assertEqualForEachField(result, { frozen: true, err: '' })
      })
    })
  })
})
