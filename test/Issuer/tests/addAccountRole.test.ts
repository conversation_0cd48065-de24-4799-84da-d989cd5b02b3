import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import {
  AccountInstance,
  AddAccountRoleOption,
  IssuerInstance,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import _ from 'lodash'

import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('addAccountRole()', () => {
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let provider: ProviderInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider, account } = await contractFixture<IssuerContractType>())
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('roleが追加できること', async () => {
        const params: AddAccountRoleOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          accountEoa: await accounts[BASE.EOA.ACCOUNT].getAddress(),
        }

        const tx = await issuerFuncs.addAccountRole({ issuer: issuer, accounts: accounts, options: params })
        // 検証項目の値を取得するイベント関数がないためイベントの検証のみ行う
        await expect(tx)
          .to.emit(account, 'AddAccountRole')
          .withArgs(...Object.values(_.pick(params, ['accountId', 'accountEoa'])), anyValue)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, issuer, validator, provider, account } = await contractFixture<IssuerContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('issuerRoleがない場合、エラーがスローされること', async () => {
        await expect(issuerFuncs.addAccountRole({ issuer: issuer, accounts: accounts })).to.be.revertedWith(
          ERR.ISSUER.ISSUER_NOT_ROLE,
        )
      })
    })

    describe('issuerRoleが登録されている状態', () => {
      before(async () => {
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
      })

      it('issuerと紐付いていないaccountの場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.addAccountRole({
            issuer: issuer,
            accounts: accounts,
            options: { accountId: BASE.ACCOUNT.ACCOUNT7.ID },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('空issuerIdを指定した場合、エラーが返されること', async () => {
        await expect(
          issuerFuncs.addAccountRole({
            issuer: issuer,
            accounts: accounts,
            options: { issuerId: BASE.ISSUER.EMPTY.ID },
          }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_INVALID_VAL)
      })

      it('空accountIdを指定した場合、エラーが返されること', async () => {
        await expect(
          issuerFuncs.addAccountRole({
            issuer: issuer,
            accounts: accounts,
            options: { accountId: BASE.ACCOUNT.EMPTY.ID },
          }),
        ).to.be.revertedWith(ERR.COMMON.INVALID_ACCOUNT_ID)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()

        await expect(
          issuerFuncs.addAccountRole({ issuer: issuer, accounts: accounts, options: { deadline: exceededDeadline } }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名エラーがスローされること', async () => {
        await expect(
          issuerFuncs.addAccountRole({ issuer: issuer, accounts: accounts, options: { sig: ['0x1234', ''] } }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('should revert when _hasAccount return false', async () => {
        // Hack: fake validator can addAccountId directly without contract
        // This case mean Issuer add id by faker but not by validator
        const validatorAddress = await validator.getAddress()
        await helpers.setBalance(validatorAddress, 100n ** 18n)
        await helpers.impersonateAccount(validatorAddress) // impersonate validator
        const fakeValidator = await ethers.getSigner(validatorAddress)
        await issuer
          .connect(fakeValidator)
          .addAccountId(BASE.ISSUER.ISSUER0.ID, BASE.ACCOUNT.ACCOUNT7.ID, BASE.TRACE_ID)
        await helpers.stopImpersonatingAccount(validatorAddress) // stop impersonating
        await expect(
          issuerFuncs.addAccountRole({
            issuer: issuer,
            accounts: accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: BASE.ACCOUNT.ACCOUNT7.ID,
            },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('Eoaが空である場合はError', async () => {
        await expect(
          issuerFuncs.addAccountRole({
            issuer: issuer,
            accounts: accounts,
            options: { accountEoa: '******************************************' },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
      })
    })
  })
})
