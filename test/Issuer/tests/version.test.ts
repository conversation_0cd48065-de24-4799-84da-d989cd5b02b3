import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance } from '@test/common/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let issuer: IssuerInstance
  describe('正常系', () => {
    before(async () => {
      ;({ issuer } = await contractFixture<IssuerContractType>())
    })

    it('versionが取得できること', async () => {
      assert.equal(await issuerFuncs.version({ issuer: issuer }), BASE.APP.VERSION, 'version')
    })
  })
})
