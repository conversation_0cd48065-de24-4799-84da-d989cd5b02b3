import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getProviderCount()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })
    describe('providerが登録されていない状態', () => {
      it('0が取得できること', async () => {
        const result = await providerFuncs.getProviderCount({ provider: provider })

        assert.equal(result, 0, 'provider count')
      })
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
      })

      it('provider数が取得できること', async () => {
        const result = await providerFuncs.getProviderCount({ provider: provider })

        assert.equal(result, 1, 'provider count')
      })
    })
  })
})
