import '@nomicfoundation/hardhat-chai-matchers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { GetProviderAllOption, ProviderInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('setProviderAll()', () => {
  let provider: ProviderInstance

  describe('正常系', () => {
    const params: GetProviderAllOption = {
      providerId: BASE.PROV.PROV0.ID,
      providerData: {
        role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
        name: BASE.PROV.PROV0.NAME,
        zoneId: BASE.ZONE_ID.ID0,
        enabled: true,
      },
      providerEoa: BASE.PROV.EOA_ADDRESS,
      zoneData: [
        {
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
        },
      ],
    }

    before(async () => {
      ;({ provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されていない状態', () => {
      it('すべてのproviderが登録できること', async () => {
        await providerFuncs.setProviderAll({ provider: provider, prams: params })

        const getProvider = await providerFuncs.getProvider({ provider: provider })
        utils.assertEqualForEachField(getProvider, {
          providerId: params.providerId,
          zoneId: params.zoneData[0].zoneId,
          zoneName: params.zoneData[0].zoneName,
        })
      })
    })
  })

  describe('準正常系', () => {
    const params: GetProviderAllOption = {
      providerId: BASE.PROV.PROV0.ID,
      providerData: {
        role: '0x8edf85e2d94cd8e4ad77c5b0aa98c9c7200b6f1bc68c11b67db960b27cb0f5d4',
        name: BASE.PROV.PROV0.NAME,
        zoneId: BASE.ZONE_ID.ID0,
        enabled: true,
      },
      providerEoa: BASE.PROV.EOA_ADDRESS,
      zoneData: [
        {
          zoneId: String(BASE.ZONE_ID.ID0),
          zoneName: BASE.ZONE_NAME.NAME0,
        },
      ],
    }

    before(async () => {
      ;({ provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されていない状態', () => {
      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          providerFuncs.setProviderAll({ provider: provider, prams: params, options: { sig: ['0x1234', ''] } }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()
        await expect(
          providerFuncs.setProviderAll({ provider: provider, prams: params, options: { deadline: exceededDeadline } }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        await expect(providerFuncs.setProviderAll({ provider: provider, prams: { providerId: '123' } })).to.be.throw
      })
    })
  })
})
