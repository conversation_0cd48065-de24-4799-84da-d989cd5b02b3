import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance, TokenInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { tokenFuncs } from '@test/Token/helpers/function'
import { before } from 'mocha'

describe('hasToken()', () => {
  let provider: ProviderInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, token } = await contractFixture<ProviderContractType>())
    })

    describe('provider, role, tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
      })

      it('tokenが追加済みの場合、trueが返されること', async () => {
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const providerId = BASE.PROV.PROV0.ID

        const result = await providerFuncs.hasToken({ provider: provider, options: [tokenId, providerId, true] })

        utils.assertEqualForEachField(result, { success: true, err: '' })
      })

      it('未登録tokenIdの場合、falseが返されること', async () => {
        const tokenId = BASE.TOKEN.TOKEN2.ID
        const providerId = BASE.PROV.PROV0.ID

        const result = await providerFuncs.hasToken({ provider: provider, options: [tokenId, providerId, true] })

        utils.assertEqualForEachField(result, {
          success: false,
          err: ERR.TOKEN.TOKEN_ID_NOT_EXIST,
        })
      })

      it('空providerId指定の場合、falseが返されること', async () => {
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const providerId = BASE.PROV.EMPTY.ID

        const result = await providerFuncs.hasToken({ provider: provider, options: [tokenId, providerId, true] })

        utils.assertEqualForEachField(result, {
          success: false,
          err: ERR.PROV.PROV_INVALID_VAL,
        })
      })

      it('未登録providerIdの場合、falseが返されること', async () => {
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const providerId = BASE.PROV.PROV1.ID

        const result = await providerFuncs.hasToken({ provider: provider, options: [tokenId, providerId, true] })

        utils.assertEqualForEachField(result, {
          success: false,
          err: ERR.PROV.PROV_ID_NOT_EXIST,
        })
      })

      it('空tokenId指定の場合、falseが返されること', async () => {
        const tokenId = BASE.TOKEN.EMPTY
        const providerId = BASE.PROV.PROV0.ID

        const result = await providerFuncs.hasToken({ provider: provider, options: [tokenId, providerId, true] })

        utils.assertEqualForEachField(result, {
          success: false,
          err: ERR.PROV.PROV_INVALID_VAL,
        })
      })
    })

    describe('tokenが無効の状態', () => {
      before(async () => {
        await tokenFuncs.setTokenEnabled({ token: token, accounts: accounts, enabled: false })
      })

      it('falseが返されること', async () => {
        const tokenId = BASE.TOKEN.TOKEN1.ID
        const providerId = BASE.PROV.PROV0.ID

        const result = await providerFuncs.hasToken({ provider: provider, options: [tokenId, providerId, true] })

        utils.assertEqualForEachField(result, {
          success: false,
          err: ERR.TOKEN.TOKEN_DISABLED,
        })
      })
    })
  })
})
