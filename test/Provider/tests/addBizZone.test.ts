import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addBizZone()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {})
  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerId is not valid', () => {
      it('should revert when _providerId is 0x00', async () => {
        await expect(
          providerFuncs.addBizZone({
            provider: provider,
            accounts: accounts,
            options: {
              providerId: BASE.PROV.EMPTY.ID,
              zoneId: BASE.ZONE_ID.ID1,
              zoneName: BASE.ZONE_NAME.NAME1,
            },
          }),
        ).to.be.revertedWith(ERR.PROV.PROV_ID_EXIST)
      })
    })
  })
})
