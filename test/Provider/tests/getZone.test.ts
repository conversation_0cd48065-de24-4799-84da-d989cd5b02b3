import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getZone()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されていない状態', () => {
      it('エラーが返されること', async () => {
        const result = await providerFuncs.getZone({ provider: provider })

        assert.equal(result.err, ERR.PROV.PROV_NOT_EXIST, 'err')
      })
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
      })

      it('zone情報が取得できること', async () => {
        const result = await providerFuncs.getZone({ provider: provider })

        utils.assertEqualForEachField(result, {
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
          err: '',
        })
      })
    })

    // 状態をリセットする関数が実装されていないため、contract関数を使い状態をリセットする
    describe('providerのzoneIdが0の状態', () => {
      before(async () => {
        ;({ accounts, provider } = await contractFixture<ProviderContractType>())
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.EMPTY_ID },
        })
      })

      it('エラーが返されること', async () => {
        const result = await providerFuncs.getZone({ provider: provider })

        assert.equal(result.err, ERR.PROV.ZONEID_NOT_EXIST, 'err')
      })
    })
  })
})
