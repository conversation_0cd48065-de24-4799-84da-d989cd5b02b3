import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AddProviderRoleOption, ProviderInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addProviderRole()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
      })

      it('provider roleが登録できること', async () => {
        const params: AddProviderRoleOption = {
          providerId: BASE.PROV.PROV0.ID,
          providerEoa: await accounts[BASE.EOA.PROV1].getAddress(),
        }

        const tx = await providerFuncs.addProviderRole({ provider: provider, accounts: accounts, options: params })

        await expect(tx).to.emit(provider, 'AddProviderRole').withArgs(params.providerId, params.providerEoa, anyValue)

        const result = await providerFuncs.checkRole({ provider: provider, options: { providerId: params.providerId } })
        utils.assertEqualForEachField(result, { has: true, err: '' })
      })
    })

    describe('roleが登録されている状態', () => {
      it('同じproviderに対して複数providerRoleが登録できること', async () => {
        const params: AddProviderRoleOption = {
          providerId: BASE.PROV.PROV0.ID,
          providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
        }

        const tx = await providerFuncs.addProviderRole({ provider: provider, accounts: accounts, options: params })

        await expect(tx).to.emit(provider, 'AddProviderRole').withArgs(params.providerId, params.providerEoa, anyValue)

        const result = await providerFuncs.checkRole({
          provider: provider,
          options: {
            eoaKey: BASE.EOA.PROV2,
            providerId: params.providerId,
          },
        })
        utils.assertEqualForEachField(result, { has: true, err: '' })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
      })
      it('Admin権限ではない署名使用の場合、エラーがスローされること', async () => {
        await expect(
          providerFuncs.addProviderRole({
            provider: provider,
            accounts: accounts,
            options: { eoaKey: BASE.EOA.PROV1 },
          }),
        ).to.be.revertedWith(ERR.PROV.PROV_NOT_ADMIN_ROLE)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()

        await expect(
          providerFuncs.addProviderRole({
            provider: provider,
            accounts: accounts,
            options: { deadline: exceededDeadline },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          providerFuncs.addProviderRole({ provider: provider, accounts: accounts, options: { sig: ['0x2345', ''] } }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('未登録providerId指定の場合、エラーがスローされること', async () => {
        await expect(
          providerFuncs.addProviderRole({
            provider: provider,
            accounts: accounts,
            options: { providerId: BASE.PROV.PROV1.ID },
          }),
        ).to.be.revertedWith(ERR.PROV.PROV_ID_NOT_EXIST)
      })

      it('無効EOA(0x0)指定の場合、エラーがスローされること', async () => {
        await expect(
          providerFuncs.addProviderRole({
            provider: provider,
            accounts: accounts,
            options: {
              providerEoa: '0x0000000000000000000000000000000000000000',
            },
          }),
        ).to.be.revertedWith(ERR.PROV.PROV_INVALID_VAL)
      })
    })
  })
})
