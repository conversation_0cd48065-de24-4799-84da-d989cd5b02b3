import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ProviderInstance } from '@test/common/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getZoneName()', () => {
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider } = await contractFixture<ProviderContractType>())
    })

    describe('providerが登録されていない状態', () => {
      it('zoneNameは空が返されること', async () => {
        const result = await providerFuncs.getZoneName({ provider: provider, options: [BASE.ZONE_ID.ID0] })

        assert.strictEqual(
          result,
          BASE.ZONE_NAME.EMPTY_NAME,
          'Expected zoneName to be empty when provider is not registered',
        )
      })
    })

    describe('providerが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
      })

      it('zoneNameが取得できること', async () => {
        const result = await providerFuncs.getZoneName({ provider: provider, options: [BASE.ZONE_ID.ID0] })

        assert.strictEqual(
          result,
          BASE.ZONE_NAME.NAME0,
          `Expected zoneName to be '${BASE.ZONE_NAME.NAME0}' when provider is registered`,
        )
      })
    })
  })
})
