import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, TokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('setTerminated()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('accountの解約フラグを更新できること', async () => {
        const tx = await validatorFuncs.setTerminated({ validator: validator, accounts: accounts })

        const expectParams = {
          zoneId: BASE.ZONE_ID.ID0,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          reasonCode: BASE.REASON_CODE2,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(validator, 'SetTerminated')
          .withArgs(...Object.values(expectParams))

        const result = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const expectedObj = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.TERMINATED,
          balance: 0,
          reasonCode: BASE.REASON_CODE2,
          appliedAt: 0,
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: 0,
          terminatedAt: await utils.getLatestBlockTimestamp(),
        }
        utils.assertEqualForEachField(result.accountData, expectedObj)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ provider, issuer, validator, token } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('未登録validatorIdを指定した場合、エラーが返されること', async () => {
        await expect(
          validatorFuncs.setTerminated({
            validator: validator,
            accounts: accounts,
            options: { validatorId: BASE.VALID.VALID1.ID },
          }),
        ).to.be.revertedWith(ERR.VALID.VALIDATOR_ID_NOT_EXIST)
      })

      it('accountIdが存在しない場合、エラーがスローされること', async () => {
        await expect(
          validatorFuncs.setTerminated({
            validator: validator,
            accounts: accounts,
            options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })

      it('should revert when accountId balance is not 0', async () => {
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await expect(
          validatorFuncs.setTerminated({
            validator: validator,
            accounts: accounts,
            options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_BALANCE_NOT_ZERO)
      })
    })
    // provider.getZoneでエラーがあるケースは、事前準備ができないため未実施
  })
})
