import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ValidatorInstance } from '@test/common/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let validator: ValidatorInstance

  describe('正常系', () => {
    before(async () => {
      ;({ validator } = await contractFixture<ValidatorContractType>())
    })

    it('versionが取得できること', async () => {
      assert.equal(await validatorFuncs.version({ validator: validator }), BASE.APP.VERSION, 'version')
    })
  })
})
