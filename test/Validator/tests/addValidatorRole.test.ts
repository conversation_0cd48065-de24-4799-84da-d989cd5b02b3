import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AddValidatorRoleOption, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addValidatorRole()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validatorが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
      })

      it('validatorRoleが追加できること', async () => {
        const params: AddValidatorRoleOption = {
          validatorId: BASE.VALID.VALID0.ID,
          validatorEoa: await accounts[BASE.EOA.VALID1].getAddress(),
        }

        const tx = await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts, options: params })

        await expect(tx)
          .to.emit(validator, 'AddValidatorRole')
          .withArgs(...Object.values(params), BASE.TRACE_ID)
        // validatorRoleを取得するイベント関数がないためイベントの検証のみ行う
      })

      it('validatorRoleが別のアカウントに追加できること', async () => {
        const params: AddValidatorRoleOption = {
          validatorId: BASE.VALID.VALID0.ID,
          validatorEoa: await accounts[BASE.EOA.VALID2].getAddress(),
        }

        const tx = await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts, options: params })

        await expect(tx)
          .to.emit(validator, 'AddValidatorRole')
          .withArgs(...Object.values(params), BASE.TRACE_ID)
        // validatorRoleを取得するイベント関数がないためイベントの検証のみ行う
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validatorが登録されている状態', () => {
      before(async () => {
        ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
      })

      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        await expect(
          validatorFuncs.addValidatorRole({
            validator: validator,
            accounts: accounts,
            options: { eoaKey: BASE.EOA.ISSUER1 },
          }),
        ).to.be.revertedWith(ERR.VALID.VALIDATOR_NOT_ADMIN_ROLE)
      })

      it('validatorIdが不正の場合、エラーがスローされること', async () => {
        await expect(
          validatorFuncs.addValidatorRole({
            validator: validator,
            accounts: accounts,
            options: { validatorId: BASE.VALID.EMPTY.ID },
          }),
        ).to.be.revertedWith(ERR.VALID.VALIDATOR_INVALID_VAL)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()

        await expect(
          validatorFuncs.addValidatorRole({
            validator: validator,
            accounts: accounts,
            options: { deadline: exceededDeadline },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名が無効の場合、エラーがスローされること', async () => {
        await expect(
          validatorFuncs.addValidatorRole({
            validator: validator,
            accounts: accounts,
            options: { sig: ['0x2345', ''] },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('無効EOA(0x0)の場合、エラーがスローされること', async () => {
        await expect(
          validatorFuncs.addValidatorRole({
            validator: validator,
            accounts: accounts,
            options: {
              validatorEoa: '0x0000000000000000000000000000000000000000',
            },
          }),
        ).to.be.revertedWith(ERR.VALID.VALIDATOR_INVALID_VAL)
      })
    })
  })
})
