import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getValidatorAccountId()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('provider, providerRole, issuer, validator, token, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorAccountId({ validator: validator, accounts: accounts })
      })

      it('Validator管理のアカウントIDを取得できること', async () => {
        const result = await validatorFuncs.getValidatorAccountId({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID],
        })
        utils.assertEqualForEachField(result, {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('初期状態', () => {
      it('validatorが未登録の場合、エラーが返却されること', async () => {
        const result = await validatorFuncs.getValidatorAccountId({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID],
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_ID_NOT_EXIST, 'err')
      })
    })

    describe('provider, providerRole, issuer, validatorが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
      })

      it('accountが未登録の場合、エラーがスローされること', async () => {
        const result = await validatorFuncs.getValidatorAccountId({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID],
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_ACCOUNT_NOT_EXIST, 'err')
      })
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('空validatorIdを指定した場合、エラーがスローされること', async () => {
        const result = await validatorFuncs.getValidatorAccountId({
          validator: validator,
          prams: [BASE.VALID.EMPTY.ID],
        })

        assert.equal(result.err, ERR.VALID.VALIDATOR_INVALID_VAL, 'err')
      })
    })
  })
})
