import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { ValidatorContractType } from '@test/Validator/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('getDestinationAccount()', () => {
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
      })

      it('アカウント名を取得できる', async () => {
        const result = await validator.connect(accounts[0]).getDestinationAccount(BASE.ACCOUNT.ACCOUNT0.ID)
        assert.equal(result.err, '', 'err')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<ValidatorContractType>())
    })

    describe('accountが登録されていない状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
      })

      it('存在しないアカウントの場合、エラーが返却される', async () => {
        const result = await validator.connect(accounts[0]).getDestinationAccount(BASE.ACCOUNT.ACCOUNT0.ID)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'err')
      })

      it('アカウントIDが空の場合、エラーが返却される', async () => {
        const result = await validatorFuncs.getDestinationAccount({
          validator: validator,
          prams: [BASE.ACCOUNT.EMPTY.ID],
        })
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_INVALID_VAL, 'err')
      })
    })

    describe('accountが凍結状態', () => {
      before(async () => {
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await issuerFuncs.setAccountStatus({
          issuer: issuer,
          accounts: accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
      })

      it('アカウントが凍結状態の場合であっても、アカウント名を取得できる', async () => {
        const result = await validator.connect(accounts[0]).getDestinationAccount(BASE.ACCOUNT.ACCOUNT0.ID)
        assert.equal(result.accountName, BASE.ACCOUNT.ACCOUNT1.NAME, 'account name')
        assert.equal(result.err, '', 'err')
      })
    })

    describe('accountが解約状態', () => {
      before(async () => {
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await issuerFuncs.setAccountStatus({
          issuer: issuer,
          accounts: accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await validatorFuncs.setTerminated({
          validator: validator,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('アカウントが解約状態の場合、エラーが返却される', async () => {
        const result = await validator.connect(accounts[0]).getDestinationAccount(BASE.ACCOUNT.ACCOUNT1.ID)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_DISABLED, 'err')
      })
    })
  })
})
