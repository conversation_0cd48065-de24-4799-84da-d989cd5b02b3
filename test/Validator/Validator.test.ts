import '@nomicfoundation/hardhat-chai-matchers'

describe('Validator', async () => {
  require('./tests/version.test')
  require('./tests/addValidator.test')
  require('./tests/addValidatorRole.test')
  require('./tests/addAccount.test')
  require('./tests/addValidatorAccountId.test')
  require('./tests/syncAccount.test')
  require('./tests/modValidator.test')
  require('./tests/modAccount.test')
  require('./tests/hasValidator.test')
  require('./tests/setTerminated.test')
  require('./tests/getValidator.test')
  require('./tests/getValidatorCount.test')
  require('./tests/getValidatorId.test')
  require('./tests/hasAccount.test')
  require('./tests/getValidatorList.test')
  require('./tests/getAccountList.test')
  require('./tests/getAccountAll.test')
  require('./tests/setBizZoneTerminated.test')
  require('./tests/getZoneByAccountId.test')
  require('./tests/getAccount.test')
  require('./tests/getDestinationAccount.test')
  require('./tests/getValidatorAccountId.test')
  require('./tests/hasValidatorRole.test')
  require('./tests/getValidatorAll.test')
  require('./tests/setValidatorAll.test')
})
