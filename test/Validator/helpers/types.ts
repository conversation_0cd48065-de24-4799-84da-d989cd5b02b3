import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  AddAccountOption,
  AddValidatorAccountIdOption,
  AddValidatorOption,
  AddValidatorRoleOption,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  IssuerInstance,
  ModAccountOption,
  ModValidatorOption,
  ProviderInstance,
  SetActiveBusinessAccountWithZoneOption,
  SetTerminatedOption,
  SetValidatorAllOption,
  SyncAccountOption,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'

export type ValidatorContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  businessZoneAccount: BusinessZoneAccountInstance
  contractManager: ContractManagerInstance
}

export type ValidatorType = { validator: ValidatorInstance }

export type BaseAccountsType = ValidatorType & {
  accounts: SignerWithAddress[]
}

export type HasValidatorType = ValidatorType & {
  prams: Parameters<ValidatorInstance['hasValidator']>
}

export type GetValidatorType = ValidatorType & {
  prams: Parameters<ValidatorInstance['getValidator']>
}

export type GetValidatorIdType = ValidatorType & {
  prams: Parameters<ValidatorInstance['getValidatorId']>
}

export type HasAccountType = ValidatorType & {
  prams: Parameters<ValidatorInstance['hasAccount']>
}

export type GetValidatorListType = ValidatorType & {
  prams: Parameters<ValidatorInstance['getValidatorList']>
}

export type GetAccountListType = ValidatorType & {
  prams: Parameters<ValidatorInstance['getAccountList']>
}

export type GetAccountType = ValidatorType & {
  prams: Parameters<ValidatorInstance['getAccount']>
}

export type GetAccountAllType = ValidatorType & {
  prams: Parameters<ValidatorInstance['getAccountAll']>
}

export type GetZoneByAccountIdType = ValidatorType & {
  prams: Parameters<ValidatorInstance['getZoneByAccountId']>
}

export type GetDestinationAccountType = ValidatorType & {
  prams: Parameters<ValidatorInstance['getDestinationAccount']>
}

export type GetValidatorAccountIdType = ValidatorType & {
  prams: Parameters<ValidatorInstance['getValidatorAccountId']>
}

export type AddValidatorType = BaseAccountsType & {
  options?: Partial<AddValidatorOption & ContractCallOption>
}

export type AddValidatorRoleType = BaseAccountsType & {
  options?: Partial<AddValidatorRoleOption & ContractCallOption>
}

export type AddAccountType = BaseAccountsType & {
  options?: Partial<AddAccountOption>
}

export type SetActiveBusinessAccountWithZoneType = BaseAccountsType & {
  options?: Partial<SetActiveBusinessAccountWithZoneOption>
}

export type SetBizZoneTerminatedType = BaseAccountsType & {
  options?: Partial<SetActiveBusinessAccountWithZoneOption>
}

export type AddValidatorAccountIdType = BaseAccountsType & {
  options?: Partial<AddValidatorAccountIdOption>
}

export type SyncAccountType = BaseAccountsType & {
  options?: Partial<SyncAccountOption>
}

export type ModValidatorType = BaseAccountsType & {
  name: string
  options?: Partial<ModValidatorOption & ContractCallOption>
}

export type ModAccountType = BaseAccountsType & {
  options?: Partial<ModAccountOption & ContractCallOption>
}

export type SetTerminatedType = BaseAccountsType & {
  options?: Partial<SetTerminatedOption>
}

export type HasValidatorRoleType = ValidatorType & {
  options?: Partial<ValidatorInstance & ContractCallOption>
}

export type GetValidatorAllType = ValidatorType & { index: number }

export type SetValidatorAllType = {
  valid: ValidatorInstance
  validator: any
  accounts: SignerWithAddress[]
  options?: Partial<SetValidatorAllOption & ContractCallOption>
}
