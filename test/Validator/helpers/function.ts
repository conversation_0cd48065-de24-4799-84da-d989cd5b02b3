import privateKey from '@/privateKey'
import { BASE } from '@test/common/consts'
import { EventReturnType } from '@test/common/types'
import * as utils from '@test/common/utils'
import {
  AddAccountType,
  AddValidatorAccountIdType,
  AddValidatorRoleType,
  AddValidatorType,
  GetAccountAllType,
  GetAccountListType,
  GetAccountType,
  GetDestinationAccountType,
  GetValidatorAccountIdType,
  GetValidatorAllType,
  GetValidatorIdType,
  GetValidatorListType,
  GetValidatorType,
  GetZoneByAccountIdType,
  HasAccountType,
  HasValidatorRoleType,
  HasValidatorType,
  ModAccountType,
  ModValidatorType,
  SetActiveBusinessAccountWithZoneType,
  SetBizZoneTerminatedType,
  SetTerminatedType,
  SetValidatorAllType,
  SyncAccountType,
  ValidatorType,
} from './types'

export const validatorFuncs = {
  version: ({ validator }: ValidatorType) => {
    return validator.version()
  },
  hasValidator: ({ validator, prams }: HasValidatorType) => {
    return validator.hasValidator(...prams) as unknown as Promise<EventReturnType['Validator']['HasValidator']>
  },
  getValidator: ({ validator, prams }: GetValidatorType) => {
    return validator.getValidator(...prams) as unknown as Promise<EventReturnType['Validator']['GetValidator']>
  },
  getValidatorCount: ({ validator }: ValidatorType) => {
    return validator.getValidatorCount() as unknown as Promise<EventReturnType['Validator']['GetValidatorCount']>
  },
  getValidatorId: ({ validator, prams }: GetValidatorIdType) => {
    return validator.getValidatorId(...prams) as unknown as Promise<EventReturnType['Validator']['GetValidatorId']>
  },
  hasAccount: ({ validator, prams }: HasAccountType) => {
    return validator.hasAccount(...prams) as unknown as Promise<EventReturnType['Validator']['HasAccount']>
  },
  getValidatorList: ({ validator, prams }: GetValidatorListType) => {
    return validator.getValidatorList(...prams) as unknown as Promise<EventReturnType['Validator']['GetValidatorList']>
  },
  getAccountList: ({ validator, prams }: GetAccountListType) => {
    return validator.getAccountList(...prams) as unknown as Promise<EventReturnType['Validator']['GetAccountList']>
  },
  getAccount: ({ validator, prams }: GetAccountType) => {
    return validator.getAccount(...prams) as unknown as Promise<EventReturnType['Validator']['GetAccount']>
  },
  getAccountAll: ({ validator, prams }: GetAccountAllType) => {
    return validator.getAccountAll(...prams) as unknown as Promise<EventReturnType['Validator']['GetAccountAll']>
  },
  getZoneByAccountId: ({ validator, prams }: GetZoneByAccountIdType) => {
    return validator.getZoneByAccountId(...prams) as unknown as Promise<
      EventReturnType['Validator']['GetZoneByAccountId']
    >
  },
  getDestinationAccount: ({ validator, prams }: GetDestinationAccountType) => {
    return validator.getDestinationAccount(...prams) as unknown as Promise<
      EventReturnType['Validator']['GetDestinationAccount']
    >
  },
  getValidatorAccountId: ({ validator, prams }: GetValidatorAccountIdType) => {
    return validator.getValidatorAccountId(...prams) as unknown as Promise<
      EventReturnType['Validator']['GetValidatorAccountId']
    >
  },
  addValidator: async ({ validator, accounts, options = {} }: AddValidatorType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      validatorId = BASE.VALID.VALID0.ID,
      name = BASE.VALID.VALID0.NAME,
      issuerId = BASE.ISSUER.ISSUER0.ID,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(signer, ['bytes32', 'bytes32', 'bytes32', 'uint256'], [validatorId, issuerId, name, _deadline])

    return validator.connect(accounts[9]).addValidator(validatorId, issuerId, name, BASE.TRACE_ID, _deadline, _sig[0])
  },
  addValidatorRole: async ({ validator, accounts, options = {} }: AddValidatorRoleType) => {
    let { sig, deadline, eoaKey = BASE.EOA.ADMIN, validatorId = BASE.VALID.VALID0.ID, validatorEoa } = options
    validatorEoa = validatorEoa ?? (await accounts[BASE.EOA.VALID1].getAddress())
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'address', 'uint256'], [validatorId, validatorEoa, _deadline])

    return validator.connect(accounts[9]).addValidatorRole(validatorId, validatorEoa, BASE.TRACE_ID, _deadline, _sig[0])
  },
  addAccount: ({ validator, accounts, options = {} }: AddAccountType) => {
    const {
      validatorId = BASE.VALID.VALID0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      accountName = BASE.ACCOUNT.ACCOUNT1.NAME,
      limitAmounts = BASE.LIMIT_AMOUNTS,
    } = options
    return validator.connect(accounts[0]).addAccount(validatorId, accountId, accountName, limitAmounts, BASE.TRACE_ID)
  },
  setActiveBusinessAccountWithZone: ({ validator, accounts, options = {} }: SetActiveBusinessAccountWithZoneType) => {
    const {
      validatorId = BASE.VALID.VALID0.ID,
      zoneId = BASE.ZONE_ID.ID0,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    return validator
      .connect(accounts[0])
      .setActiveBusinessAccountWithZone(validatorId, zoneId, accountId, BASE.TRACE_ID)
  },
  setBizZoneTerminated: ({ validator, accounts, options = {} }: SetBizZoneTerminatedType) => {
    const { zoneId = BASE.ZONE_ID.ID0, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    return validator.connect(accounts[0]).setBizZoneTerminated(zoneId, accountId, BASE.TRACE_ID)
  },
  addValidatorAccountId: ({ validator, accounts, options = {} }: AddValidatorAccountIdType) => {
    const { validatorId = BASE.VALID.VALID0.ID, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    return validator.connect(accounts[0]).addValidatorAccountId(validatorId, accountId, BASE.TRACE_ID)
  },
  syncAccount: async ({ validator, accounts, options = {} }: SyncAccountType) => {
    const {
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      accountName = BASE.ACCOUNT.ACCOUNT1.NAME,
      validatorId = BASE.VALID.VALID0.ID,
      zoneId = BASE.ZONE_ID.ID0,
      zoneName = BASE.ZONE_NAME.NAME0,
      accountStatus = BASE.STATUS.APPLYING,
      reasonCode = BASE.REASON_CODE1,
      approvalAmount = BASE.ZERO_APPROVAL_VALUE,
    } = options
    return validator.syncAccount(
      validatorId,
      accountId,
      accountName,
      zoneId,
      zoneName,
      accountStatus,
      reasonCode,
      approvalAmount,
      BASE.TRACE_ID,
      {
        from: accounts[0],
      },
    )
  },
  // syncBusinessZoneAccountStatus: ({validator,accounts,options}:{
  //   validator: ValidatorInstance,
  //   accounts: any,
  //   options: any,
  // }
  // ) => {
  //   const {
  //     zoneId = BASE.ZONE_ID.ID0,
  //     zoneName = BASE.ZONE_NAME.NAME0,
  //     accountId = BASE.ACCOUNT.ACCOUNT1.ID,
  //     accountName = BASE.ACCOUNT.ACCOUNT1.NAME,
  //     accountStatus = BASE.STATUS.APPLYING,
  //   } = options;
  //   return validator.syncBusinessZoneAccountStatus(
  //     zoneId,
  //     zoneName,
  //     accountId,
  //     accountName,
  //     accountStatus,
  //     BASE.TRACE_ID,
  //     {
  //       from: accounts[0],
  //     },
  //   )
  // },
  modValidator: async ({ validator, accounts, name, options = {} }: ModValidatorType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, validatorId = BASE.VALID.VALID0.ID } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'bytes32', 'uint256'], [validatorId, name, _deadline])
    return validator.connect(accounts[9]).modValidator(validatorId, name, BASE.TRACE_ID, _deadline, _sig[0])
  },
  modAccount: async ({ validator, accounts, options = {} }: ModAccountType) => {
    const {
      validatorId = BASE.VALID.VALID0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
      accountName = BASE.ACCOUNT.ACCOUNT1.NAME,
      traceId = BASE.TRACE_ID,
      deadline,
      sig,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[BASE.EOA.VALID1],
        ['bytes32', 'bytes32', 'uint256'],
        [validatorId, accountId, _deadline],
      )
    return validator.connect(accounts[9]).modAccount(validatorId, accountId, accountName, traceId, _deadline, _sig[0])
  },
  setTerminated: ({ validator, accounts, options = {} }: SetTerminatedType) => {
    const { validatorId = BASE.VALID.VALID0.ID, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    //TODO reasonCodeのテストの実施
    const reasonCode = BASE.REASON_CODE2
    return validator.connect(accounts[0]).setTerminated(validatorId, accountId, reasonCode, BASE.TRACE_ID)
  },
  hasValidatorRole: async ({ validator, options = {} }: HasValidatorRoleType) => {
    const { sig, deadline, eoaKey = BASE.EOA.VALID1, validatorId = BASE.VALID.VALID0.ID, hash = 'morning' } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'string', 'uint256'], [validatorId, hash, _deadline])

    return validator.hasValidatorRole(validatorId, _sig[1], _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Validator']['HasValidatorRole']
    >
  },
  getValidatorAll: async ({ validator, index }: GetValidatorAllType) => {
    return validator.getValidatorAll(index) as unknown as Promise<EventReturnType['Validator']['GetValidatorAll']>
  },
  setValidatorAll: async ({ valid, validator, accounts, options = {} }: SetValidatorAllType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_VALIDATORS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return valid.connect(accounts[9]).setValidatorAll(validator, _deadline, _sig[0])
  },
}
