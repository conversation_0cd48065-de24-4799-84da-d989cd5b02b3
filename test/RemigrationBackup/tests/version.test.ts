import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { RemigrationRestoreInstance } from '@test/common/types'
import { remigrationFuncs } from '@test/RemigrationBackup/helpers/function'
import { RemigrationBackupContractType } from '@test/RemigrationBackup/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('version()', () => {
  let remigrationRestore: RemigrationRestoreInstance

  describe('正常系', () => {
    before(async () => {
      ;({ remigrationRestore } = await contractFixture<RemigrationBackupContractType>())
    })

    it('versionが取得できること', async () => {
      assert.equal(await remigrationFuncs.version({ remigration: remigrationRestore }), BASE.APP.VERSION, 'version')
    })
  })
})
