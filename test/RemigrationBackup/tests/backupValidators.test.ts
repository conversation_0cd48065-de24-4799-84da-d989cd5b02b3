import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, RemigrationBackupInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { remigrationFuncs } from '@test/RemigrationBackup/helpers/function'
import { RemigrationBackupContractType } from '@test/RemigrationBackup/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

// Chai global vars
declare let assert: Chai.Assert

describe('backupValidators()', () => {
  let accounts: SignerWithAddress[]
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let remigrationBackup: RemigrationBackupInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, remigrationBackup } =
        await contractFixture<RemigrationBackupContractType>())
    })

    describe('validatorが登録されていない状態', () => {
      it('空リストが取得できること', async () => {
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: 0,
          limit: 1000,
        })
        utils.assertEqualForEachField(result, {
          validators: [],
          totalCount: 0,
          err: '',
        })
      })
    })

    describe('Validatorsが登録されている状態', () => {
      let addValidatorAndAccountParams

      const assertList = (
        result: PromiseType<ReturnType<typeof remigrationFuncs.backupValidators>>,
        expected: typeof addValidatorAndAccountParams,
      ) => {
        assert.strictEqual(result.validators.length, expected.length, 'validators count')
        expected.forEach((v, i) => {
          assert.isString(result.validators[i].role, 'role')
          utils.assertEqualForEachField(result.validators[i], {
            validatorId: v.validator.ID,
            name: v.validator.NAME,
            issuerId: v.issuer.ID,
            enabled: true,
            validatorIdExistence: true,
            issuerIdLinkedFlag: true,
            validatorEoa: v.validatorEoa,
          })
          v.accounts.forEach((v2, i2) => {
            utils.assertEqualForEachField(result.validators[i].validAccountExistence[i2], {
              accountId: v2.ID,
              accountIdExistenceByValidatorId: true,
            })
          })
        })
      }

      before(async () => {
        addValidatorAndAccountParams = [
          {
            validator: BASE.VALID.VALID0,
            issuer: BASE.ISSUER.ISSUER0,
            accounts: [],
            validatorEoa: await accounts[0].getAddress(),
          },
          {
            validator: BASE.VALID.VALID1,
            issuer: BASE.ISSUER.ISSUER1,
            accounts: [],
            validatorEoa: await accounts[1].getAddress(),
          },
          {
            validator: BASE.VALID.VALID2,
            issuer: BASE.ISSUER.ISSUER2,
            accounts: [BASE.ACCOUNT.ACCOUNT1, BASE.ACCOUNT.ACCOUNT2],
            validatorEoa: await accounts[2].getAddress(),
          },
          {
            validator: BASE.VALID.VALID3,
            issuer: BASE.ISSUER.ISSUER3,
            accounts: [],
            validatorEoa: await accounts[3].getAddress(),
          },
          {
            validator: BASE.VALID.VALID4,
            issuer: BASE.ISSUER.ISSUER4,
            accounts: [],
            validatorEoa: await accounts[4].getAddress(),
          },
          {
            validator: BASE.VALID.VALID5,
            issuer: BASE.ISSUER.ISSUER5,
            accounts: [],
            validatorEoa: await accounts[5].getAddress(),
          },
          {
            validator: BASE.VALID.VALID6,
            issuer: BASE.ISSUER.ISSUER6,
            accounts: [],
            validatorEoa: await accounts[6].getAddress(),
          },
          {
            validator: BASE.VALID.VALID7,
            issuer: BASE.ISSUER.ISSUER7,
            accounts: [],
            validatorEoa: await accounts[7].getAddress(),
          },
          {
            validator: BASE.VALID.VALID8,
            issuer: BASE.ISSUER.ISSUER8,
            accounts: [],
            validatorEoa: await accounts[8].getAddress(),
          },
          {
            validator: BASE.VALID.VALID9,
            issuer: BASE.ISSUER.ISSUER9,
            accounts: [],
            validatorEoa: await accounts[9].getAddress(),
          },
          {
            validator: BASE.VALID.VALID10,
            issuer: BASE.ISSUER.ISSUER10,
            accounts: [],
            validatorEoa: await accounts[10].getAddress(),
          },
          {
            validator: BASE.VALID.VALID11,
            issuer: BASE.ISSUER.ISSUER11,
            accounts: [],
            validatorEoa: await accounts[11].getAddress(),
          },
          {
            validator: BASE.VALID.VALID12,
            issuer: BASE.ISSUER.ISSUER12,
            accounts: [],
            validatorEoa: await accounts[12].getAddress(),
          },
          {
            validator: BASE.VALID.VALID13,
            issuer: BASE.ISSUER.ISSUER13,
            accounts: [],
            validatorEoa: await accounts[13].getAddress(),
          },
          {
            validator: BASE.VALID.VALID14,
            issuer: BASE.ISSUER.ISSUER14,
            accounts: [],
            validatorEoa: await accounts[14].getAddress(),
          },
          {
            validator: BASE.VALID.VALID15,
            issuer: BASE.ISSUER.ISSUER15,
            accounts: [],
            validatorEoa: await accounts[15].getAddress(),
          },
          {
            validator: BASE.VALID.VALID16,
            issuer: BASE.ISSUER.ISSUER16,
            accounts: [],
            validatorEoa: await accounts[16].getAddress(),
          },
          {
            validator: BASE.VALID.VALID17,
            issuer: BASE.ISSUER.ISSUER17,
            accounts: [],
            validatorEoa: await accounts[17].getAddress(),
          },
          {
            validator: BASE.VALID.VALID18,
            issuer: BASE.ISSUER.ISSUER18,
            accounts: [],
            validatorEoa: await accounts[18].getAddress(),
          },
          {
            validator: BASE.VALID.VALID19,
            issuer: BASE.ISSUER.ISSUER19,
            accounts: [],
            validatorEoa: await accounts[19].getAddress(),
          },
        ]
        const deadline = await utils.getDeadline()
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        for (let i = 0; i < addValidatorAndAccountParams.length; i++) {
          await issuerFuncs.addIssuer({
            issuer: issuer,
            accounts: accounts,
            options: {
              issuerId: addValidatorAndAccountParams[i].issuer.ID,
              name: addValidatorAndAccountParams[i].issuer.NAME,
              bankCode: addValidatorAndAccountParams[i].issuer.BANK_CODE,
              deadline: deadline + 60,
            },
          })
          await validatorFuncs.addValidator({
            validator: validator,
            accounts: accounts,
            options: {
              validatorId: addValidatorAndAccountParams[i].validator.ID,
              name: addValidatorAndAccountParams[i].validator.NAME,
              issuerId: addValidatorAndAccountParams[i].issuer.ID,
              deadline: deadline + 60,
            },
          })
          await validatorFuncs.addValidatorRole({
            validator: validator,
            accounts: accounts,
            options: {
              validatorId: addValidatorAndAccountParams[i].validator.ID,
              validatorEoa: addValidatorAndAccountParams[i].validatorEoa,
              deadline: deadline + 60,
            },
          })
        }
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (let i = 0; i < addValidatorAndAccountParams.length; i++) {
          for (let j = 0; j < addValidatorAndAccountParams[i].accounts.length; j++) {
            await validatorFuncs.addAccount({
              validator: validator,
              accounts: accounts,
              options: {
                validatorId: addValidatorAndAccountParams[i].validator.ID,
                accountId: addValidatorAndAccountParams[i].accounts[j].ID,
              },
            })
          }
        }
      })

      it('offset0, limit10を指定した場合、1ページ目1項目目から10件取得できること', async () => {
        const offset = 0
        const limit = 10
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: addValidatorAndAccountParams.length, err: '' })
        assertList(result, addValidatorAndAccountParams.slice(0, 10))
      })

      it('offset1, limit10を指定した場合、2ページ目1項目目から10件取得できること', async () => {
        const offset = 1
        const limit = 10
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: addValidatorAndAccountParams.length, err: '' })
        assertList(result, addValidatorAndAccountParams.slice(1, 11))
      })

      it('offset1, limit2を指定した場合、2ページ目2項目目から2件取得できること', async () => {
        const offset = 2
        const limit = 2
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: addValidatorAndAccountParams.length, err: '' })
        assertList(result, [addValidatorAndAccountParams[2], addValidatorAndAccountParams[3]])
      })

      it('最後の1件が取得できること', async () => {
        const offset = 19
        const limit = 1
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: addValidatorAndAccountParams.length, err: '' })
        assertList(result, [addValidatorAndAccountParams[addValidatorAndAccountParams.length - 1]])
      })

      it('limitが取得上限(100件)以下の場合、データが取得ができること', async () => {
        const offset = 0
        const limit = 100
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: addValidatorAndAccountParams.length, err: '' })
        assertList(result, addValidatorAndAccountParams)
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: '' })
        assertList(result, [])
      })

      it('limitが取得上限(100件)より大きい場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 1001
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.VALID.VALIDATOR_TOO_LARGE_LIMIT,
        })
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        const offset = addValidatorAndAccountParams.length + 1
        const limit = 20
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.VALID.VALIDATOR_OFFSET_OUT_OF_INDEX,
        })
      })

      it('Admin権限がない場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: { eoaKey: 9 },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_ROLE })
      })

      it('署名無効の場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: {
            sig: ['0x1234', ''],
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('署名期限切れの場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const now = await utils.getExceededDeadline()
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: { deadline: now },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })
    })
  })
})
