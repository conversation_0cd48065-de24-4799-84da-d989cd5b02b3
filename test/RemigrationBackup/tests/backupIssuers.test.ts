import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, RemigrationBackupInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { remigrationFuncs } from '@test/RemigrationBackup/helpers/function'
import { RemigrationBackupContractType } from '@test/RemigrationBackup/helpers/types'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

describe('backupIssuers()', () => {
  let accounts: SignerWithAddress[]
  let issuer: IssuerInstance
  let remigrationBackup: RemigrationBackupInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, issuer, remigrationBackup } = await contractFixture<RemigrationBackupContractType>())
    })

    describe('issuerが登録されていない状態', () => {
      it('空リストが取得できること', async () => {
        const result = await remigrationFuncs.backupIssuers({ remigration: remigrationBackup, offset: 0, limit: 10 })
        utils.assertEqualForEachField(result, {
          issuers: [],
          totalCount: 0,
          err: '',
        })
      })
    })

    describe('Issuerが登録されている状態', () => {
      const addIssuerParams = [
        BASE.ISSUER.ISSUER0,
        BASE.ISSUER.ISSUER1,
        BASE.ISSUER.ISSUER2,
        BASE.ISSUER.ISSUER3,
        BASE.ISSUER.ISSUER4,
        BASE.ISSUER.ISSUER5,
        BASE.ISSUER.ISSUER6,
        BASE.ISSUER.ISSUER7,
        BASE.ISSUER.ISSUER8,
        BASE.ISSUER.ISSUER9,
        BASE.ISSUER.ISSUER10,
        BASE.ISSUER.ISSUER11,
        BASE.ISSUER.ISSUER12,
        BASE.ISSUER.ISSUER13,
        BASE.ISSUER.ISSUER14,
        BASE.ISSUER.ISSUER15,
        BASE.ISSUER.ISSUER16,
        BASE.ISSUER.ISSUER17,
        BASE.ISSUER.ISSUER18,
        BASE.ISSUER.ISSUER19,
      ]

      const assertList = (
        result: PromiseType<ReturnType<typeof issuerFuncs.getIssuerList>>,
        expected: typeof addIssuerParams,
      ) => {
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.issuers[i], {
            issuerId: v.ID,
            bankCode: v.BANK_CODE.toString(),
            name: v.NAME,
          })
        })
      }

      before(async () => {
        const deadline = await utils.getDeadline()
        // 20件分のaddIssuerを連続実行することで "sig timeout" が発生するためdeadlineにさらに30秒加算している
        for (let i = 0; i < addIssuerParams.length; i++) {
          await issuerFuncs.addIssuer({
            issuer: issuer,
            accounts: accounts,
            options: {
              deadline: deadline + 30,
              issuerId: addIssuerParams[i].ID,
              bankCode: addIssuerParams[i].BANK_CODE,
              name: addIssuerParams[i].NAME,
            },
          })
        }
      })

      it('offset0, limit10を指定した場合、1ページ目1項目目から10件取得できること', async () => {
        const offset = 0
        const limit = 10
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length, err: '' })
        assertList(result, addIssuerParams.slice(0, 10))
      })

      it('offset1, limit10を指定した場合、2ページ目1項目目から10件取得できること', async () => {
        const offset = 1
        const limit = 10
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length, err: '' })
        assertList(result, addIssuerParams.slice(1, 11))
      })

      it('offset1, limit2を指定した場合、3ページ目2項目目から2件取得できること', async () => {
        const offset = 2
        const limit = 2
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length, err: '' })
        assertList(result, [addIssuerParams[2], addIssuerParams[3]])
      })

      it('最後の1件が取得できること', async () => {
        const offset = 19
        const limit = 1
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length, err: '' })
        assertList(result, [addIssuerParams[addIssuerParams.length - 1]])
      })

      it('limitが取得上限(100件)以下の場合、データが取得ができること', async () => {
        const offset = 0
        const limit = 100
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: addIssuerParams.length, err: '' })
        assertList(result, addIssuerParams)
      })

      it('limitが0の場合、空リストが取得できること', async () => {
        const offset = 2
        const limit = 0
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: '' })
        assertList(result, [])
      })

      it('limitが取得上限(1000件)より大きい場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 1001
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.ISSUER.ISSUER_TOO_LARGE_LIMIT,
        })
      })

      it('offsetが登録されている件数以上の場合、エラーが返されること', async () => {
        const offset = addIssuerParams.length + 1
        const limit = 20
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
        })
        utils.assertEqualForEachField(result, {
          totalCount: 0,
          err: ERR.ISSUER.ISSUER_OFFSET_OUT_OF_INDEX,
        })
      })

      it('Admin権限がない場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: { eoaKey: 9 },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_ROLE })
      })

      it('署名無効の場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: {
            sig: ['0x1234', ''],
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('署名期限切れの場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const now = await utils.getExceededDeadline()
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: { deadline: now },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })
    })
  })
})
