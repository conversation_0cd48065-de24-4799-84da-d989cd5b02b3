import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  RemigrationBackupInstance,
  RemigrationRestoreInstance,
  RestoreAccountsOption,
  RestoreBusinessZoneAccountsOption,
  RestoreFinAccountsOption,
  RestoreIssuersOption,
  RestoreProviderOption,
  RestoreTokenOption,
  RestoreValidatorsOption,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'

export type RemigrationBackupContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  ibcToken: IBCTokenInstance
  businessZoneAccount: BusinessZoneAccountInstance
  remigrationBackup: RemigrationBackupInstance
  remigrationRestore: RemigrationRestoreInstance
  contractManager: ContractManagerInstance
}

export type RemigrationRestoreType = { remigration: RemigrationRestoreInstance }

export type RemigrationBackupType = { remigration: RemigrationBackupInstance }

export type BaseBackupType = RemigrationBackupType & {
  offset: number
  limit: number
}

export type BaseRestoreType = RemigrationRestoreType & {
  accounts: SignerWithAddress[]
}

export type BackupValidatorsType = BaseBackupType & {
  options?: Partial<RestoreValidatorsOption & ContractCallOption>
}

export type BackupProviderType = {
  remigration: RemigrationBackupInstance
  options?: Partial<RestoreProviderOption & ContractCallOption>
}

export type BackupAccountsType = BaseBackupType & {
  options?: Partial<RestoreProviderOption & ContractCallOption>
}

export type BackupFinancialZoneAccountsType = BaseBackupType & {
  options?: Partial<RestoreFinAccountsOption & ContractCallOption>
}

export type BackupIssuersType = BaseBackupType & {
  options?: Partial<RestoreIssuersOption & ContractCallOption>
}

export type BackupBusinessZoneAccountsType = BaseBackupType & {
  options?: Partial<RestoreBusinessZoneAccountsOption & ContractCallOption>
}

export type BackupTokenType = RemigrationBackupType & {
  options?: Partial<RestoreTokenOption & ContractCallOption>
}

export type RestoreValidatorsType = BaseRestoreType & {
  validators: any
  options?: Partial<RestoreValidatorsOption & ContractCallOption>
}

export type RestoreProvidersType = BaseRestoreType & {
  providers: any
  options?: Partial<RestoreProviderOption & ContractCallOption>
}

export type RestoreAccountsType = BaseRestoreType & {
  accs: any
  options?: Partial<RestoreAccountsOption & ContractCallOption>
}

export type RestoreFinancialZoneAccountsType = BaseRestoreType & {
  finAccounts: any
  options?: Partial<RestoreFinAccountsOption & ContractCallOption>
}

export type RestoreIssuersType = BaseRestoreType & {
  issuers: any
  options?: Partial<RestoreIssuersOption & ContractCallOption>
}

export type RestoreTokenType = BaseRestoreType & {
  token: any
  options?: Partial<RestoreIssuersOption & ContractCallOption>
}

export type RestoreBusinessZoneAccountsType = BaseRestoreType & {
  bizAccounts: any
  options?: Partial<RestoreBusinessZoneAccountsOption & ContractCallOption>
}
