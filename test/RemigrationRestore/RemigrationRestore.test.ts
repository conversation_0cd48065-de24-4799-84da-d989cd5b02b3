import '@nomicfoundation/hardhat-chai-matchers'

describe('Remigration', async () => {
  require('./tests/version.test')
  require('./tests/restoreValidators.test')
  require('./tests/restoreProvider.test')
  require('./tests/restoreAccounts.test')
  require('./tests/restoreFinancialZoneAccounts.test')
  require('./tests/restoreIssuers.test')
  require('./tests/restoreToken.test')
  require('./tests/restoreBusinessZoneAccounts.test')
})
