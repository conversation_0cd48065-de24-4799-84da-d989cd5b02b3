import { BASE, ERR } from '@test/common/consts'
import { RemigrationBackupInstance, RemigrationRestoreInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32 } from '@test/common/utils'
import { remigrationFuncs } from '@test/RemigrationRestore/helpers/function'
import { PromiseType } from 'utility-types'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { RemigrationRestoreContractType } from '@test/RemigrationRestore/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('restoreValidators()', () => {
  let accounts: SignerWithAddress[]
  let remigrationBackup: RemigrationBackupInstance
  let remigrationRestore: RemigrationRestoreInstance

  let createParams
  describe('正常系', () => {
    before(async () => {
      createParams = async (accounts: any, num: number) => {
        const params = await Promise.all(
          [...Array(num).keys()].map(async (index) => {
            return {
              validatorId: toBytes32(`x${index}`),
              name: toBytes32(`NAME${index}`),
              issuerId: toBytes32(`x${index}`),
              role: toBytes32(`ROLE${index}`),
              validatorAccountId: toBytes32(`x${index}`),
              enabled: true,
              validatorIdExistence: true,
              issuerIdLinkedFlag: true,
              validatorEoa: await accounts[index].getAddress(),
              validAccountExistence: [
                {
                  accountId: toBytes32(`VALIDATOR${index}_ACCOUNT1`),
                  accountIdExistenceByValidatorId: true,
                },
                {
                  accountId: toBytes32(`VALIDATOR${index}_ACCOUNT2`),
                  accountIdExistenceByValidatorId: true,
                },
              ],
            }
          }),
        )
        return params
      }
      ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
    })

    describe('初期状態', () => {
      let validatorsPram
      let assertList
      before(async () => {
        validatorsPram = await createParams(accounts, 20)
        assertList = (
          result: PromiseType<ReturnType<typeof remigrationFuncs.backupValidators>>,
          expected: typeof validatorsPram,
        ) => {
          assert.strictEqual(result.validators.length, expected.length, 'validators count')
          expected.forEach((v, i) => {
            assert.isString(result.validators[i].role, 'role')
            utils.assertEqualForEachField(result.validators[i], {
              validatorId: v.validatorId,
              name: v.name,
              issuerId: v.issuerId,
              enabled: v.enabled,
              validatorIdExistence: v.validatorIdExistence,
              issuerIdLinkedFlag: v.issuerIdLinkedFlag,
              validatorEoa: v.validatorEoa,
            })
            v.validAccountExistence.forEach((v2, i2) => {
              utils.assertEqualForEachField(result.validators[i].validAccountExistence[i2], {
                accountId: v2.accountId,
                accountIdExistenceByValidatorId: v2.accountIdExistenceByValidatorId,
              })
            })
          })
        }
      })
      it('全てのissuers(20件)が登録できること', async () => {
        await remigrationFuncs.restoreValidators({
          remigration: remigrationRestore,
          validators: validatorsPram,
          accounts: accounts,
        })
        const result = await remigrationFuncs.backupValidators({
          remigration: remigrationBackup,
          offset: 0,
          limit: validatorsPram.length,
        })
        assertList(result, validatorsPram)
      })
    })
  })

  describe('準正常系', () => {
    let validatorsPram

    before(async () => {
      ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
      validatorsPram = await createParams(accounts, 20)
    })

    describe('初期状態', () => {
      it('Admin権限がない場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreValidators({
            remigration: remigrationRestore,
            validators: validatorsPram,
            accounts: accounts,
            options: {
              eoaKey: BASE.EOA.ISSUER1,
            },
          }),
        ).to.be.revertedWith(ERR.VALID.VALIDATOR_NOT_ADMIN_ROLE)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreValidators({
            remigration: remigrationRestore,
            validators: validatorsPram,
            accounts: accounts,
            options: { sig: ['0x1234', ''] },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        await expect(
          remigrationFuncs.restoreValidators({
            remigration: remigrationRestore,
            validators: validatorsPram,
            accounts: accounts,
            options: { deadline: now },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const validatorsIvalid = [
          {
            validatorId: '123',
            name: 'name',
            issuerId: '123',
            role: '456',
            validatorAccountId: '123',
            enabled: true,
            validatorIdExistence: true,
            issuerIdLinkedFlag: true,
            issuerEoa: '',
            validAccountExistence: [],
          },
        ]
        await expect(
          remigrationFuncs.restoreValidators({
            remigration: remigrationRestore,
            validators: validatorsIvalid,
            accounts: accounts,
          }),
        ).to.be.throw
      })
    })
  })
})
