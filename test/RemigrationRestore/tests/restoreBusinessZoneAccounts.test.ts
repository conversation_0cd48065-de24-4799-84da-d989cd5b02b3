import { BASE, ERR } from '@test/common/consts'
import {
  AccountInstance,
  ContractManagerInstance,
  IssuerInstance,
  ProviderInstance,
  RemigrationBackupInstance,
  RemigrationRestoreInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32 } from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { remigrationFuncs } from '@test/RemigrationRestore/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { PromiseType } from 'utility-types'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { RemigrationRestoreContractType } from '@test/RemigrationRestore/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('restoreBusinessZoneAccounts()', () => {
  let accounts: SignerWithAddress[]
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let account: AccountInstance
  let contractManager: ContractManagerInstance
  let remigrationBackup: RemigrationBackupInstance
  let remigrationRestore: RemigrationRestoreInstance

  let createParams
  let createNotExistBizAccount
  describe('正常系', () => {
    let ibcAddress
    let ibcAddressString
    before(async () => {
      ;({ accounts, provider, issuer, validator, account, remigrationBackup, remigrationRestore, contractManager } =
        await contractFixture<RemigrationRestoreContractType>())

      ibcAddress = await accounts[0]
      ibcAddressString = await ibcAddress.getAddress()

      createParams = (num: number) =>
        [...Array(num).keys()].map((index) => {
          return {
            accountId: toBytes32(`x${index}`),
            bizAccountsByZoneId: [
              {
                zoneId: '3001',
                businessZoneAccountData: {
                  accountName: toBytes32(`NAME${index}`),
                  balance: (index + 100).toString(),
                  accountStatus: toBytes32('active'),
                  appliedAt: '**********',
                  registeredAt: '**********',
                  terminatingAt: '**********',
                  terminatedAt: '**********',
                },
                accountIdExistenceByZoneId: true,
              },
            ],
          }
        })

      createNotExistBizAccount = (num: number) =>
        [...Array(num).keys()].map((index) => {
          return {
            accountId: toBytes32(`x${index}`),
            bizAccountsByZoneId: [],
          }
        })
    })

    describe('初期状態', () => {
      let businessZoneAccountPram
      const assertList = (
        result: PromiseType<ReturnType<typeof remigrationFuncs.backupBusinessZoneAccounts>>,
        expected: typeof businessZoneAccountPram,
      ) => {
        assert.strictEqual(result.bizAccountsAll.length, expected.length, 'businessZoneAccounts count')
        expected.forEach((v, i) => {
          assert.isString(result.bizAccountsAll[i].accountId, expected[i].accountId)
          utils.assertEqualForEachField(result.bizAccountsAll[i].bizAccountsByZoneId[0], {
            zoneId: v.bizAccountsByZoneId[0].zoneId,
            accountIdExistenceByZoneId: v.bizAccountsByZoneId[0].accountIdExistenceByZoneId,
            businessZoneAccountData: v.bizAccountsByZoneId[0].businessZoneAccountData,
          })
        })
      }
      before(async () => {
        businessZoneAccountPram = createParams(20)
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await providerFuncs.addBizZone({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        for (let i = 0; i < businessZoneAccountPram.length; i++) {
          await validatorFuncs.addAccount({
            validator: validator,
            accounts: accounts,
            options: { accountId: businessZoneAccountPram[i].accountId },
          })
          await account.addZone(businessZoneAccountPram[i].accountId, BASE.ZONE_ID.ID1, BASE.TRACE_ID)
        }
      })
      describe('初期状態', () => {
        it('全てのbusinessZoneAccount(20件)が登録できること', async () => {
          await remigrationFuncs.restoreBusinessZoneAccounts({
            remigration: remigrationRestore,
            bizAccounts: businessZoneAccountPram,
            accounts: accounts,
          })
          const result = await remigrationFuncs.backupBusinessZoneAccounts({
            remigration: remigrationBackup,
            offset: 0,
            limit: 20,
          })
          await expect(
            result.bizAccountsAll.map((v) => {
              return {
                accountId: v.accountId,
                bizAccountsByZoneId: v.bizAccountsByZoneId.map((v2) => {
                  return {
                    zoneId: v2.zoneId,
                    accountIdExistenceByZoneId: v2.accountIdExistenceByZoneId,
                    businessZoneAccountData: {
                      accountName: v2.businessZoneAccountData.accountName,
                      balance: v2.businessZoneAccountData.balance,
                      accountStatus: v2.businessZoneAccountData.accountStatus,
                      appliedAt: v2.businessZoneAccountData.appliedAt,
                      registeredAt: v2.businessZoneAccountData.registeredAt,
                      terminatingAt: v2.businessZoneAccountData.terminatingAt,
                      terminatedAt: v2.businessZoneAccountData.terminatedAt,
                    },
                  }
                }),
              }
            }),
          ).to.deep.equal(businessZoneAccountPram)
        })
      })
    })
  })

  describe('準正常系', () => {
    let ibcAddress
    let ibcAddressString
    let businessZoneAccountPram
    let notExistBizAccountPram
    const assertList = (
      result: PromiseType<ReturnType<typeof remigrationFuncs.backupBusinessZoneAccounts>>,
      expected: typeof notExistBizAccountPram,
    ) => {
      assert.strictEqual(result.bizAccountsAll.length, expected.length, 'businessZoneAccounts count')
      expected.forEach((v, i) => {
        assert.isString(result.bizAccountsAll[i].accountId, expected[i].accountId)
        utils.assertEqualForEachField(result.bizAccountsAll[i], { bizAccountsByZoneId: [] })
      })
    }

    before(async () => {
      ;({ accounts, provider, issuer, validator, account, remigrationBackup, remigrationRestore } =
        await contractFixture<RemigrationRestoreContractType>())
      ibcAddress = await accounts[0]
      ibcAddressString = await ibcAddress.getAddress()
      businessZoneAccountPram = createParams(20)
      notExistBizAccountPram = createNotExistBizAccount(20)
    })

    describe('初期状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await providerFuncs.addBizZone({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        for (let i = 0; i < notExistBizAccountPram.length; i++) {
          await validatorFuncs.addAccount({
            validator: validator,
            accounts: accounts,
            options: { accountId: notExistBizAccountPram[i].accountId },
          })
          await account.addZone(notExistBizAccountPram[i].accountId, BASE.ZONE_ID.ID1, BASE.TRACE_ID)
        }
      })

      it('businessZoneAccountが登録されないこと', async () => {
        await remigrationFuncs.restoreBusinessZoneAccounts({
          remigration: remigrationRestore,
          bizAccounts: notExistBizAccountPram,
          accounts: accounts,
        })
        const result = await remigrationFuncs.backupBusinessZoneAccounts({
          remigration: remigrationBackup,
          offset: 0,
          limit: 20,
        })
        assertList(result, notExistBizAccountPram)
      })

      it('Admin権限がない場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreBusinessZoneAccounts({
            remigration: remigrationRestore,
            bizAccounts: businessZoneAccountPram,
            accounts: accounts,
            options: {
              eoaKey: BASE.EOA.ISSUER1,
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreBusinessZoneAccounts({
            remigration: remigrationRestore,
            bizAccounts: businessZoneAccountPram,
            accounts: accounts,
            options: {
              sig: ['0x1234', ''],
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        await expect(
          remigrationFuncs.restoreBusinessZoneAccounts({
            remigration: remigrationRestore,
            bizAccounts: businessZoneAccountPram,
            accounts: accounts,
            options: {
              deadline: now,
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const issuerInvalid = [
          {
            validatorId: '123',
            name: 'name',
            issuerId: '123',
            role: '456',
            validatorAccountId: '123',
            enabled: true,
            validatorIdExistence: true,
            issuerIdLinkedFlag: true,
            issuerEoa: '',
            validAccountExistence: [],
          },
        ]
        await expect(
          remigrationFuncs.restoreBusinessZoneAccounts({
            remigration: remigrationRestore,
            bizAccounts: issuerInvalid,
            accounts: accounts,
          }),
        ).to.be.throw
      })
    })
  })

  describe('準正常系', () => {
    let businessZoneAccountPram

    before(async () => {
      ;({ accounts, provider, issuer, validator, account, remigrationBackup, remigrationRestore } =
        await contractFixture<RemigrationRestoreContractType>())
      businessZoneAccountPram = createParams(20)
    })

    describe('初期状態', () => {
      it('Admin権限がない場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreBusinessZoneAccounts({
            remigration: remigrationRestore,
            bizAccounts: businessZoneAccountPram,
            accounts: accounts,
            options: {
              eoaKey: BASE.EOA.ISSUER1,
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreBusinessZoneAccounts({
            remigration: remigrationRestore,
            bizAccounts: businessZoneAccountPram,
            accounts: accounts,
            options: {
              sig: ['0x1234', ''],
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        await expect(
          remigrationFuncs.restoreBusinessZoneAccounts({
            remigration: remigrationRestore,
            bizAccounts: businessZoneAccountPram,
            accounts: accounts,
            options: {
              deadline: now,
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const issuerInvalid = [
          {
            validatorId: '123',
            name: 'name',
            issuerId: '123',
            role: '456',
            validatorAccountId: '123',
            enabled: true,
            validatorIdExistence: true,
            issuerIdLinkedFlag: true,
            issuerEoa: '',
            validAccountExistence: [],
          },
        ]
        await expect(
          remigrationFuncs.restoreBusinessZoneAccounts({
            remigration: remigrationRestore,
            bizAccounts: issuerInvalid,
            accounts: accounts,
          }),
        ).to.be.throw
      })
    })
  })
})
