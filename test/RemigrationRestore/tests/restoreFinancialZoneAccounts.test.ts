import { BASE, ERR } from '@test/common/consts'
import { RemigrationBackupInstance, RemigrationRestoreInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32 } from '@test/common/utils'
import { remigrationFuncs } from '@test/RemigrationRestore/helpers/function'
import { PromiseType } from 'utility-types'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { RemigrationRestoreContractType } from '@test/RemigrationRestore/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('restoreFinancialZoneAccounts()', () => {
  let accounts: SignerWithAddress[]
  let remigrationBackup: RemigrationBackupInstance
  let remigrationRestore: RemigrationRestoreInstance

  let createParams
  describe('正常系', () => {
    before(async () => {
      createParams = async (accounts: any, num: number) => {
        const createAccParams = Promise.all(
          [...Array(num).keys()].map((index) => {
            return {
              accountId: toBytes32(`x${index}`),
              financialZoneAccountData: {
                mintLimit: BASE.LIMIT_AMOUNTS[0],
                burnLimit: BASE.LIMIT_AMOUNTS[1],
                chargeLimit: BASE.LIMIT_AMOUNTS[2],
                transferLimit: BASE.LIMIT_AMOUNTS[3],
                cumulativeLimit: BASE.LIMIT_AMOUNTS[4],
                cumulativeAmount: 0,
                cumulativeDate: 0,
              },
            }
          }),
        )
        return createAccParams
      }
      ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
    })

    describe('初期状態', () => {
      let accountsPram
      const assertList = async (
        result: PromiseType<ReturnType<typeof remigrationFuncs.backupFinancialZoneAccounts>>,
        expected: typeof accountsPram,
      ) => {
        const blockTimestamp = await utils.getLatestBlockTimestamp()
        assert.strictEqual(result.financialZoneAccounts.length, expected.length, 'accounts count')
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.financialZoneAccounts[i], {
            accountId: v.accountId,
            financialZoneAccountData: {
              mintLimit: BASE.LIMIT_AMOUNTS[0],
              burnLimit: BASE.LIMIT_AMOUNTS[1],
              chargeLimit: BASE.LIMIT_AMOUNTS[2],
              transferLimit: BASE.LIMIT_AMOUNTS[3],
              cumulativeLimit: BASE.LIMIT_AMOUNTS[4],
              cumulativeAmount: 0,
              cumulativeDate: blockTimestamp,
            },
          })
        })
      }

      before(async () => {
        accountsPram = await createParams(accounts, 20)
      })

      it('全てのaccounts(20件)が登録できること', async () => {
        await remigrationFuncs.restoreFinancialZoneAccounts({
          remigration: remigrationRestore,
          finAccounts: accountsPram,
          accounts: accounts,
        })
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: 0,
          limit: 1000,
        })
        assertList(result, accountsPram)
      })
    })
  })

  describe('準正常系', () => {
    let accountsPram

    before(async () => {
      ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
      accountsPram = await createParams(accounts, 20)
    })

    describe('初期状態', () => {
      it('Admin権限がない場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreFinancialZoneAccounts({
            remigration: remigrationRestore,
            finAccounts: accountsPram,
            accounts: accounts,
            options: {
              eoaKey: BASE.EOA.ISSUER1,
            },
          }),
          ERR.ACCOUNT.ACCOUNT_NOT_ADMIN,
        )
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreFinancialZoneAccounts({
            remigration: remigrationRestore,
            finAccounts: accountsPram,
            accounts: accounts,
            options: {
              sig: ['0x1234', ''],
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        await expect(
          remigrationFuncs.restoreFinancialZoneAccounts({
            remigration: remigrationRestore,
            finAccounts: accountsPram,
            accounts: accounts,
            options: {
              deadline: now,
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const accountsIvalid = [
          {
            accountId: '123',
            accountName: 'name',
            accountStatus: '123',
            zoneIds: [],
            balance: '456',
            reasonCode: true,
            appliedAt: ********,
            registeredAt: ********,
            terminatingAt: ********,
            terminatedAt: ********,
            accountIdExistence: true,
            accountEoa: '',
            accountApprovalAll: [],
          },
        ]
        await expect(
          remigrationFuncs.restoreFinancialZoneAccounts({
            remigration: remigrationRestore,
            finAccounts: accountsIvalid,
            accounts: accounts,
          }),
        ).to.be.throw
      })
    })
  })
})
