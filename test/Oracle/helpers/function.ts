import { EventReturnType } from '@test/common/types'
import { GetBatchType, GetType } from './types'

/**
 * oracleのイベントを呼ぶ関数を持つobject
 */
export const oracleFuncs = {
  get: ({ oracle, prams }: GetType) => {
    return oracle.get(...prams) as unknown as Promise<EventReturnType['Oracle']['Get']>
  },
  getBatch: ({ oracle, prams }: GetBatchType) => {
    return oracle.getBatch(...prams) as unknown as Promise<EventReturnType['Oracle']['GetBatch']>
  },
  // 下記の関数はsignature等の生成が不要であり、共通化するメリットがないため未定義
  // version
  // addOracle
  // deleteOracle
  // set
  // setBatch
}
