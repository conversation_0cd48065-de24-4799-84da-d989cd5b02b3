import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { OracleInstance } from '@test/common/types'

export type OracleContractType = {
  accounts: SignerWithAddress[]
  oracle: OracleInstance
}

export type GetType = { oracle: OracleInstance; prams: Parameters<OracleInstance['get']> }

export type GetBatchType = { oracle: OracleInstance; prams: Parameters<OracleInstance['getBatch']> }
