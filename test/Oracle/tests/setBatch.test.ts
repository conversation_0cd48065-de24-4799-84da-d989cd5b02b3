import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { OracleInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { OracleContractType } from '@test/Oracle/helpers/types'
import chai, { expect } from 'chai'
import { before } from 'mocha'

describe('setBatch()', () => {
  let oracle: OracleInstance
  let accounts: SignerWithAddress[]

  const oracleId = 2
  const keys = [utils.toBytes32('0x01'), utils.toBytes32('0x02')]
  const values = [utils.toBytes32('0x64'), utils.toBytes32('0x65')]

  describe('正常系', () => {
    let invoker

    before(async () => {
      ;({ accounts, oracle } = await contractFixture<OracleContractType>())
      invoker = await accounts[1]
    })

    describe('oracleが登録されている状態', () => {
      before(async () => {
        await oracle.addOracle(oracleId, await invoker.getAddress())
      })

      it('複数件OracleValuesが設定されること', async () => {
        const txSet = await oracle.connect(invoker).setBatch(oracleId, keys, values)

        let logIndex = 0
        await expect(txSet).to.emit(oracle, 'SetOracleValue').withArgs(oracleId, keys[logIndex], values[logIndex])
        logIndex++
        const result = await oracle.getBatch(oracleId, keys)

        // Property of result is naming "values" but it is conflicting with the values function of object
        // Changed to 0, 1 to avoid conflict
        const expectParams = {
          0: values,
          1: '',
        }
        for (const [key, value] of Object.entries(result)) {
          chai.assert.deepEqual(value, expectParams[key])
        }
      })
    })
  })

  describe('準正常系', () => {
    let invoker

    before(async () => {
      ;({ accounts, oracle } = await contractFixture<OracleContractType>())
      invoker = await accounts[1]
    })

    describe('oracleが登録されている状態', () => {
      before(async () => {
        await oracle.addOracle(oracleId, await invoker.getAddress())
      })

      it('未登録invokerを指定した場合、エラーがスローされること', async () => {
        await expect(oracle.connect(accounts[2]).setBatch(oracleId, keys, values)).to.be.revertedWith(
          ERR.ORACLE.NOT_INVOKER_ADDRESS,
        )
      })

      it('keysとvaluesの要素数が不一致の場合、エラーがスローされること', async () => {
        await expect(oracle.connect(invoker).setBatch(oracleId, keys, [utils.toBytes32('0x64')])).to.be.revertedWith(
          ERR.ORACLE.WRONG_ARGUMENT_NUMBER,
        )
      })
    })
  })
})
