# ./scripts

Hardhatプロジェクトのルートディレクトリから実行することを想定している。

## Build

Solidityコンパイル。  
`--force`オプションでビルドされる。

```bash
scripts/build.sh
```

## Hardhat Test

Hardhat Test実行。  
内部では`--force`しているので毎回ビルドされる。

### 全ファイル

```bash
scripts/test.sh
```

### 個別ファイル

```bash
# test/以下のjs/tsファイル名を指定する。複数ファイルの指定も可能
scripts/test.sh test/XXX/XXX.test.ts test/XXX/XXX.test.ts
```

### 外部署名タイムアウトエラーになる

test.shを動かしていると、エラーになったことがない箇所がエラーになることがある。次に動かしてもエラーにならなかったり、別の箇所がエラーになったりする。  
そのエラーが外部署名のタイムアウトエラーでトランザクションを送信しないコントラクト関数呼び出しだった場合、ブロック生成をしない状態が続いてタイムアウトしている可能性がある。  
`hreHelpers.mine()`を呼び出すとブロック生成を促すことができるため、それで回避する。  

## Coverage

[solidity-coverage](https://github.com/sc-forks/solidity-coverage)によるカバレッジ計測(ファイルの指定も可能)。  
※全ファイルに対して実行するとタイムアウトでテストが失敗するため個別ファイル指定での実行のみ可能としている

### 個別ファイル

```bash
# test/以下のjs/tsファイル名を指定する。複数ファイルの指定も可能
scripts/coverage.sh '{test/XXX/XXX.test.ts,test/XXX/XXX.test.ts,}'
```

### coverage.shだけエラーになる

同じテストにもかかわらず、test.shでは成功するのにcoverage.shは失敗するということがある。  
私が見た限りでは、外部署名のタイムアウトエラーが発生し、それ以降のテストがエラーになるという現象しかなかった。発生する箇所もタイミングも不定で、起きたり起きなかったりした。  
これはsolidity-coverageが計測のためにSolidityファイルを編集してから実行していることによると考えられるのだが、確証はない。  
回避策は今のところ見つかっておらず、重たいアプリが動いていない状態にしたり、[Config Options](https://github.com/sc-forks/solidity-coverage#config-options)で`measureStatementCoverage`や`measureFunctionCoverage`をfalseにしたりして様子を見ている。

## Format

[prettier-plugin-solidity](https://github.com/prettier-solidity/prettier-plugin-solidity)によるSolidityファイルの自動整形。  
`contracts/`以下のSolidityファイルを自動整形する。

```bash
scripts/format.sh
```

## Generate Documentation

[solidoc](https://github.com/statechannels/solidoc)によるSolidity APIドキュメント生成(solidocを一部改造している)。  
`docs/interface`以下を削除して作成する。  
ドキュメントとして残すファイル以外は削除しているため、ファイルが増減した場合には修正が必要になることもある。  
動作詳細はスクリプトを参照のこと。

```bash
scripts/docs.sh
```

## ビルド・デプロイファイルの削除

`build/`と`.openzeppelin/`および`deployed_addr.log`を削除する。  
削除するとコントラクトのupgradeができなくなるため、本番環境では実行しないこと。

```bash
scripts/clean.sh
```

## Lint

[solhint](https://protofire.github.io/solhint/)によるlint。  
オプション `--fix` によって自動修正もできるようであるが、今は無効にしている。

```bash
scripts/lint.sh
```

## 関数一覧の取得
内部関数、initialize、version を除外した関数名を取得する。
```
scripts/list_external_functions.sh
```
