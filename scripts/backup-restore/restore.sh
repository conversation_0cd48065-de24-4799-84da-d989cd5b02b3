#!/bin/bash
ROOTDIR=$(
  cd $(dirname $BASH_SOURCE)/../..
  pwd
)
source $ROOTDIR/bin/common/utils.sh

# Select network from "hardhat list-networks"
if [ -n "$1" ]; then
  CHOICE=$1
else
  # Hardhatからネットワーク一覧を取得
  DIRECTORIES=$(npx hardhat list-networks)
  menu "Select the network to deploy:" "$DIRECTORIES"
  # menu 関数後の $CHOICE を確認
  if [ -z "$CHOICE" ]; then
    message "err" "Invalid selection. Please try again."
    exit 1
  fi
fi

# set NETWORK
export NETWORK=$CHOICE

if [[ ${NETWORK} == *local* ]]; then
  source $ROOTDIR/bin/main/local_scenario/_load_env.sh $NETWORK
else
  source $ROOTDIR/bin/main/_load_env.sh
fi

ROOTDIR=$(
  cd $(dirname $BASH_SOURCE)/../..
  pwd
)

pushd ${ROOTDIR} >/dev/null

print_double_line_title "Restoring all ${NETWORK} contracts..."

# リストアコマンドの実行とログの表示
execute_and_log() {
  local cmd=$1
  local task_name=$2

  message "i" "Starting restore for ${task_name}..."
  ${cmd}
  sleep 2 # ブロック確定の2秒待機
  message "i" "Restore for ${task_name} completed."
  echo ""
}
# リストアコマンドの実行（逐次実行）
execute_and_log "npx hardhat restoreProviders --network ${NETWORK}" "Providers"
execute_and_log "npx hardhat restoreToken --network ${NETWORK}" "Token"
execute_and_log "npx hardhat restoreIssuers --network ${NETWORK}" "Issuers"
execute_and_log "npx hardhat restoreValidators --network ${NETWORK}" "Validators"
execute_and_log "npx hardhat restoreAccounts --network ${NETWORK}" "Accounts"
execute_and_log "npx hardhat restoreFinAccounts --network ${NETWORK}" "FinAccounts"
execute_and_log "npx hardhat restoreBizAccounts --network ${NETWORK}" "BizAccounts"

if [[ ${NETWORK} == *Biz* ]]; then
  execute_and_log "npx hardhat restoreRETokens --network ${NETWORK}" "RETokens"
  execute_and_log "npx hardhat restoreTokenIdsByAccountId --network ${NETWORK}" "TokenIdsByAccountIds"
fi

message "s" "All restore processes done!"

popd >/dev/null
