#!/bin/bash
ROOTDIR=$(
  cd $(dirname $BASH_SOURCE)/../..
  pwd
)
source $ROOTDIR/bin/common/utils.sh

# Select network from "hardhat list-networks"
if [ -n "$1" ]; then
  CHOICE=$1
else
  # Hardhatからネットワーク一覧を取得
  DIRECTORIES=$(npx hardhat list-networks)
  menu "Select the network to deploy:" "$DIRECTORIES"
  # menu 関数後の $CHOICE を確認
  if [ -z "$CHOICE" ]; then
    message "err" "Invalid selection. Please try again."
    exit 1
  fi
fi

export NETWORK=$CHOICE

if [[ ${NETWORK} == *local* ]]; then
  source $ROOTDIR/bin/main/local_scenario/_load_env.sh $NETWORK
else
  source $ROOTDIR/bin/main/_load_env.sh
fi

ROOTDIR=$(
  cd $(dirname $BASH_SOURCE)/../..
  pwd
)

pushd ${ROOTDIR} >/dev/null

# 並列実行時にログを一時ファイルに保存
TMP_DIR=$(mktemp -d)

print_double_line_title "Backing up all ${NETWORK} contracts..."

# 各バックアップコマンドの実行とログのリアルタイム表示
execute_and_log() {
  local cmd=$1
  local log_file=$2
  local task_name=$3

  message "i" "Starting backup for ${task_name}..."
  ${cmd} >${log_file} 2>&1 &
  pids+=($!)  # 各プロセスIDを配列に格納
  task_names+=("${task_name}")
}

# バックアップコマンドの実行
pids=()  # プロセスIDを格納する配列
task_names=()  # タスク名を格納する配列

execute_and_log "npx hardhat backupProviders --network ${NETWORK}" "${TMP_DIR}/providers.log" "Providers"
execute_and_log "npx hardhat backupToken --network ${NETWORK}" "${TMP_DIR}/token.log" "Token"
execute_and_log "npx hardhat backupIssuers --network ${NETWORK}" "${TMP_DIR}/issuers.log" "Issuers"
execute_and_log "npx hardhat backupValidators --network ${NETWORK}" "${TMP_DIR}/validators.log" "Validators"
execute_and_log "npx hardhat backupAccounts --network ${NETWORK}" "${TMP_DIR}/accounts.log" "Accounts"
execute_and_log "npx hardhat backupFinAccounts --network ${NETWORK}" "${TMP_DIR}/finaccounts.log" "FinAccounts"
execute_and_log "npx hardhat backupBizAccounts --network ${NETWORK}" "${TMP_DIR}/bizaccounts.log" "BizAccounts"
if [[ ${NETWORK} == *Biz* ]]; then
  execute_and_log "npx hardhat backupRETokens --network ${NETWORK}" "${TMP_DIR}/retokens.log" "RETokens"
  execute_and_log "npx hardhat backupTokenIdsByAccountIds --network ${NETWORK}" "${TMP_DIR}/tokenidsbyaccountids.log" "TokenIdsByAccountIds"
fi

# すべてのプロセスの終了を待つ
for i in "${!pids[@]}"; do
  wait "${pids[$i]}"
  echo ""
  message "i" "Backup for ${task_names[$i]} completed:"
  cat "${TMP_DIR}/${task_names[$i],,}.log"
done

# 一時ディレクトリを削除
rm -rf ${TMP_DIR}

message "s" "All backup processes done!"

popd >/dev/null
