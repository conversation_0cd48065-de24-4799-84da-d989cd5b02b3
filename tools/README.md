# ./tools

コントラクトの関数を実行するスクリプト集

.sh ファイルを実行して利用（*.js ファイルは、同名の *.sh ファイルから利用される。)  
※ 先の2ファイルは例外的にシェルスクリプトが用意されていないため、jsを直実行  
deployedConfirmation.js, generateRandomHex.js（引数がなかったり、使用頻度が低いものはJavaScriptのみ作成している。）
## 主な使用方法
### カスタムタスクの実行方法

```bash
npx hardhat [カスタムタスク名] [引数..] --network [NETWORK]
```

`NETWORK`は、hardhat.config.tsの`networks`設定になる。  
いまは`local`と`main`のみ。

### シェルスクリプト

```bash
NETWORK=[NETWORK] ./tools/xxxx.sh [引数1] [引数2] ...
```

`NETWORK`はJavaScriptと同じ。スクリプトの実行前でなく、普通に環境変数として設定しておいてもよい(`./bin`で呼び出すスクリプトはほぼそうしている)。

## JavaScriptでのみ提供しているスクリプト

引数がなかったり、使用頻度が低いものはJavaScriptのみ作成している。

### デプロイ済みコントラクトバージョン取得

```bash
npx hardhat deployConfirmation_main --network [NETWORK]
```

## シェルスクリプトで提供しているスクリプト

### Transaction Receipt取得

```bash
./tools/receipt.sh [transactionHash]
```

### Revert Reason取得

```bash
./tools/revertReason.sh [transactionHash]
```

### 登録・EOA登録削除

* `registerProvider.sh`: Provider登録(IDおよびEOA)
* `registerProviderEOA.sh`: Provider登録(EOAのみ)
* `registerProviderID.sh`: Provider登録(IDのみ)
* `registerToken.sh`: Token登録
* `registerIssuer.sh`: Issuer登録(IDおよびEOA)
* `registerIssuerEOA.sh`: Issuer登録(EOAのみ)
* `registerIssuerID.sh`: Issuer登録(IDのみ)
* `registerValidator.sh`: Validator登録(IDおよびEOA)
* `registerValidatorEOA.sh`: Validator登録(EOAのみ)
* `registerValidatorID.sh`: Validator登録(IDのみ)
* `registerAccount.sh`: Account登録(IDおよびEOA)
* `registerAccountEOA.sh`: Account登録(EOAのみ)
* `registerEscrowAccount.sh`: EscrowAccount登録(IDのみ)
* `modifyIssuer.sh`: Issuerのステータスを変更する
* `modifyProv.sh`: Providerのステータスを変更する
* `modifyToken.sh`: Tokenのステータスを変更する
* `modifyValid.sh`: Validatorのステータスを変更する
* `setIBCApp.sh`: JPYTokenTransferBridgeのコントラクトアドレスを登録する
* `mintToken.sh`: TokenをMintする

## その他のスクリプト

### 秘密鍵作成

```bash
node ./tools/generateRandomHex.js
```

### パフォーマンス試験用
各Entityの追加を測定する為に作成されたシェル。
* `performanceMeasurementByRegisterAccount.sh`: Account登録のパフォーマンス試験用
* `performanceMeasurementByRegisterIssuer.sh`: Issuer登録のパフォーマンス試験用
* `performanceMeasurementByRegisterValid.sh`: Validator登録のパフォーマンス試験用

詳細は[Confleunce](https://decurret.atlassian.net/wiki/spaces/DIG/pages/********** )を参照


### gas測定用
gasを測定する為に作成されたシェル
* `transferBatchMeasureGas.sh`: TransferBatchを行った際のgas測定用
* `transferSingleMeasureGas.sh`: TransferSingleを行った際のgas測定用

詳細は[Confleunce]( https://decurret.atlassian.net/wiki/spaces/DIG/pages/********** )を参照

## 新規にスクリプトを作成する際の注意点

* truffleではスクリプト用のJSファイルを作成し、truffle exec [ファイル名]で実行していたが、Hardhatではtaskフォルダ配下に実行スクリプトを配置することで他のhardhatコマンドと同様に`npx hardhat [task名]`で実行できる
* Hardhatのtaskの具体的な実装方法は既存のtaskファイルや[公式ドキュメント](https://hardhat.org/hardhat-runner/docs/advanced/create-task)を参照すること
