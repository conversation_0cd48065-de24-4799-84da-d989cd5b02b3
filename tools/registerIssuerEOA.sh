#!/bin/bash

if [ $# -ne 3 ]; then
    echo "usage:"
    echo "    bash tools/registerIssuerEOA.sh [issuerID] [bankCode] [issuerPrivateK<PERSON>]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

ISSUER_ID="$1"
BANK_CODE="$2"
ISSUER_NAME="UNUSED"
KEY_ISSUER="$3"
FLAG="01"

npx hardhat registerIssuer \
--network ${NETWORK} \
--issuer-id ${ISSUER_ID} \
--bank-code ${BANK_CODE} \
--issuer-name ${ISSUER_NAME} \
--issuer-key ${KEY_ISSUER} \
--flag ${FLAG}

popd > /dev/null