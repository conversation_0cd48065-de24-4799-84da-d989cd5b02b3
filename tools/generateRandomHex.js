// USAGE
//      $ node tools/generateRandomHex.js
//      privateKey= 0x4b488acf1ff1ea8cddb7ef2fa2990294562dad69877955d348b11fdd778e96c8
//      EOA=        ******************************************

const Web3 = require("web3");
const web3 = new Web3();
const key = web3.utils.randomHex(32);
const account = web3.eth.accounts.privateKeyToAccount(key);
console.log(`privateKey= ${account.privateKey}`);
console.log(`EOA=        ${account.address}`);
