#!/bin/bash

if [ $# -ne 2 ]; then
    echo "usage:"
    echo "    bash tools/registerProvEOA.sh [provID] [provPrivateKey]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

PROV_ID="$1"
KEY_PROV="$2"
FLAG="01"

npx hardhat registerProvider \
--network ${NETWORK} \
--prov-id ${PROV_ID} \
--zone-id z"_" \
--zone-name "_" \
--prov-key ${KEY_PROV} \
--flag ${FLAG} \

popd > /dev/null
