#!/bin/bash

if [ $# -ne 3 ]; then
    echo "usage:"
    echo "    bash tools/registerProvID.sh [provID] [zoneId] [zoneName]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

PROV_ID="$1"
ZONE_ID="$2"
ZONE_NAME="$3"
FLAG="10"

npx hardhat registerProvider \
--network ${NETWORK} \
--prov-id ${PROV_ID} \
--zone-id ${ZONE_ID} \
--zone-name ${ZONE_NAME} \
--prov-key "ae6ae8e5ccbfb04590405997ee2d52d2b330726137b875053c36d94e974d162f" \
--flag ${FLAG} \

popd > /dev/null