if [ $# -ne 0 ]; then
    echo "usage:"
    echo "    bash tools/modTokenLimit.sh [issuerId] [limitAmounts]  [accountId] [itemFlgs] [issuerKey]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

issuerId="$1"
accountId="$2"
itemFlgs="$3"
limitAmounts="$4"
issuerKey="$5" 

npx hardhat modTokenLimit \
--network ${NETWORK} \
--issuerId "${issuerId}" \
--accountId "${accountId}" \
--itemFlgs "${itemFlgs}"\
--limitAmounts "${limitAmounts}"\
--issuerKey "${issuerKey}"\