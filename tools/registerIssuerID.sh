#!/bin/bash

if [ $# -ne 3 ]; then
    echo "usage:"
    echo "    bash tools/registerIssuerID.sh [issuerId] [bankCode] [issuerName]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

ISSUER_ID="$1"
BANK_CODE="$2"
ISSUER_NAME="$3"
KEY_ISSUER="c88b703fb08cbea894b6aeff5a544fb92e78a18e19814cd85da83b71f772aa6c"
FLAG="10"


npx hardhat registerIssuer \
--network ${NETWORK} \
--issuer-id ${ISSUER_ID} \
--bank-code ${BANK_CODE} \
--issuer-name ${ISSUER_NAME} \
--issuer-key ${KEY_ISSUER} \
--flag ${FLAG}

popd > /dev/null