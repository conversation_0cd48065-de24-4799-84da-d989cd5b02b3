#!/bin/bash

if [ $# -ne 7 ]; then
    echo "usage:"
    echo "    bash tools/registerToken.sh [providerID] [tokenID] [name] [symbol] [providerPrivateKey]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

PROV_ID="$1"
TOKEN_ID="$2"
TOKEN_NAME="$3"
TOKEN_SYMBOL="$4"
KEY_PROV="$5"

npx hardhat registerToken \
--network ${NETWORK} \
--prov-key ${KEY_PROV} \
--token-id ${TOKEN_ID} \
--prov-id ${PROV_ID} \
--token-name ${TOKEN_NAME} \
--symbol ${TOKEN_SYMBOL} 
