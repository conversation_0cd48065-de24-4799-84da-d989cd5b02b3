#!/bin/bash

if [ $# -ne 4 ]; then
    echo "usage:"
    echo "    bash tools/registerIssuer.sh [issuerID] [bankCode] [issuerName] [issuerPrivateKey]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

ISSUER_ID="$1"
BANK_CODE="$2"
ISSUER_NAME="$3"
KEY_ISSUER="$4"
FLAG="11"

npx hardhat registerIssuer \
--network ${NETWORK} \
--issuer-id ${ISSUER_ID} \
--bank-code ${BANK_CODE} \
--issuer-name ${ISSUER_NAME} \
--issuer-key ${KEY_ISSUER} \
--flag ${FLAG}

popd > /dev/null