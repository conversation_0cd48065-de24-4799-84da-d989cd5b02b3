#!/bin/bash

if [ $# -ne 4 ]; then
    echo "usage:"
    echo "    bash tools/addAccountRole.sh [accountID] [accountKey] [issuerID] [issuerKey]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

ACCOUNT_ID="$1"
ACCOUNT_KEY="$2"
ISSUER_ID="$3"
ISSUER_KEY="$4"

npx hardhat addAccountRole \
--network ${NETWORK} \
--account-id "${ACCOUNT_ID}" \
--account-key "${ACCOUNT_KEY}" \
--issuer-id "${ISSUER_ID}" \
--issuer-key "${ISSUER_KEY}"
