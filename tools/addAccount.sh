if [ $# -ne 1 ]; then
    echo "usage:"
    echo "    bash tools/addAccount.sh [validatorId] [accountId] [accountName] [limitAmounts]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

VALIDATOR_ID="$1"
ACCOUNT_ID="$2"
ACCOUNTNAME="$3"
LIMITAMOUNTS="$4"

npx hardhat addAccount \
--network ${NETWORK} \
--validator-Id "${validatorId}" \
--account-Id "${accountId}" \
--accountName "${accountName}" \
--limitAmounts "${limitAmounts}" \