#!/bin/bash

if [ $# -ne 6 ]; then
    echo "usage:"
    echo "    bash tools/registerAccount.sh [validatorID] [issuerID] [accountID] [accountPrivateKey] [issuerPrivateKey]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

VALID_ID="$1"
ISSUER_ID="$2"
ACCOUNT_ID="$3"
KEY_ACCOUNT="$4"
KEY_ISSUER="$5"
FLAG="11"

npx hardhat registerAcc \
--network ${NETWORK} \
--account-id ${ACCOUNT_ID} \
--account-key ${KEY_ACCOUNT} \
--valid-id ${VALID_ID} \
--issuer-id ${ISSUER_ID} \
--issuer-key ${KEY_ISSUER} \
--flag ${FLAG}