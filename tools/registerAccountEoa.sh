#!/bin/bash

if [ $# -ne 4 ]; then
    echo "usage:"
    echo "    bash tools/registerAccountEoa.sh [issuerID] [accountID] [accountPrivateKey] [issuerPrivateKey]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

VALID_ID="000"
ISSUER_ID="$1"
ACCOUNT_ID="$2"
KEY_ACCOUNT="$3"
KEY_ISSUER="$4"
FLAG="01"

npx hardhat registerAcc \
--network ${NETWORK} \
--account-id ${ACCOUNT_ID} \
--account-key ${KEY_ACCOUNT} \
--valid-id ${VALID_ID} \
--issuer-id ${ISSUER_ID} \
--issuer-key ${KEY_ISSUER} \
--flag ${FLAG}