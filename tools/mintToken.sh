#!/bin/bash

if [ $# -ne 4 ]; then
echo $#
echo "usage:"
echo "    bash tools/mintToken.sh [issuerID] [accountID] [amount] [issuerPrivateKey]"
exit
fi
if [ -z "${NETWORK}" ]; then
echo "need var NETWORK"
exit
fi

ISSUER_ID="$1"
ACCOUNT_ID="$2"
AMOUNT="$3"
PRIV_ISSUER="$4"

npx hardhat mintToken \
--issuer-id ${ISSUER_ID} \
--account-id ${ACCOUNT_ID} \
--amount ${AMOUNT} \
--issuer-key ${PRIV_ISSUER} \
--network "${NETWORK}"