#!/bin/bash

if [ $# -ne 4 ]; then
    echo "usage:"
    echo "    bash tools/registerValidator.sh [validID] [issuerID] [validName] [validPrivateKey]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

VALID_ID="$1"
ISSUER_ID="$2"
VALID_NAME="$3"
KEY_VALID="$4"
FLAG="11"

npx hardhat registerValidator \
--network ${NETWORK} \
--valid-id ${VALID_ID} \
--issuer-id ${ISSUER_ID} \
--valid-name ${VALID_NAME} \
--valid-key ${KEY_VALID} \
--flag ${FLAG}

popd > /dev/null