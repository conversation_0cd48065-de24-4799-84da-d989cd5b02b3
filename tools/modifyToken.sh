#!/bin/bash

if [ $# -ne 5 ]; then
    echo "usage:"
    echo "    bash tools/modifyToken.sh [tokenId] [name] [symbol] [providerPrivateKey]"
    exit
fi

if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

TOKEN_ID="$1"
TOKEN_NAME="$2"
TOKEN_SYMBOL="$3"
KEY_PROV="$5"

npx hardhat modifyToken \
--token-id "${TOKEN_ID}" \
--prov-key "${KEY_PROV}" \
--token-name "${TOKEN_NAME}" \
--symbol "${TOKEN_SYMBOL}" \
--network "${NETWORK}"
