#!/bin/bash

if [ $# -ne 3 ]; then
    echo "usage:"
    echo "    bash tools/registerValidID.sh [validID] [issuerID] [validName]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

VALID_ID="$1"
ISSUER_ID="$2"
VALID_NAME="$3"
KEY_VALID="0dbbe8e4ae425a6d2687f1a7e3ba17bc98c673636790f1b8ad91193c05875ef1"
FLAG="10"

npx hardhat registerValidator \
--network ${NETWORK} \
--valid-id ${VALID_ID} \
--issuer-id ${ISSUER_ID} \
--valid-name ${VALID_NAME} \
--valid-key ${KEY_VALID} \
--flag ${FLAG}

popd > /dev/null