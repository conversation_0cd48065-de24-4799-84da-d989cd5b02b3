if [ $# -ne 1 ]; then
    echo "usage:"
    echo "    bash tools/addValidator.sh [issuerId] [validatorId] [name] [validatorKey] "
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

ISSUER_ID="$1"
VALIDATOR_ID="$2"
NAME="$3"
VALIDATOR_KEY="$4"

npx hardhat addValidator \
--network ${NETWORK} \
--issuer-Id "${issuerId}" \
--validator-Id "${validatorId}" \
--name "${name}"\
--validator-Key "${validatorKey}"\