#!/bin/bash

if [ $# -ne 4 ]; then
    echo "usage:"
    echo "    bash tools/registerProvider.sh [provID] [zoneId] [zoneName] [provPrivateKey]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

PROV_ID="$1"
ZONE_ID="$2"
ZONE_NAME="$3"
KEY_PROV="$4"
FLAG="11"

npx hardhat registerProvider \
--network ${NETWORK} \
--prov-id ${PROV_ID} \
--zone-id ${ZONE_ID} \
--zone-name ${ZONE_NAME} \
--prov-key ${KEY_PROV} \
--flag ${FLAG} \

popd > /dev/null