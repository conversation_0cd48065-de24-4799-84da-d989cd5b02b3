#!/bin/bash

if [ $# -ne 2 ]; then
    echo "usage:"
    echo "    bash tools/registerValidEOA.sh [validID] [validPrivateKey]"
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

VALID_ID="$1"
ISSUER_ID="0000"
VALID_NAME="UNUSED"
KEY_VALID="$2"
FLAG="01"

npx hardhat registerValidator \
--network ${NETWORK} \
--valid-id ${VALID_ID} \
--issuer-id ${ISSUER_ID} \
--valid-name ${VALID_NAME} \
--valid-key ${KEY_VALID} \
--flag ${FLAG}

popd > /dev/null