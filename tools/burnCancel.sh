if [ $# -ne 1 ]; then
    echo "usage:"
    echo "    bash tools/burnCancel.sh [validatorId] [ownerId]  [spenderId] "
    exit
fi
if [ -z "${NETWORK}" ]; then
    echo "need var NETWORK"
    exit
fi

VissuerId="$1"
accountId="$2"
amount="$3"
blockTimestamp="$4"
issuerKey="$5" 

npx hardhat burnCancel \
--network ${NETWORK} \
--issuerId "${issuerId}" \
--accountId "${accountId}" \
--amount "${amount}"\
--blockTimestamp "${blockTimestamp}"\
--issuerKey "${issuerKey}"\
