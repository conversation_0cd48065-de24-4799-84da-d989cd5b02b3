#!/bin/bash

if [ $# -ne 1 ]; then
    echo "usage:"
    echo "    bash tools/transferSingleMeasureGas.sh [transferCount]"
    exit
fi

source bin/main/local_scenario/_load_env.sh
export TRANSFER_COUNT="$1"

npx hardhat transferSingleMeasureGas \
--from "${ACCOUNT_ID_1}" \
--to "${ACCOUNT_ID_2}" \
--amount 0 \
--misc1 "0" \
--misc2 "0" \
--memo "test" \
--trace-id "test" \
--transfer-count "${TRANSFER_COUNT}" \
--network localFin