#!/bin/bash

if [ $# -ne 1 ]; then
    echo "usage:"
    echo "    bash tools/performanceMeasurementByRegisterIssuer.sh [transactionCount]"
    exit
fi

ISSUER_ID=4400
BANK_CODE=100
ISSUER_NAME="test"
COUNT=$1

NETWORK=localFin

npx hardhat performanceMeasurementByRegisterIssuer --issuer-id "${ISSUER_ID}" --bank-code "${BANK_CODE}" --issuer-name "${ISSUER_NAME}" --count "${COUNT}"  --network "${NETWORK}"