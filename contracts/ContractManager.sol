// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/Struct.sol";
import "./interfaces/Error.sol";

/**
 * @dev ContractNameContractコントラクト
 */
contract ContractManager is Initializable, IContractManager {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    IAccessCtrl private _accessCtrl;
    IProvider private _provider;
    IAccount private _account;
    IFinancialZoneAccount private _fiannacialZoneAccount;
    IBusinessZoneAccount private _businessZoneAccount;
    IValidator private _validator;
    IIssuer private _issuer;
    IToken private _token;
    IIBCToken private _ibcToken;
    IFinancialCheck private _financialCheck;
    ITransferProxy private _transferProxy;
    mapping(string => address) private _ibcApps;

    // stack too deep対応
    struct Contracts {
        IAccessCtrl ctrlAddress;
        IProvider providerAddress;
        IIssuer issuerAddress;
        IValidator validatorAddress;
        IAccount accountAddress;
        IFinancialZoneAccount financialZoneAccountAddress;
        IBusinessZoneAccount businessZoneAccountAddress;
        IToken tokenAddress;
        IIBCToken ibcTokenAddress;
        IFinancialCheck financialCheckAddress;
        ITransferProxy transferProxyAddress;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     */
    function initialize() public initializer {} // solhint-disable-line no-empty-blocks

    /**
     * @dev コントラクトバージョン取得。
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev コントラクトを設定する。
     * @param contracts contracts
     *  ctrlAddress ctrlAddress
     *  providerAddress providerAddress
     *  issuerAddress issuerAddress
     *  validatorAddress validatorAddress
     *  accountAddress accountAddress
     *  financialZoneAccountAddress financialZoneAccountAddress
     *  businessZoneAccountAddress businessZoneAccountAddress
     *  tokenAddress tokenAddress
     *  financialCheckAddress financialCheckAddress
     *  transferProxyAddress transferProxyAddress
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function setContracts(
        Contracts memory contracts,
        uint256 deadline,
        bytes memory signature
    ) external {
        bytes32 hash = keccak256(
            abi.encode(
                contracts.ctrlAddress,
                contracts.providerAddress,
                contracts.issuerAddress,
                contracts.validatorAddress,
                contracts.accountAddress,
                contracts.financialZoneAccountAddress,
                contracts.businessZoneAccountAddress,
                contracts.tokenAddress,
                contracts.ibcTokenAddress,
                contracts.financialCheckAddress,
                contracts.transferProxyAddress,
                deadline
            )
        );
        (bool has, string memory errTmp) = contracts.ctrlAddress.checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(errTmp).length == 0, errTmp);
        require(has, Error.ACTRL_NOT_ADMIN_ROLE);

        _accessCtrl = contracts.ctrlAddress;
        _provider = contracts.providerAddress;
        _issuer = contracts.issuerAddress;
        _validator = contracts.validatorAddress;
        _account = contracts.accountAddress;
        _fiannacialZoneAccount = contracts.financialZoneAccountAddress;
        _businessZoneAccount = contracts.businessZoneAccountAddress;
        _token = contracts.tokenAddress;
        _ibcToken = contracts.ibcTokenAddress;
        _financialCheck = contracts.financialCheckAddress;
        _transferProxy = contracts.transferProxyAddress;
    }

    /**
     * @dev IBCAPPのアドレスを設定する。
     * @param ibcAppAddress ibcAppAddress
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function setIbcApp(
        address ibcAppAddress,
        string memory ibcAppName,
        uint256 deadline,
        bytes memory signature
    ) external override {
        bytes32 hash = keccak256(abi.encode(ibcAppAddress, deadline));
        (bool has, string memory errTmp) = this.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(errTmp).length == 0, errTmp);
        require(has, Error.ACTRL_NOT_ADMIN_ROLE);

        _ibcApps[ibcAppName] = ibcAppAddress;
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////
    /**
     * @dev accessCtrl取得。
     * @return accessCtrl accessCtrl
     */
    function accessCtrl() external view override returns (IAccessCtrl) {
        require(address(_accessCtrl) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _accessCtrl;
    }

    /**
     * @dev provider取得。
     * @return provider provider
     */
    function provider() external view override returns (IProvider) {
        require(address(_provider) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _provider;
    }

    /**
     * @dev account取得。
     * @return account account
     */
    function account() external view override returns (IAccount) {
        require(address(_account) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _account;
    }

    /**
     * @dev financialZoneAccount取得。
     * @return financialZoneAccount financialZoneAccount
     */
    function financialZoneAccount() external view override returns (IFinancialZoneAccount) {
        require(address(_fiannacialZoneAccount) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _fiannacialZoneAccount;
    }

    /**
     * @dev businessZoneAccount取得
     * @return businessZoneAccount businessZoneAccount
     */
    function businessZoneAccount() external view override returns (IBusinessZoneAccount) {
        require(address(_businessZoneAccount) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _businessZoneAccount;
    }

    /**
     * @dev validator取得。
     * @return validator validator
     */
    function validator() external view override returns (IValidator) {
        require(address(_validator) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _validator;
    }

    /**
     * @dev issuer取得。
     * @return issuer issuer
     */
    function issuer() external view override returns (IIssuer) {
        require(address(_issuer) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _issuer;
    }

    /**
     * @dev token取得。
     * @return token token
     */
    function token() external view override returns (IToken) {
        require(address(_token) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _token;
    }

    /**
     * @dev tokenPrivateService取得。
     * @return tokenPrivateService tokenPrivateService
     */
    function ibcToken() external view override returns (IIBCToken) {
        require(address(_ibcToken) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _ibcToken;
    }

    /**
     * @dev FinancialCheck取得。
     * @return token token
     */
    function financialCheck() external view override returns (IFinancialCheck) {
        require(address(_financialCheck) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _financialCheck;
    }

    /**
     * @dev transferProxy取得。
     * @return transferProxy transferProxy
     */
    function transferProxy() external view override returns (ITransferProxy) {
        require(address(_transferProxy) != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return _transferProxy;
    }

    /**
     * @dev balanceSyncBridge取得。
     * @return balanceSyncBridge balanceSyncBridge
     */
    function balanceSyncBridge() external view override returns (IBalanceSyncBridge) {
        require(_ibcApps[Constant._BALANCE_SYNC] != address(0), Error.CONTRACT_ADDRESS_NOT_EXIST);
        return IBalanceSyncBridge(_ibcApps[Constant._BALANCE_SYNC]);
    }

    /**
     * @dev ibcApp取得。
     * @return ibcApp ibcApp
     */
    function ibcApp(string memory ibcAppName) external view override returns (address) {
        return _ibcApps[ibcAppName];
    }

    ///////////////////////////////////
    // for upgrade contracts
    // deprecated
    ///////////////////////////////////

    // uint256[50] private __gap;
}
