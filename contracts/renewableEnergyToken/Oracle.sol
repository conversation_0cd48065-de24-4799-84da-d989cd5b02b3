// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "../interfaces/IOracle.sol";

/**
 * @dev Oracleコントラクト
 *　　　 カスタムコントラクトで使用したい外部データを管理するコントラクト
 */
contract Oracle is Initializable, IOracle {
    /** @dev oracleID => オラクルの実行アドレス */
    mapping(uint256 => address) private invokers;

    /** @dev oracleID => key => value */
    mapping(uint256 => mapping(bytes32 => bytes32)) private oracleValues;

    function initialize() public initializer {} // solhint-disable-line no-empty-blocks

    /**
     * @dev コントラクトバージョン取得
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @dev Oracleの登録を行う。
     */
    function addOracle(uint256 oracleId, address invoker) external override {
        // 入力値チェック
        {
            // OracleIdが未入力であればエラー
            require(oracleId != 0, "Oracle invalid value");
            // Invokerが未入力であればエラー
            require(invoker != address(0), "invoker invalid value");
        }
        // OracleIdとInvokerの登録を行う
        invokers[oracleId] = invoker;
        // EventをEmitする
        emit AddOracle(oracleId, invoker);
    }

    /**
     * @dev Oracleの削除を行う。残高管理DLTにてValidatorの権限チェックを行った後に実行される。
     */
    function deleteOracle(uint256 oracleId) external override {
        // 入力値チェック
        {
            // OracleIdが未入力であればエラー
            require(oracleId != 0, "Oracle invalid value");
        }
        // OracleIdとInvokerの登録を行う
        delete invokers[oracleId];
        // EventをEmitする
        emit DeleteOracle(oracleId);
    }

    /**
     * @dev OracleValueのセットを行う。値が変更された場合はtrueを返す。
     */
    function set(
        uint256 oracleId,
        bytes32 key,
        bytes32 value
    ) external override {
        // 入力値チェック
        {
            // 登録されていないinvokerからのリクエストである場合はエラー
            if (msg.sender != address(this)) {
                require(msg.sender == invokers[oracleId], "not invoker address");
            }
        }
        // OracleIdに対しvalueが重複していない場合は実行する
        if (oracleValues[oracleId][key] != value) {
            oracleValues[oracleId][key] = value;
            // EventをEmitする
            emit SetOracleValue(oracleId, key, oracleValues[oracleId][key]);
        }
    }

    /**
     * @dev OracleValueを複数件セットを行う。値が変更された場合はtrueを返す。
     */
    function setBatch(
        uint256 oracleId,
        bytes32[] memory keys,
        bytes32[] memory values
    ) external override {
        // 入力値チェック
        {
            // 登録されていないinvokerからのリクエストである場合はエラー
            require(msg.sender == invokers[oracleId], "not invoker address");
            // keyとvalueの値が一致していない場合はエラー
            require(keys.length == values.length, "wrong argument number");
        }

        // OracleIDに対してKeyとValueをセットする
        for (uint8 index = 0; index < keys.length; index++) {
            this.set(oracleId, keys[index], values[index]);
        }
    }

    /**
     * @dev OracleValueを取得する。
     */
    function get(uint256 oracleId, bytes32 key)
        external
        view
        override
        returns (bytes32 value, string memory err)
    {
        // 入力値チェック
        if (oracleId == 0) {
            return (0x00, "Oracle invalid value");
        }
        return (oracleValues[oracleId][key], "");
    }

    /**
     * @dev OracleValueを複数件取得する。
     */
    function getBatch(uint256 oracleId, bytes32[] memory keys)
        external
        view
        override
        returns (bytes32[] memory values, string memory err)
    {
        // 入力値チェック
        if (oracleId == 0) {
            values = new bytes32[](0);
            return (values, "Oracle invalid value");
        }

        values = new bytes32[](keys.length);

        for (uint8 index = 0; index < keys.length; index++) {
            bytes32 key = keys[index];
            values[index] = oracleValues[oracleId][key];
        }
        err = "";
    }

    /**
     * @dev コントラクトを更新する場合に備える予備のストレージ領域
     */
    uint256[50] private ______gap;
}
