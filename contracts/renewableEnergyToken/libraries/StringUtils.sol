// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

// String操作用のライブラリ
library StringUtils {
    /**
     * @dev stringをbytes32に変換する
     *
     * @param source 変換対象のstring
     * @return result 変換後のbytes32
     */
    function stringToBytes32(string memory source) internal pure returns (bytes32 result) {
        // stringをbytesに変換
        bytes memory temp = bytes(source);

        // bytesをbytes32にキャスト
        if (temp.length <= 32) {
            assembly {
                result := mload(add(temp, 32))
            }
        } else {
            revert("Input string is too long to convert to bytes32");
        }

        return result;
    }

    /**
     * @dev stringの文字列を配列に変換する
     *
     * @param source 変換対象のstring
     * @param delimiter 区切り文字
     * @return result 変換後の配列
     */
    function slice(string memory source, string memory delimiter)
        internal
        pure
        returns (string[] memory result)
    {
        bytes memory sourceBytes = bytes(source);
        bytes memory delimiterBytes = bytes(delimiter);
        uint256 delimiterLength = delimiterBytes.length;
        uint256 slicesCount = 1;

        // カウントして分割する回数を決定
        for (uint256 i = 0; i <= sourceBytes.length - delimiterLength; i++) {
            bool matching = true;
            for (uint256 j = 0; j < delimiterLength; j++) {
                if (sourceBytes[i + j] != delimiterBytes[j]) {
                    matching = false;
                    break;
                }
            }
            if (matching) {
                slicesCount++;
                i += delimiterLength - 1;
            }
        }

        // 結果を格納するための配列
        result = new string[](slicesCount);
        uint256 lastIndex = 0;
        uint256 resultIndex = 0;

        // 実際に文字列を分割して配列に格納
        for (uint256 i = 0; i <= sourceBytes.length - delimiterLength; i++) {
            bool matching = true;
            for (uint256 j = 0; j < delimiterLength; j++) {
                if (sourceBytes[i + j] != delimiterBytes[j]) {
                    matching = false;
                    break;
                }
            }

            if (matching) {
                result[resultIndex] = substring(source, lastIndex, i);
                resultIndex++;
                lastIndex = i + delimiterLength;
                i += delimiterLength - 1;
            }
        }

        // 最後の部分を追加
        result[resultIndex] = substring(source, lastIndex, sourceBytes.length);

        return result;
    }

    /**
     * @dev 配列内の特定の位置の文字列を取得
     *
     * @param str string
     * @param startIndex  startIndex
     * @param endIndex endIndex
     */
    function substring(
        string memory str,
        uint256 startIndex,
        uint256 endIndex
    ) internal pure returns (string memory) {
        bytes memory strBytes = bytes(str);
        bytes memory result = new bytes(endIndex - startIndex);
        for (uint256 i = startIndex; i < endIndex; i++) {
            result[i - startIndex] = strBytes[i];
        }
        return string(result);
    }
}
