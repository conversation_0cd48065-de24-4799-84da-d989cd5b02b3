// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev Tokenコントラクト
 */
interface IToken {
    /**
     * @dev アカウント検索結果
     */

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev トークンを追加する
     *
     * @param tokenId tokenId
     * @param name Tokenの名前
     * @param symbol symbol
     * @param traceId トレースID
     */
    function addToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId
    ) external;

    /**
     * @dev Tokenを更新する
     *
     * @param tokenId tokenId
     * @param name Tokenの名前
     * @param symbol zoneId
     * @param traceId トレースID
     */
    function modToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId
    ) external;

    /**
     * @dev Tokenのステータスを変更する。
     *
     * @param providerId providerID
     * @param tokenId tokenId
     * @param enabled Tokenの有効性.true:有効,false:無効
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function setTokenEnabled(
        bytes32 providerId,
        bytes32 tokenId,
        bool enabled,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev 送金許可。
     *
     * @param validatorId ownerId
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param amount 送金数量
     * @param traceId トレースID
     */
    function approve(
        bytes32 validatorId,
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount,
        bytes32 traceId
    ) external;

    /**
     * @dev 送金許可設定の取得 Callで呼ばれる時にValidatorの紐付き検証を行う。
     *
     * @param validatorId validatorId
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @return allowance 許容額
     * @return approvedAt 許可日付
     * @return err エラーメッセージ
     */
    function getAllowance(
        bytes32 validatorId,
        bytes32 ownerId,
        bytes32 spenderId
    )
        external
        view
        returns (
            uint256 allowance,
            uint256 approvedAt,
            string memory err
        );

    /**
     * @dev 送金許可設定一覧の取得 Callで呼ばれる時にValidatorの紐付き検証を行う。
     *
     * @param ownerAccountId ownerAccountId
     * @return approvalData 許容額
     * @return totalCount 送金許可数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        bytes32 ownerAccountId,
        uint256 offset,
        uint256 limit
    )
        external
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        );

    /**
     * @dev 発行。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Mintする数量
     * @param traceId トレースID
     */
    function mint(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    /**
     * @dev 償却。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Burnする数量
     * @param traceId トレースID
     */
    function burn(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    /**
     * @dev 償却取り消し。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Burnを取り消す数量
     * @param blockTimestamp 取り消し対象のBurnの日時
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function burnCancel(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 blockTimestamp,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev 単数のTransferを行う。
     *
     * @param sendAccountId sendAccountId
     * @param fromAccountId fromAccountId
     * @param toAccountId toAccountId
     * @param amount 送金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param traceId トレースID
     */
    function transferSingle(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external;

    /**
     * @dev TotalSupplyを増額する。
     *
     * @param amount Mintする数量
     */
    function addTotalSupply(uint256 amount) external;

    /**
     * @dev TotalSupplyを減額する。
     *
     * @param amount Burnする数量
     */
    function subTotalSupply(uint256 amount) external;

    function setTokenAll(
        TokenAll memory token,
        uint256 deadline,
        bytes memory signature
    ) external;

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev TokenId確認用。
     *
     * @param tokenId tokenId
     * @param chkEnabled true:有効性確認を行う,false:有効性確認を行わない
     * @return success true:Tokenが存在し,false:Tokenが存在しない
     * @return err エラーメッセージ
     */
    function hasToken(bytes32 tokenId, bool chkEnabled)
        external
        view
        returns (bool success, string memory err);

    /**
     * @dev 内部保持TokenId確認用。
     *
     * @return success true:Tokenが存在し,false:Tokenが存在しない
     * @return err エラーメッセージ
     */
    function hasTokenState() external view returns (bool success, string memory err);

    /**
     * @dev Token情報の取得。
     *
     * @return tokenId tokenId
     * @return name token名
     * @return symbol symbol
     * @return totalSupply tokenの総供給量
     * @return enabled ture:有効,false:無効
     * @return err エラーメッセージ
     */
    function getToken()
        external
        view
        returns (
            bytes32 tokenId,
            bytes32 name,
            bytes32 symbol,
            uint256 totalSupply,
            bool enabled,
            string memory err
        );

    function getTokenAll() external view returns (TokenAll memory token);

    //付加領域の合計Balanceを算出する
    // TODO getBalanceList関数の修正はBusinessZoneAccountのデータを呼び出すべき箇所となるため、後続のチケットで対応
    // function getBalanceList(bytes32 accountId)
    //     external
    //     view
    //     returns (
    //         uint16[] memory zoneIds,
    //         uint256[] memory balances,
    //         uint256[] memory stateCodes,
    //         uint256 totalBalance,
    //         string memory err
    //     );

    ///////////////////////////////////
    // events
    ///////////////////////////////////

    event AddToken(
        bytes32 indexed tokenId,
        uint16 zoneId,
        string zoneName,
        bool enabled,
        bytes32 traceId
    );

    event ModToken(bytes32 indexed tokenId, bytes32 name, bytes32 symbol, bytes32 traceId);

    event Approval(
        bytes32 validatorId,
        bytes32 indexed ownerId,
        bytes32 spenderId,
        uint256 amount,
        bytes32 traceId
    );

    event Mint(
        uint16 zoneId,
        bytes32 validatorId,
        bytes32 indexed issuerId,
        bytes32 accountId,
        string accountName,
        uint256 amount,
        uint256 balance,
        bytes32 traceId
    );

    event Burn(
        uint16 zoneId,
        bytes32 validatorId,
        bytes32 indexed issuerId,
        bytes32 accountId,
        string accountName,
        uint256 amount,
        uint256 balance,
        bytes32 traceId
    );

    event BurnCancel(
        uint16 zoneId,
        bytes32 validatorId,
        bytes32 indexed issuerId,
        bytes32 accountId,
        string accountName,
        uint256 amount,
        uint256 balance,
        uint256 blockTimestamp,
        bytes32 traceId
    );

    event Transfer(TransferData transferData, bytes32 traceId);

    event SetEnabledToken(bytes32 indexed tokenId, bool enabled, bytes32 traceId);
}
