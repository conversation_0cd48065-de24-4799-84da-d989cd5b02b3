// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev Tokenコントラクト
 */
interface IIBCToken {
    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Voucherを償却する。
     *
     * @param accountId accountId
     * @param amount 償却額
     * @param traceId トレースID
     */
    function redeemVoucher(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    /**
     * @dev Voucherを発行する。
     *
     * @param accountId accountId
     * @param amount 発行額
     * @param traceId トレースID
     */
    function issueVoucher(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    /**
     * @dev 後続のDischarge処理を開始するためのイベントを発行する
     * 
     * @param accountId アカウントID
     * @param fromZoneId ディスチャージ元のゾーンID
     * @param amount ディスチャージ額
     * @param traceId トレースID
     */
    function discharge(
        bytes32 accountId,
        uint16 fromZoneId,
        uint256 amount,
        bytes32 traceId
    ) external;

    /**
     * @dev IBC用 Escrow --> Account。
     *
     * @param sendAccountId sendAccountId
     * @param fromAccountId fromAccountId
     * @param toAccountId toAccountId
     * @param amount 送金額
     * @param traceId トレースID
     */
    function transferFromEscrow(
        uint16 fromZoneId,
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    /**
     * @dev IBC用 Account --> Escrow。
     *
     * @param fromAccountId fromAccountId
     * @param toAccountId toAccountId
     * @param amount 送金額
     * @param traceId トレースID
     */
    function transferToEscrow(
        uint16 toZoneId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    /**
     * @dev IBC用　残高同期処理
     *
     * @param syncBuisinessZoneBlanaceParams  BizZone内送金の残高更新のデータ
     */
    function syncBusinessZoneBalance(
        SyncBuisinessZoneBlanaceParams memory syncBuisinessZoneBlanaceParams
    ) external;

    /**
     * @dev Biz Zone のアカウントの残高を初期化(0)する。（強制償却済みのみ）
     *
     * @param accountId accountId
     */
    function initAccountBalance(bytes32 accountId) external;

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev IBC用 Admin権限チェック。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true:権限あり,false:権限なし
     * @return err エラーメッセージ
     */
    function checkAdminRole(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool has, string memory err);

    ///////////////////////////////////
    // events
    ///////////////////////////////////

    event Transfer(TransferData transferData, bytes32 traceId);

    event IssueVoucher(
        uint16 indexed zoneId,
        bytes32 indexed validatorId,
        bytes32 accountId,
        string accountName,
        uint256 amount,
        uint256 balance,
        bytes32 traceId
    );

    event RedeemVoucher(
        uint16 indexed zoneId,
        bytes32 indexed validatorId,
        bytes32 accountId,
        string accountName,
        uint256 amount,
        uint256 balance,
        bytes32 traceId
    );

    event DischargeRequested(
        bytes32 indexed validatorId,
        bytes32 accountId,
        uint16 fromZoneId,
        uint256 amount,
        bytes32 traceId
    );
}
