// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./RenewableEnergyTokenStruct.sol";
import "./ITransferable.sol";

// TODO: RenewableEnergyToken.solはカスタムコントラクトとして呼び出すため、別フォルダに切り出す(DCPF-15665で対応)

/**
 * @dev RenewableEnergyTokenコントラクト
 */
interface IRenewableEnergyToken is ITransferable {
    ///////////////////////////////////
    // functions
    ///////////////////////////////////
    function mint(
        bytes32 tokenId,
        bytes32 metadataId,
        bytes32 metadataHash,
        bytes32 mintAccountId,
        bytes32 ownerAccountId,
        bool isLocked,
        bytes32 traceId
    ) external;

    function transfer(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 tokenId,
        bytes32 traceId
    ) external;

    function checkTransaction(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 miscValue1,
        string memory miscValue2
    ) external view returns (bool success, string memory err);

    function getTokenList(
        bytes32 validatorId,
        bytes32 accountId,
        uint256 offset,
        uint256 limit,
        string memory sortOrder
    )
        external
        returns (
            RenewableEnergyTokenListData[] memory,
            uint256 totalCount,
            string memory err
        );

    function getToken(bytes32 tokenId)
        external
        returns (RenewableEnergyTokenData memory, string memory err);

    ///////////////////////////////////
    // events
    ///////////////////////////////////

    event MintRNToken(
        bytes32 indexed tokenId,
        bytes32 metadataId,
        bytes32 metadataHash,
        bytes32 mintAccountId,
        bytes32 ownerAccountId,
        bool isLocked,
        bytes32 traceId
    );

    event TransferRNToken(
        bytes32 validatorId,
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 tokenId,
        bytes32 traceId
    );
}
