// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

/**
 * @dev FinancialCheckコントラクト
 */
interface IFinancialCheck {
    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    //Transaction確認用
    function checkTransaction(
        uint16 zoneId,
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        bytes memory accountSignature,
        bytes memory info
    ) external view returns (bool success, string memory err);

    // Exchange用の検証用関数
    function checkExchange(
        bytes32 accountId,
        uint16 fromZoneId,
        uint16 toZoneId,
        uint256 amount
    ) external view returns (bool success, string memory err);

    // Approveの検証用関数
    // function checkApprove(
    //     bytes32 validatorId,
    //     bytes32 ownerId,
    //     bytes32 spenderId,
    //     uint256 amount,
    //     bytes memory accountSignature,
    //     bytes memory info,
    //     uint256 deadline,
    //     bytes memory signature
    // ) external view returns (bool success, string memory err);

    // SyncAccountの検証用関数
    function checkSyncAccount(
        bytes32 validatorId,
        bytes32 accountId,
        uint16 zoneId,
        bytes32 accountStatus,
        bytes memory accountSignature,
        bytes memory info
    ) external view returns (bool success, string memory err);

    // FinZoneのアカウント状態を参照するための関数(BizZoneからFinZoneの状態を参照)
    function checkFinAccountStatus(bytes32 accountId)
        external
        view
        returns (bytes32 accountStatus, string memory err);

    // AccountId、ZoneIdに紐づくBizZoneAccountStatusを取得
    function getBizZoneAccountStatus(bytes32 accountId, uint16 zoneId)
        external
        view
        returns (bytes32 accountStatus, string memory err);
}
