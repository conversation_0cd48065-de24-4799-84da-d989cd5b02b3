// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

/**
 * @dev カスタムコントラクトインターフェース。
 *      カスタムコントラクトは本インターフェイスを踏襲し実装される為、付加領域コントラクトからの呼び出し時に使用する
 */
interface ITransferable {
    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev カスタムコントラクトで行われるTransferである。
     *      付加領域コントラクトからカスタムコントラクトを呼び出す際に共通のインタフェースとして必要。
     * @param sendAccountId SendAccount
     * @param fromAccountId FromAccount
     * @param toAccountId toAccount
     * @param amount 金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param traceId トレースID
     */
    function customTransfer(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external returns (bool result);

    ///////////////////////////////////
    // events
    ///////////////////////////////////

    /**
     * @dev カスタムコントラクトでCustomTransferが起こった際のEvent。
     *      使用は自由である。
     * @param sendAccountId SendAccount
     * @param fromAccountId FromAccount
     * @param toAccountId toAccount
     * @param amount 金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param traceId トレースID
     */
    event CustomTransfer(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string miscValue2,
        bytes32 traceId
    );
}
