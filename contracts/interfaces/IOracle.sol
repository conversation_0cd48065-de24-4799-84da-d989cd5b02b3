// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

/**
 * @dev Oracleインタフェース
 */
interface IOracle {
    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev OracleIdとInvokerをセットする。
     * @param oracleId oracleId
     * @param invoker invoker
     */
    function addOracle(uint256 oracleId, address invoker) external;

    /**
     * @dev OracleIdに紐づく情報とOracleIdを削除する。
     * @param oracleId oracleId
     */
    function deleteOracle(uint256 oracleId) external;

    /**
     * @dev OracleIdに対し、Valueをセットする。
     * @param oracleId oracleId
     * @param key key
     * @param value value
     */
    function set(
        uint256 oracleId,
        bytes32 key,
        bytes32 value
    ) external;

    /**
     * @dev OracleIdに対し、複数のKeyとValueを同時にsetする。
     * @param oracleId oracleId
     * @param keys keys
     * @param values values
     */
    function setBatch(
        uint256 oracleId,
        bytes32[] memory keys,
        bytes32[] memory values
    ) external;

    /**
     * @dev OracleIdに紐付けられたKeyに対してのValueの取得を行う。
     * @param oracleId oracleId
     * @param key key
     */
    function get(uint256 oracleId, bytes32 key)
        external
        view
        returns (bytes32 value, string memory err);

    /**
     * @dev OracleIdに紐付けられた複数のKeyに対してのValueを取得する。
     * @param oracleID oracleID
     * @param keys keys
     */
    function getBatch(uint256 oracleID, bytes32[] memory keys)
        external
        view
        returns (bytes32[] memory values, string memory err);

    ///////////////////////////////////
    // events
    ///////////////////////////////////

    /**
     * @dev AddOracleを実行した際のEvent。
     * @param oracleId oracleId
     * @param invoker invoker
     */
    event AddOracle(
        uint256 indexed oracleId, // OracleId
        address invoker // Invoker
    );

    /**
     * @dev DeleteOracleを実行した際のEvent。
     * @param oracleId oracleId
     */
    event DeleteOracle(
        uint256 indexed oracleId // OracleId
    );

    /**
     * @dev SetOracleValueを実行した際のEvent。
     * @param oracleId oracleId
     * @param key key
     * @param value value
     */
    event SetOracleValue(
        uint256 indexed oracleId, // OracleId
        bytes32 key, // OracleKey
        bytes32 value // OracleValue
    );
}
