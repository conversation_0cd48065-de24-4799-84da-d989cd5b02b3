// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

interface IBalanceSyncBridge {
    ///////////////////////////////////
    // functions
    ///////////////////////////////////
    function syncTransfer(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint16 fromZoneId,
        uint256 amount,
        uint64 timeoutHeight,
        bytes32 traceId
    ) external returns (uint256);
}
