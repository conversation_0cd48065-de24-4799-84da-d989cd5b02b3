// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;
import "./Struct.sol";

/**
 * @dev Validatorインターフェース。
 *      トークンの転送を許可された権限。
 *      ブロックチェーンのvalidatorとは関係が無いことに注意すること。
 *      何かを検証するわけではないが「検証者」とする。
 */
interface IValidator {
    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev
     * 検証者を追加する。
     * 追加後はenabled状態になっている。
     * 署名するEOAはaddValidatorRole()で登録する。
     * ```
     * emit event: AddValidator()
     * ```
     *
     * signature用hash: `keccak256(abi.encode(validatorId, name, deadline))`
     *
     *      <li>signatureがAdmin登録されたEOAのものである
     *      <li>deadlineが期限内
     *      <li>validatorIdが未登録
     *      <li>validatorIdが0以外
     * @param validatorId 検証者ID
     * @param issuerId 発行者ID
     * @param name 検証者名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザーによる署名
     */
    function addValidator(
        bytes32 validatorId,
        bytes32 issuerId,
        bytes32 name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev
     * ValidatorのEOA追加する。
     * 本関数ではEventをEmitするのみでweb3Streamにて共有DLTにEOAを伝播する
     * ```
     * emit event: AddValidatorRole()
     * ```
     *
     * signature用hash: `keccak256(abi.encode(validatorId, validatorEoa, deadline))`
     *
     *      <li>signatureがAdmin登録されたEOAのものである
     *      <li>deadlineが期限内
     *      <li>validatorIdが未登録
     *      <li>validatorIdが0以外
     * @param validatorId ValidatorID
     * @param validatorEoa ValidatorEoa
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザーによる署名
     */
    function addValidatorRole(
        bytes32 validatorId,
        address validatorEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev
     * 検証者の更新。
     * name, issuerIdを指定しない(name=0x00)を指定した場合はその項目の値を更新しない
     * ```
     * emit event: ModValidator()
     * ```
     *
     * signature用hash: `keccak256(abi.encode(validatorId, name, issuerId, deadline))`
     *
     *      <li>signatureがAdmin登録されたEOAのものである
     *      <li>deadlineが期限内
     *      <li>validatorIdが登録済み
     * @param validatorId 検証者ID
     * @param name 検証者名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名,
     */
    function modValidator(
        bytes32 validatorId,
        bytes32 name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev アカウントの名前を更新する。バリデータの権限が必要。
     *
     * ```
     * emit event: ModAccount()
     * ```
     *
     * @param validatorId validatorId
     * @param accountId アカウントID
     * @param accountName アカウント名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature バリデータによる署名
     */
    function modAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev Accountを解約する。
     * @param validatorId validatorId
     * @param accountId accountId
     * @param traceId トレースID
     */
    function setTerminated(
        bytes32 validatorId,
        bytes32 accountId,
        bytes32 reasonCode,
        bytes32 traceId
    ) external;

    /**
     * @dev Accountを登録する(共通領域)。
     * @param validatorId validatorId
     * @param accountId accountId
     * @param limitAmounts 限度額の配列
     * @param traceId トレースID
     */
    function addAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        uint256[] memory limitAmounts,
        bytes32 traceId
    ) external;

    /**
     * @dev バリデータが直接管理するアカウントIDを追加する
     *
     * @param validatorId バリデータID
     * @param accountId アカウントID
     */
    function addValidatorAccountId(
        bytes32 validatorId,
        bytes32 accountId,
        bytes32 traceId
    ) external;

    /**
     * @dev BusinessZoneアカウントを追加する。
     *
     *
     */
    function setActiveBusinessAccountWithZone(
        bytes32 validatorId,
        uint16 zoneId,
        bytes32 accountId,
        bytes32 traceId
    ) external;

    /**
     * @dev BusinessZoneアカウント解約する。
     * @param zoneId zoneId
     * @param accountId accountId
     * @param traceId トレースID
     */
    function setBizZoneTerminated(
        uint16 zoneId,
        bytes32 accountId,
        bytes32 traceId
    ) external;

    /**
     * @dev Account登録（付加領域用)。
     * @param validatorId validatorId
     * @param accountId accountId
     * @param zoneId zoneId
     * @param zoneName zoneName
     * @param accountStatus 口座のステータス
     * @param approvalAmount 承認額
     * @param traceId トレースID
     */
    function syncAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        uint16 zoneId,
        string memory zoneName,
        bytes32 accountStatus,
        bytes32 reasonCode,
        uint256 approvalAmount,
        bytes32 traceId
    ) external;

    function setValidatorAll(
        ValidatorAll memory validator,
        uint256 deadline,
        bytes memory signature
    ) external;

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev (内部用)検証者IDが登録済であるか確認する。
     * @param validatorId 検証者ID
     * @return success true:検証者IDが登録済み, false:検証者IDが登録されていない
     * @return err エラー内容
     *      <li>VALIDATOR_INVALID_VAL:ID値不正
     *      <li>ACCOUNT_ID_NOT_EXIST:未登録
     *      <li>ACCOUNT_DISABLED:無効(chkEnabled=true時)
     */
    function hasValidator(bytes32 validatorId)
        external
        view
        returns (bool success, string memory err);

    /**
     * @dev 検証者情報リストを取得する。
     * @param limit 取得件数
     * @param offset インデックス開始位置、0始まり
     * @return validators 検証者リスト
     * @return totalCount 検証者情報の総件数
     * @return err bytes(err).length!=0の場合、エラー有り(その他の戻り値は無効)
     */
    function getValidatorList(uint256 limit, uint256 offset)
        external
        view
        returns (
            ValidatorListData[] memory validators,
            uint256 totalCount,
            string memory err
        );

    /**
     * @dev 検証者情報を取得する。
     * @param validatorId 検証者ID
     * @return name 検証者名
     * @return issuerId 検証者に関連する発行者ID
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function getValidator(bytes32 validatorId)
        external
        view
        returns (
            bytes32 name,
            bytes32 issuerId,
            string memory err
        );

    /**
     * @dev 検証者数を取得する。
     * @return count 登録されている検証者数(無効検証者を含む)
     */
    function getValidatorCount() external view returns (uint256 count);

    /**
     * @dev ValidatorがAccountと紐付いているか確認を行う。
     * @param validatorId 検証者ID
     * @param accountId   AccountID
     * @return success true:ValidatorがAccountと紐づいている, false:ValidatorがAccountと紐づいていない
     * @return err        エラー内容(success以外の戻り値は無効)
     */
    function hasAccount(bytes32 validatorId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err);

    /**
     * @dev バリデータ権限を持っているか確認する。
     *
     * @param validatorId validatorId
     * @param accountId accountId
     */
    function hasValidatorByAccount(bytes32 validatorId, bytes32 accountId)
        external
        view
        returns (bool success);

    /**
     * @dev Validatorに紐づくAccountの情報を取得する。
     * @param validatorId validatorId
     * @param accountId accountId
     * @return accountData Account情報(zoneIdなし)
     * @return err エラーメッセージ
     */
    function getAccount(bytes32 validatorId, bytes32 accountId)
        external
        view
        returns (AccountDataWithLimitData memory accountData, string memory err);

    /**
     * @dev バリデータが直接管理するアカウントIDを取得する
     *
     * @param validatorId バリデータID
     * @return accountId アカウントID
     * @return err エラー
     */
    function getValidatorAccountId(bytes32 validatorId)
        external
        view
        returns (bytes32 accountId, string memory err);

    /**
     * @dev 検証者IDを取得する。
     * @param index 取得する検証者IDのインデックス。0始まり。
     * @return validatorId 検証者ID
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function getValidatorId(uint256 index)
        external
        view
        returns (bytes32 validatorId, string memory err);

    /**
     * @dev 該当ValidatorIDに紐づくAccountの情報を取得する。
     * @param validatorId validatorId
     * @param offset オフセット
     * @param limit リミット
     * @param sortOrder ソート順(desc: 降順, asc: 昇順)
     * @return accounts 情報を取得できたaccounts
     * @return totalCount 情報を取得できたaccountの数
     * @return err エラーメッセージ
     */
    function getAccountList(
        bytes32 validatorId,
        uint256 offset,
        uint256 limit,
        string memory sortOrder
    )
        external
        view
        returns (
            ValidatorAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        );

    /**
     * @dev 該当Accountに紐づくZoneの情報を取得する。
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return zones zoneIdの配列
     * @return err エラーメッセージ
     */
    function getZoneByAccountId(bytes32 validatorId, bytes32 accountId)
        external
        view
        returns (ZoneData[] memory zones, string memory err);

    /**
     * @dev バリデータ権限を持っているか確認する。
     * @param validatorId validatorId
     * @param hash hash
     * @param deadline deadline
     * @param signature signature
     */
    function hasValidatorRole(
        bytes32 validatorId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err);

    /**
     * @dev バリデータ権限を持っているか確認する。
     * @param index index
     */
    function getValidatorAll(uint256 index) external view returns (ValidatorAll memory validator);

    ///////////////////////////////////
    // events
    ///////////////////////////////////

    /**
     * @dev Validator追加時に出力されるEvent
     * @param validatorId 検証者ID
     * @param issuerId 発行者ID
     * @param name 検証者名
     * @param traceId トレースID
     */
    event AddValidator(
        bytes32 indexed validatorId,
        bytes32 issuerId,
        bytes32 name,
        bytes32 traceId
    );

    /**
     * @dev Validatorに対しRoleを付与した際のEvent
     * @param validatorId 検証者ID
     * @param validatorEoa 検証者EOA
     * @param traceId トレースID
     */
    event AddValidatorRole(bytes32 indexed validatorId, address validatorEoa, bytes32 traceId);

    /**
     * @dev Validatorを有効か無効にした際のEvent
     * @param validatorId 検証者ID
     * @param enabled true=有効化, false=無効化
     * @param traceId トレースID
     */
    event ValidatorEnabled(bytes32 indexed validatorId, bool enabled, bytes32 traceId);

    /**
     * @dev ValidatorのStatusが変更された際のEvent
     * @param validatorId 検証者ID
     * @param name 検証者名
     * @param traceId トレースID
     */
    event ModValidator(bytes32 indexed validatorId, bytes32 name, bytes32 traceId);

    /**
     * @dev Accountを登録する(共通領域)際のEvent
     * @param validatorId validatorId
     * @param accountId accountId
     * @param enabled enabled
     * @param limitAmounts 限度額の配列
     * @param traceId トレースID
     */
    event AddAccount(
        bytes32 indexed validatorId,
        bytes32 accountId, //AccountID
        bool identified, // 本人確認
        bool enabled, // 有効性
        uint256[] limitAmounts, // 限度額
        bytes32 traceId
    );

    event AddValidatorAccountId(bytes32 validatorId, bytes32 accountId, bytes32 traceId);

    /**
     * @dev Account登録（付加領域用)際のEvent
     * @param validatorId validatorId
     * @param accountId accountId
     * @param zoneId zoneId
     * @param zoneName zoneName
     * @param accountStatus 口座のステータス
     * @param approvalAmount 承認額
     * @param traceId トレースID
     */
    event SyncAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string accountName,
        uint16 zoneId,
        string zoneName,
        bytes32 accountStatus,
        uint256 approvalAmount,
        bytes32 traceId
    );

    /**
     * @dev Validatorによるアカウント解約
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param reasonCode 理由コード
     * @param traceId トレースID
     */
    event SetTerminated(
        uint16 indexed zoneId,
        bytes32 accountId,
        bytes32 reasonCode,
        bytes32 traceId
    );

    /**
     * @dev BusinessZoneアカウント解約のEvent
     * @param validatorId 検証者ID
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param traceId トレースID
     */
    event SetBizZoneTerminated(
        bytes32 validatorId,
        uint16 indexed zoneId,
        bytes32 accountId,
        bytes32 traceId
    );
}
