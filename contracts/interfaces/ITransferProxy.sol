// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

/**
 * @dev TransferProxyインターフェース
 */
interface ITransferProxy {
    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 対象のカスタムコントラクトのRule(Address)を設定する
     * @param rule 追加するRule(Address)
     * @param position Rule(Address)を配列に追加する。
     * その位置を配列の先頭からの位置で指定する。
     * 0 は先頭を意味する。
     */
    function addRule(address rule, uint256 position) external;

    /**
     * @dev Rule(Address)が登録されているか確認を行う
     * @param rule 追加するRule(Address)
     * @return result 登録の有無
     */
    function isRegistered(address rule) external view returns (bool result);

    /**
     * @dev Rule(Address)の削除を行う
     * @param rule 削除するRule(Address)
     */
    function deleteRule(address rule) external;

    /**
     *  @dev 全てのRule(Address)の削除を行う
     */
    function clearRule() external;

    /**
     *  @dev Ruleを優先度順に全て取得する
     */
    function findAll() external view returns (address[] memory rules);

    /**
     * @dev カスタムコントラクトのTransferを実行する
     * @param sendAccountId SendAccount
     * @param fromAccountId FromAccount
     * @param toAccountId toAccount
     * @param amount 金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     */
    function customTransfer(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external;

    ///////////////////////////////////
    // events
    ///////////////////////////////////
    /**
     * @dev カスタムコントラクトのRule(Address)が追加された際のEvent
     */
    event AddRule(address rule, uint256 position);
    /**
     * @dev カスタムコントラクトのRule(Address)が削除された際のEvent
     */
    event DeleteRule(address rule);
}
