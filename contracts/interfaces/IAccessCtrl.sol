// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev AccessCtrlインターフェース
 */
interface IAccessCtrl {
    /**
     * @dev 権限アドレス追加(Admin)。Adminの権限が必要。
     * ```
     * emit event: RoleGranted()
     * ```
     *
     * @param eoaNew 権限を付与するEOA
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addAdminRole(
        address eoaNew,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev 権限アドレス追加(Admin以外)。<br/>
     *      Admin以外の権限値はcalcRole()で計算した値を使用し、
     *      各Providerや各Issuerごとに権限値が用意される想定である。<br/>
     *      Adminの権限が必要。
     * ```
     * emit event: RoleGranted()
     * ```
     *
     * @param role 権限
     * @param eoaNew 権限を付与するEOA
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addRole(
        bytes32 role,
        address eoaNew,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev 権限アドレス追加(Admin以外)(Providerコントラクト専用)。<br/>
     *      addRole()参照
     * ```
     * emit event: RoleGranted()
     * ```
     *
     * @param providerId ProviderID
     * @param role 権限
     * @param eoaNew 権限を付与するEOA
     */
    function addRoleByProv(
        bytes32 providerId,
        bytes32 role,
        address eoaNew
    ) external;

    /**
     * @dev 権限アドレス追加(Admin以外)(Issuerコントラクト専用)。<br/>
     *      addRole()参照
     * ```
     * emit event: RoleGranted()
     * ```
     *
     * @param issuerId 発行者ID
     * @param role 権限
     * @param eoaNew 権限を付与するEOA
     */
    function addRoleByIssuer(
        bytes32 issuerId,
        bytes32 role,
        address eoaNew
    ) external;

    /**
     * @dev 権限アドレス追加(Admin以外)(Validatorコントラクト専用)。<br/>
     *      addRole()参照
     * ```
     * emit event: RoleGranted()
     * ```
     *
     * @param role 権限
     * @param eoaNew 権限を付与するEOA
     */
    function addRoleByValidator(
        bytes32 validatorId,
        bytes32 role,
        address eoaNew
    ) external;

    /**
     * @dev AccountのEoaを追加
     */
    function addAccountEoa(bytes32 accountId, address eoa) external;

    /**
     * @dev
     * Admin権限アドレス削除。<br/>
     * msg.senderがコントラクトをデプロイしたアドレスであること。<br/>
     * コントラクトをデプロイしたアドレスは削除できない。
     *
     * ```
     * emit event: RoleRevoked()
     * ```
     *
     * @param eoaDel 権限を削除するEOA
     */
    function delAdminRole(address eoaDel) external;

    /**
     * @dev
     * Provider権限アドレス削除。<br/>
     * msg.senderがコントラクトをデプロイしたアドレスであること。<br/>
     *
     * ```
     * emit event: RoleRevoked()
     * ```
     *
     * @param provID プロバイダID
     * @param eoaDel 権限を削除するEOA
     */
    function delProviderRole(bytes32 provID, address eoaDel) external;

    /**
     * @dev
     * Issuer権限アドレス削除。<br/>
     * msg.senderがコントラクトをデプロイしたアドレスであること。<br/>
     *
     * ```
     * emit event: RoleRevoked()
     * ```
     *
     * @param issuerID 発行者ID
     * @param eoaDel 権限を削除するEOA
     */
    function delIssuerRole(bytes32 issuerID, address eoaDel) external;

    /**
     * @dev
     * Validator権限アドレス削除。<br/>
     * msg.senderがコントラクトをデプロイしたアドレスであること。<br/>
     *
     * ```
     * emit event: RoleRevoked()
     * ```
     *
     * @param validID 検証者ID
     * @param eoaDel 権限を削除するEOA
     */
    function delValidRole(bytes32 validID, address eoaDel) external;

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev 権限値計算。権限(role)として大きくAdmin、Admin以外、無し、の3つのどれかに属する。
     *      Admin以外ではProvider, Issuerがあるが、権限の有効範囲は各Providerや各Issuerに
     *      属するものだけである。
     *      ここでは各Providerや各Issuer用の権限値を作成する。
     *      prefixを用意したのは、idが連番になると値が重複してしまうためである。
     * @param prefix ROLE_PREFIX_xxx()を想定(チェックはしない)
     * @param id 権限を割り振るID
     * @return 権限値
     */
    function calcRole(bytes32 prefix, bytes32 id) external pure returns (bytes32);

    /**
     * @dev 権限チェック(Admin)。署名からEOAを復元し、Admin権限を持つかチェックする
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true:Admin権限あり
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function checkAdminRole(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool has, string memory err);

    /**
     * @dev 権限チェック(Admin以外)。署名からEOAを復元し、対象の権限を持つかチェックする。
     * @param role チェック対象の権限値
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true:該当の権限あり
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function checkRole(
        bytes32 role,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool has, string memory err);

    // アカウントの署名検証
    function checkSigAccount(
        bytes32 accountId,
        bytes32 hashAccount,
        bytes memory accountSignature,
        bytes memory info
    ) external view returns (bool has, string memory err);

    /// @dev ecAddRecover recover an address
    /// @param pox Px of public key O
    /// @param pkoSign compress flag of public key O
    /// @param pcx Px of public key C
    /// @param pkcSign Px compress flag of public key C
    /// @param addr address of PKa.
    function eccAddRecover(
        uint256 pox,
        uint256 pkoSign,
        uint256 pcx,
        uint256 pkcSign
    ) external view returns (address addr);

    /**
     * @dev ProviderIDからEOAを逆引きする。Providerコントラクトのみ実行を許す。
     * @param providerId ProviderId
     * @return providerEoa
     */
    function getProviderEoa(bytes32 providerId) external view returns (address);

    /**
     * @dev IssuerIDからEOAを逆引きする。Issuerコントラクトのみ実行を許す。
     * @param issuerId IssuerId
     * @return issuerEoa
     */
    function getIssuerEoa(bytes32 issuerId) external view returns (address);

    /**
     * @dev ValidatorIDからEOAを逆引きする。Validatorコントラクトのみ実行を許す。
     * @param validatorId ValidatorId
     * @return validatorEoa
     */
    function getValidatorEoa(bytes32 validatorId) external view returns (address);

    /**
     * @dev AccountIDからEOAを逆引きする。Accountコントラクトのみ実行を許す。
     * @param accountId AccountId
     * @return accountEoa
     */
    function getAccountEoa(bytes32 accountId) external view returns (address);

    ///////////////////////////////////
    // for upgrade contracts
    // deprecated
    ///////////////////////////////////

    // uint256[50] private __gap;
}
