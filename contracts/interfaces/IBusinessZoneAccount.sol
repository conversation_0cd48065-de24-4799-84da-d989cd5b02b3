// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev BusinessZoneAccountコントラクト
 */
interface IBusinessZoneAccount {
    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    // BusinessZoneアカウント追加
    function setActiveBusinessAccountWithZone(
        uint16 zoneId,
        bytes32 accountId,
        bytes32 traceId
    ) external;

    // BusinessZoneアカウント解約
    function setBizZoneTerminated(uint16 zoneId, bytes32 accountId) external;

    // ゾーンIDごとにアカウントが存在しているかの確認
    function hasAccountByZone(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err);

    // BusinessZoneアカウント情報取得
    function getBusinessZoneAccount(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (BusinessZoneAccountData memory);

    // zoneIdごとの指定したアカウントのステータスがアクティブであるかどうかを確認する
    function isActivatedByZone(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err);

    // zoneIdごとのアカウントの存在確認
    function accountIdExistenceByZoneId(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool);

    // BusinessZoneアカウント残高更新
    function syncBusinessZoneBalance(
        SyncBuisinessZoneBlanaceParams memory syncBuisinessZoneBlanaceParams
    ) external;

    // BusinessZoneアカウント残高チャージ
    function addBusinessZoneBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external;

    // BusinessZoneアカウント残高ディスチャージ
    function subtractBusinessZoneBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external;

    // BusinessZoneアカウントステータス更新
    function syncBusinessZoneStatus(
        uint16 zoneId,
        string memory zoneName,
        bytes32 accountId,
        string memory accountName,
        bytes32 accountStatus,
        bytes32 traceId
    ) external;

    // BusinessZoneアカウントステータス更新
    function balanceUpdateByRedeemVoucher(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    // BusinessZoneアカウントステータス更新
    function balanceUpdateByIssueVoucher(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external;

    // BusinessZoneの残高を強制償却する
    function forceBurnAllBalance(bytes32 accountId)
        external
        returns (uint256 bizZoneBurnedAmount, ForceDischarge[] memory forceDischarge);

    function getBizAccountsAll(uint256 index)
        external
        view
        returns (BizAccountsAll memory bizAccounts);

    function setBizAccountsAll(
        BizAccountsAll memory bizAccounts,
        uint256 deadline,
        bytes memory signature
    ) external;

    // BusinessZoneアカウントをアクティブにする
    event SetActiveBusinessAccountWithZone(bytes32 accountId, uint256 zoneId, bytes32 traceId);

    // BusinessZoneアカウント残高更新
    event SyncBusinessZoneBalance(TransferData transferData, bytes32 traceId);

    // BusinessZoneアカウントステータス更新
    event SyncBusinessZoneStatus(
        bytes32 validatorId,
        bytes32 accountId,
        uint256 zoneId,
        string zoneName,
        bytes32 accountStatus,
        bytes32 traceId
    );

    // 残高を更新(償却)
    event BalanceUpdateByRedeemVoucher(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    );

    // 残高を更新(発行)
    event BalanceUpdateByIssueVoucher(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    );
}
