// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

///////////////////////////////////
// struct definitions
///////////////////////////////////

/**
 * @dev 再生可能エネルギートークンの詳細情報
 *
 * TODO: RenewableEnergyToken.solはカスタムコントラクトとして呼び出すため、このStructもStruct.solから切り出す(DCPF-15665で対応)
 */
struct RenewableEnergyTokenData {
    /* token状態 */
    TokenStatus tokenStatus;
    /* メタデータID */
    bytes32 metadataId;
    /* メタデータハッシュ値 */
    bytes32 metadataHash;
    /* 発行者のアカウントID */
    bytes32 mintAccountId;
    /* 所有者のアカウントID */
    bytes32 ownerAccountId;
    /* 直前に操作したアカウントID */
    bytes32 previousAccountId;
    /* Tokenのロック状態 */
    bool isLocked;
}

/**
 * @dev 再生可能エネルギートークンの詳細情報(リスト返却用)
 *
 * TODO: RenewableEnergyToken.solはカスタムコントラクトとして呼び出すため、このStructもStruct.solから切り出す(DCPF-15665で対応)
 */
struct RenewableEnergyTokenListData {
    /* tokenID */
    bytes32 tokenId;
    /* token状態 */
    TokenStatus tokenStatus;
    /* メタデータID */
    bytes32 metadataId;
    /* メタデータハッシュ値 */
    bytes32 metadataHash;
    /* 発行者のアカウントID */
    bytes32 mintAccountId;
    /* 所有者のアカウントID */
    bytes32 ownerAccountId;
    /* 直前に操作したアカウントID */
    bytes32 previousAccountId;
    /* Tokenのロック状態 */
    bool isLocked;
}

/**
 * @dev 再生可能エネルギートークンの詳細情報
 *
 * TODO: RenewableEnergyToken.solはカスタムコントラクトとして呼び出すため、このStructもStruct.solから切り出すかどうか検討
 */
struct RenewableEnergyTokenAll {
    /* tokenID */
    bytes32 tokenId;
    /* tokenデータ */
    RenewableEnergyTokenData renewableEnergyTokenData;
}

/**
 * @dev 再生可能エネルギートークンの詳細情報
 *
 * TODO: RenewableEnergyToken.solはカスタムコントラクトとして呼び出すため、このStructもStruct.solから切り出すかどうか検討
 */
struct TokenIdsByAccountIdAll {
    /* accountID */
    bytes32 accountId;
    /* tokenデータ */
    bytes32[] tokenIds;
}

///////////////////////////////////
// enum definitions
///////////////////////////////////

/**
 * @title トークンステータス
 * @dev トークンの現在の状態を表す列挙型
 */
enum TokenStatus {
    Empty, // トークンがまだ存在しない
    Active, // トークンがアクティブで取引可能
    Inactive, // トークンが一時的に非アクティブ
    Cancelled, // トークンがキャンセルまたは無効化
    Frozen, // トークンが凍結されている
    Pending, // トークンが保留中（承認待ちなど）
    Minted, // トークンが新規に発行された
    Burned // トークンが焼却され、市場から削除された
}
