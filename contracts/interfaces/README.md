# dcbg-dcjpy-contract-interfaces

このリポジトリは、各コントラクトリポジトリのスマートコントラクトのインターフェース定義を管理する目的で作られている。各コントラクトのインターフェースは、共通のインターフェースを使用して異なるプロジェクト間での互換性と再利用を可能にする。

## Purpose

このリポジトリの主な目的は以下の通り：

- **統一性の確保:** 全ての関連するプロジェクトで一貫したインターフェースを使用し、開発の効率化とエラーの削減を目指す。
- **再利用性の向上:** 標準化されたインターフェースを提供し、異なるプロジェクトやモジュール間でのコードの再利用を容易にする。
- **互換性の保持:** 異なるコントラクト間での通信をスムーズに行うため、互換性のあるインターフェースを維持する。

## How to Use the Interfaces

### Adding as a Submodule

他のプロジェクトにこのインターフェースリポジトリをサブモジュールとして組み込む場合、以下のコマンドを実行する

```bash
git submodule add https://github.com/decurret-lab/dcbg-dcjpy-contract-interfaces.git path/to/your/project/contracts/interfaces
git submodule init
git submodule update
```

### Referencing Interfaces

プロジェクト内のスマートコントラクトからこれらのインターフェースを参照するには、適切なインポート文をファイルに含める：

```contract
import "./contracts/interfaces/IYourInterface.sol";
```

## Pull Requests

変更を提案する前に、各コントラクトリポジトリへの影響把握と修正内容及び反映タイミングについて議論の場を設ける必要がある。クリアなコミュニケーションを通じて、コンフリクトが発生しないように心がけること。
