// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/ITransferable.sol";
import "./interfaces/Error.sol";
import "./Validator.sol";
import "./libraries/TokenLib.sol";
import "./interfaces/Struct.sol";

contract FinancialCheck is Initializable, IFinancialCheck {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /* @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /* @dev issueVoucherの計算パターン */
    uint256 private constant CALC_ISSUE = 1;
    /* @dev redeemVoucherの計算パターン */
    uint256 private constant CALC_REDEEM = 2;
    /* @dev 未登録の場合にて返す空の値 */
    bytes32 private constant _EMPTY_VALUE = 0x00;
    /* @dev AccountStatus判定用の定数(口座申し込み) */
    bytes32 private constant _STATUS_APPLIYNG = "applying";
    /* @dev AccountStatus判定用の定数(口座解約申し込み) */
    bytes32 private constant _STATUS_TERMINATING = "terminating";
    /* @dev 累積限度額の加算を行わない */
    bool private constant NOT_ADD_CUMULATIVE = false;
    /* @dev 累積限度額の加算を行う */
    bool private constant ADD_CUMULATIVE = true;
    /** @dev 口座の開設状況が初期化済みであることを表す固定値 **/
    uint256 private constant STATE_CODE_INITIALIZED = 0;
    /* @dev FinZoneのID */
    uint16 private constant _FINANCIAL_ZONE = 3000;
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_TRANSFER = "transfer";
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_SYNC = "synchronous";
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_EXCHANGE = "exchange";
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _STRING_APPROVE = "approve";
    /** @dev 外部署名の検証に使用する固定値 **/
    bytes32 private constant _EMPTY_BYTES65_HASH = keccak256(abi.encodePacked(new bytes(65)));
    bytes32 private constant _EMPTY_BYTES512_HASH = keccak256(abi.encodePacked(new bytes(512)));

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     * @param contractManager ContractManagerアドレス
     */
    function initialize(IContractManager contractManager) public initializer {
        _contractManager = contractManager;
    }

    /**
     * @dev コントラクトバージョン取得。
     * @return version コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @notice FinancialZoneトランザクションをチェックする
     * @dev この関数はFinDLT上で実行されるためBizDZone実行時には参照先を意識して呼び出す必要がある
     * @param zoneId 処理ゾーンのID
     * @param sendAccountId 送信元アカウントID
     * @param fromAccountId 送金元アカウントID
     * @param toAccountId 送金先アカウントID
     * @param amount 送金額
     * @param accountSignature アカウント署名
     * @param info 追加情報
     * @return success トランザクションが成功したかどうか
     * @return err エラーメッセージ
     */
    function checkTransaction(
        uint16 zoneId,
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        bytes memory accountSignature,
        bytes memory info
    ) external view returns (bool success, string memory err) {
        // 共通領域でのSendAccountの有効性を確認する
        {
            // SendAccount有効性確認
            (success, err) = _contractManager.account().isActivated(sendAccountId);
            if (!success) {
                return (success, err);
            }
        }
        // 共通領域でのFromAccountの有効性を確認する
        {
            // FromAccount有効性確認
            (success, err) = _contractManager.account().isActivated(fromAccountId);
            if (!success) {
                return (success, err);
            }
        }
        // 共通領域でのToAccountの有効性を確認する
        {
            // ToAccount有効性確認
            (success, err) = _contractManager.account().isActivated(toAccountId);
            if (!success) {
                return (success, err);
            }
        }
        {
            // fromAccountIdとtoAccountIdが同じの場合にエラー
            if (fromAccountId == toAccountId) {
                return (false, Error.FROM_TO_SAME);
            }
            // accountSignatureとsigInfoが共に0でない場合のみアカウント署名検証を行う　// TODO: 恒久対応として_sigVeruifyを削除する
            if (
                keccak256(accountSignature) != _EMPTY_BYTES65_HASH &&
                keccak256(info) != _EMPTY_BYTES512_HASH
            ) {
                // Accountの署名検証に使用するHashを生成する
                bytes32 hashAccount = keccak256(
                    abi.encode(sendAccountId, fromAccountId, toAccountId, amount, _STRING_TRANSFER)
                );
                (success, err) = _contractManager.accessCtrl().checkSigAccount(
                    sendAccountId,
                    hashAccount,
                    accountSignature,
                    info
                );
                if (!success) {
                    return (success, err);
                }
            }
        }
        {
            // FinZoneからのリクエストである場合、FinZoneの送金元アカウントの残高確認を行う
            // BizZoneからのリクエストである場合、BizZoneでの各アカウント有効性の確認、残高チェックを行う
            if (zoneId == _FINANCIAL_ZONE) {
                (AccountDataWithoutZoneId memory data, ) = _contractManager.account().getAccount(
                    fromAccountId
                );
                if (data.balance < amount) {
                    return (false, Error.BALANCE_NOT_ENOUGH);
                }
            } else {
                // BizZoneでのSendAccountの有効性確認
                (success, err) = _contractManager.businessZoneAccount().isActivatedByZone(
                    zoneId,
                    sendAccountId
                );
                if (!success) {
                    return (success, err);
                }
                // BizZoneでのFromAccountの有効性確認
                (success, err) = _contractManager.businessZoneAccount().isActivatedByZone(
                    zoneId,
                    fromAccountId
                );
                if (!success) {
                    return (success, err);
                }
                // BizZoneでのToAccountの有効性確認
                (success, err) = _contractManager.businessZoneAccount().isActivatedByZone(
                    zoneId,
                    toAccountId
                );
                if (!success) {
                    return (success, err);
                }
                // BizZoneでのFromAccountの残高確認
                BusinessZoneAccountData memory data = _contractManager
                    .businessZoneAccount()
                    .getBusinessZoneAccount(zoneId, fromAccountId);
                if (data.balance < amount) {
                    return (false, Error.BALANCE_NOT_ENOUGH);
                }
            }
        }
        {
            // TransferLimitとDailyLimitを確認する
            (success, err) = _contractManager.financialZoneAccount().checkTransfer(
                fromAccountId,
                amount
            );
            if (!success) {
                return (success, err);
            }
        }
        {
            // miscValue2のサイズが4KB未満であることを確認する
            if (bytes(miscValue2).length > 4096){
                return (false, Error.TOKEN_INVALID_VAL);
            }
        }
        return (true, "");
    }

    // Exchange用の検証用関数
    function checkExchange(
        bytes32 accountId,
        uint16 fromZoneId,
        uint16 toZoneId,
        uint256 amount
    ) external view returns (bool success, string memory err) {
        if (fromZoneId == toZoneId) {
            return (false, Error.ACCOUNT_INVALID_VAL);
        }

        BusinessZoneAccountData memory data;
        // FromZoneが"Financial_zone"ではない場合はbalanceByZoneの残高とステータス確認を行う
        if (fromZoneId != _FINANCIAL_ZONE) {
            // fromZoneIdでアカウント情報を取得する
            data = _contractManager.businessZoneAccount().getBusinessZoneAccount(
                fromZoneId,
                accountId
            );
            if (data.accountStatus != Constant._STATUS_ACTIVE) {
                return (false, Error.ACCOUNT_STATUS_INVALID);
            }
            if (data.balance < amount) {
                return (false, Error.BALANCE_NOT_ENOUGH);
            }
            // FromZoneが　"Fonancial_zone"の場合はbalanceByZoneのステータス確認を行う
        } else {
            // toZoneIdでアカウント情報を取得する
            data = _contractManager.businessZoneAccount().getBusinessZoneAccount(
                toZoneId,
                accountId
            );
            if (data.accountStatus != Constant._STATUS_ACTIVE) {
                return (false, Error.ACCOUNT_STATUS_INVALID);
            }
        }

        // ExchangeLimitとDailyLimitを確認する
        return _contractManager.financialZoneAccount().checkCharge(accountId, amount);
    }

    // SyncAccountの検証用関数
    function checkSyncAccount(
        bytes32 validatorId,
        bytes32 accountId,
        uint16 zoneId,
        bytes32 accountStatus,
        bytes memory accountSignature,
        bytes memory info
    ) external view override returns (bool success, string memory err) {
        // Validator未入力チェック
        if (validatorId == _EMPTY_VALUE) {
            return (false, Error.VALIDATOR_INVALID_VAL);
        }
        // アカウントステータスの入力チェック
        if (accountStatus != _STATUS_APPLIYNG && accountStatus != _STATUS_TERMINATING) {
            return (false, Error.ACCOUNT_INVALID_VAL);
        }

        // Account有効性確認
        (success, err) = _contractManager.account().hasAccount(accountId);
        if (success == false) {
            return (false, err);
        }

        // inputするアカウントステータスがterminatingの場合はbusiness zoneのアカウント存在チェック
        if (accountStatus == Constant._STATUS_TERMINATING) {
            // BizAccount存在確認
            (success, err) = _contractManager.businessZoneAccount().hasAccountByZone(
                zoneId,
                accountId
            );
            if (!success) {
                return (false, err);
            }

            // BizAccount状態確認
            BusinessZoneAccountData memory data = _contractManager
                .businessZoneAccount()
                .getBusinessZoneAccount(zoneId, accountId);
            if (
                data.accountStatus == Constant._STATUS_TERMINATING ||
                data.accountStatus == Constant._STATUS_TERMINATED
            ) {
                return (false, Error.ACCOUNT_TERMINATING_OR_TERINATED);
            }
        } else if (accountStatus == Constant._STATUS_APPLIYNG) {
            // BizAccount未存在確認
            (success, err) = _contractManager.businessZoneAccount().hasAccountByZone(
                zoneId,
                accountId
            );
            if (success) {
                // 存在する場合、BizAccount状態強制償却済みであることを確認
                BusinessZoneAccountData memory data = _contractManager
                    .businessZoneAccount()
                    .getBusinessZoneAccount(zoneId, accountId);
                if (data.accountStatus != Constant._STATUS_FORCE_BURNED) {
                    return (false, Error.ACCOUNT_NOT_FORCE_BURNED);
                }
            }
        }

        // Accountの署名検証に使用するHashを生成する
        bytes32 hashAccount = keccak256(abi.encode(accountId, _STRING_SYNC));
        // Accountの署名を検証する
        (success, err) = _contractManager.accessCtrl().checkSigAccount(
            accountId,
            hashAccount,
            accountSignature,
            info
        );
        if (success == false) {
            return (false, err);
        }
        return (true, "");
    }

    // FinZoneのアカウント状態を参照するための関数(BizZoneからFinZoneの状態を参照)
    function checkFinAccountStatus(bytes32 accountId)
        external
        view
        override
        returns (bytes32 accountStatus, string memory err)
    {
        AccountDataWithoutZoneId memory accountData;
        (accountData, err) = _contractManager.account().getAccount(accountId);

        return (accountData.accountStatus, err);
    }

    /**
     * @dev BizZone側からFinZoneの限度額情報を参照するための関数(bizZoneのBCClientからのみ呼び出される)
     *
     * @param accountId accountId
     * @return accountLimitData accountLimitData
     *
     */
    function getAccountLimit(bytes32 accountId)
        external
        view
        returns (FinancialZoneAccountData memory accountLimitData, string memory err)
    {
        (bool success, string memory errAcc) = _contractManager.account().hasAccount(accountId);
        if (!success) {
            return (FinancialZoneAccountData(0, 0, 0, 0, 0, 0, 0), errAcc);
        }
        return _contractManager.financialZoneAccount().getAccountLimitData(accountId);
    }

    /**
     * @dev BizZone側からFinZoneのアカウントステータスを参照するための関数(bizZoneのBCClientからのみ呼び出される)
     *
     * @param accountId accountId
     * @param zoneId accountId
     * @return accountStatus accountStatus
     *
     */
    function getBizZoneAccountStatus(bytes32 accountId, uint16 zoneId)
        external
        view
        returns (bytes32 accountStatus, string memory err)
    {
        if (zoneId == 0x00) {
            return (Constant._EMPTY_VALUE, Error.ACCOUNT_INVALID_VAL);
        }
        if (accountId == 0x00) {
            return (Constant._EMPTY_VALUE, Error.ACCOUNT_INVALID_VAL);
        }
        if (!_contractManager.businessZoneAccount().accountIdExistenceByZoneId(zoneId, accountId)) {
            return (Constant._EMPTY_VALUE, Error.ACCOUNT_ID_NOT_EXIST);
        }

        bool success;
        (success, err) = _contractManager.businessZoneAccount().hasAccountByZone(zoneId, accountId);
        if (!success) {
            return (Constant._EMPTY_VALUE, err);
        }

        BusinessZoneAccountData memory businessZoneAccountData = _contractManager
            .businessZoneAccount()
            .getBusinessZoneAccount(zoneId, accountId);
        return (businessZoneAccountData.accountStatus, "");
    }
}
