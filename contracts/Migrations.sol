// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

contract Migrations {
    address public owner = msg.sender;
    uint256 public lastCompletedMigration;

    modifier restricted() {
        require(msg.sender == owner, "restricted to owner");
        _;
    }

    /**
     * @dev Migrationの完了時間を最新の値に更新する。
     *
     * @param completed Migrationの完了時間
     */
    function setCompleted(uint256 completed) public restricted {
        lastCompletedMigration = completed;
    }
}
