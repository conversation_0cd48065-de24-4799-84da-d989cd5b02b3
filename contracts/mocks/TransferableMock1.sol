// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "../interfaces/ITransferable.sol";

contract TransferableMock1 is Initializable, ITransferable {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /* @dev ルールがない際にデフォルトで実行されるTokenである */
    ITransferable private _token;

    ///////////////////////////////////
    // test function
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     */
    function initialize(ITransferable token) public initializer {
        _token = token;
    }

    function customTransfer(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external override returns (bool result) {
        if (miscValue1 == bytes32("DeCurret1")) {
            // amountが10以上であればamountを10とする
            if (amount >= 10) {
                amount = 10;
                _token.customTransfer(
                    sendAccountId,
                    fromAccountId,
                    toAccountId,
                    amount,
                    miscValue1,
                    miscValue2,
                    memo,
                    traceId
                );
            } else {
                _token.customTransfer(
                    sendAccountId,
                    fromAccountId,
                    toAccountId,
                    amount,
                    miscValue1,
                    miscValue2,
                    memo,
                    traceId
                );
            }
            emit CustomTransfer(
                sendAccountId,
                fromAccountId,
                toAccountId,
                amount,
                miscValue1,
                miscValue2,
                traceId
            );
            return true;
        } else {
            return false;
        }
    }
}
