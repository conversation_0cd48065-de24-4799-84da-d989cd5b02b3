// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import {Height} from "../../contracts/yuiContracts/proto/Client.sol";
import {Packet} from "../../contracts/yuiContracts/proto/Channel.sol";

import {AccountSyncBridge} from "../../contracts/AccountSyncBridge.sol";
import {BalanceSyncBridge} from "../../contracts/BalanceSyncBridge.sol";
import {JPYTokenTransferBridge} from "../../contracts/JPYTokenTransferBridge.sol";

import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

contract IBCHandlerMock is Initializable {
    function sendPacket(
        string calldata sourcePort,
        string calldata sourceChannel,
        Height.Data calldata destHeight,
        uint64 timeoutHeight,
        bytes calldata packetData
    ) external returns (uint64) {

        emit SendPacket(
            sourcePort,
            sourceChannel,
            destHeight,
            timeoutHeight,
            packetData
        );
        return 1;
    }

    function recvPacket(
        address bridgeAddr,
        Packet.Data memory packet,
        address addr
    ) external returns (bytes memory) {
        // TODO: reference the port name. have to change this code if the port string is changed.
        if (keccak256(bytes(packet.source_port)) == keccak256(bytes("account-sync"))) {
            AccountSyncBridge bridge = AccountSyncBridge(bridgeAddr);
            return bridge.onRecvPacket(packet, addr);
        }
        if (keccak256(bytes(packet.source_port)) == keccak256(bytes("balance-sync"))) {
            BalanceSyncBridge bridge = BalanceSyncBridge(bridgeAddr);
            return bridge.onRecvPacket(packet, addr);
        }
        if (keccak256(bytes(packet.source_port)) == keccak256(bytes("token-transfer"))) {
            JPYTokenTransferBridge bridge = JPYTokenTransferBridge(bridgeAddr);
            return bridge.onRecvPacket(packet, addr);
        }
        revert("no bridge contract");
    }

    function acknowledgementPacket(
        address bridgeAddr,
        Packet.Data memory packet,
        bytes calldata acknowledgement,
        address addr
    ) external {
        // TODO: reference the port name. have to change this code if the port string is changed.
        if (keccak256(bytes(packet.source_port)) == keccak256(bytes("account-sync"))) {
            AccountSyncBridge bridge = AccountSyncBridge(bridgeAddr);
            return bridge.onAcknowledgementPacket(packet, acknowledgement, addr);
        }
        if (keccak256(bytes(packet.source_port)) == keccak256(bytes("balance-sync"))) {
            BalanceSyncBridge bridge = BalanceSyncBridge(bridgeAddr);
            return bridge.onAcknowledgementPacket(packet, acknowledgement, addr);
        }
        if (keccak256(bytes(packet.source_port)) == keccak256(bytes("token-transfer"))) {
            JPYTokenTransferBridge bridge = JPYTokenTransferBridge(bridgeAddr);
            return bridge.onAcknowledgementPacket(packet, acknowledgement, addr);
        }
        revert("no bridge contract");
    }

    function timeoutPacket(
        address bridgeAddr,
        Packet.Data memory packet,
        address addr
    ) external {
        // TODO: reference the port name. have to change this code if the port string is changed.
        if (keccak256(bytes(packet.source_port)) == keccak256(bytes("account-sync"))) {
            AccountSyncBridge bridge = AccountSyncBridge(bridgeAddr);
            return bridge.onTimeoutPacket(packet, addr);
        }
        if (keccak256(bytes(packet.source_port)) == keccak256(bytes("balance-sync"))) {
            BalanceSyncBridge bridge = BalanceSyncBridge(bridgeAddr);
            return bridge.onTimeoutPacket(packet, addr);
        }
        if (keccak256(bytes(packet.source_port)) == keccak256(bytes("token-transfer"))) {
            JPYTokenTransferBridge bridge = JPYTokenTransferBridge(bridgeAddr);
            return bridge.onTimeoutPacket(packet, addr);
        }
        revert("no bridge contract");
    }

    event SendPacket(
        string sourcePort,
        string destinationPort,
        Height.Data height,
        uint64 timeoutHeight,
        bytes data
    );
}
