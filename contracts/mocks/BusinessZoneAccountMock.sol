// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "../interfaces/IBusinessZoneAccount.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

contract BusinessZoneAccountMock is IBusinessZoneAccount {
    /** @dev BusinessZoneAccountData */
    struct MockBusiessZoneAccountData {
        bytes32 accountStatus;
    }

    /** @dev BusinessZoneAccountData */
    mapping(uint16 => mapping(bytes32 => MockBusiessZoneAccountData))
        private _businessZoneAccountData;

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    function setActiveBusinessAccountWithZone(
        uint16,
        bytes32,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function setBizZoneTerminated(uint16, bytes32) external pure override {
        revert("unused override only");
    }

    function syncBusinessZoneStatus(
        uint16 fromZoneId,
        string memory,
        bytes32 accountId,
        string memory,
        bytes32 accountStatus,
        bytes32
    ) external override {
        _businessZoneAccountData[fromZoneId][accountId].accountStatus = accountStatus;
    }

    function syncBusinessZoneBalance(SyncBuisinessZoneBlanaceParams memory) external pure override {
        revert("unused override only");
    }

    function addBusinessZoneBalance(
        uint16,
        bytes32,
        uint256
    ) external pure override {
        revert("unused override only");
    }

    function subtractBusinessZoneBalance(
        uint16,
        bytes32,
        uint256
    ) external pure override {
        revert("unused override only");
    }

    function forceBurnAllBalance(bytes32)
        external
        pure
        override
        returns (uint256, ForceDischarge[] memory)
    {
        revert("unused override only");
    }

    function balanceUpdateByRedeemVoucher(
        uint16,
        bytes32,
        uint256,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function balanceUpdateByIssueVoucher(
        uint16,
        bytes32,
        uint256,
        bytes32
    ) external pure override {
        revert("unused override only");
    }

    function setBizAccountsAll(
        BizAccountsAll[] memory,
        uint256,
        bytes memory
    ) external pure {
        revert("unused override only");
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    function hasAccountByZone(uint16, bytes32)
        external
        pure
        override
        returns (bool, string memory)
    {
        revert("unused override only");
    }

    function getBusinessZoneAccount(uint16, bytes32)
        external
        pure
        override
        returns (BusinessZoneAccountData memory)
    {
        revert("unused override only");
    }

    function isActivatedByZone(uint16, bytes32)
        external
        pure
        override
        returns (bool, string memory)
    {
        revert("unused override only");
    }

    function accountIdExistenceByZoneId(uint16, bytes32) external pure override returns (bool) {
        revert("unused override only");
    }

    ///////////////////////////////////
    // Mock functions
    ///////////////////////////////////

    // テスト用
    function getBizZoneAccount(uint16 fromZoneId, bytes32 accountId)
        external
        view
        returns (bytes32)
    {
        return _businessZoneAccountData[fromZoneId][accountId].accountStatus;
    }

    function getBizAccountsAll(uint256) external pure override returns (BizAccountsAll memory) {
        revert("unused override only");
    }

    function setBizAccountsAll(
        BizAccountsAll memory,
        uint256,
        bytes memory
    ) external override {
        revert("unused override only");
    }
}
