// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/cryptography/ECDSAUpgradeable.sol";

import "../interfaces/IAccessCtrl.sol";
import "../interfaces/IContractManager.sol";
import "../interfaces/Error.sol";

/**
 * @dev AccessCtrlコントラクト
 */
contract AccessCtrlMock is IAccessCtrl, AccessControlUpgradeable {
    ///////////////////////////////////
    // private const variables
    ///////////////////////////////////

    /// @dev Adminロール値
    bytes32 private constant _ROLE_ADMIN = keccak256("ADMIN_ROLE");

    ///////////////////////////////////
    // Mock send functions
    ///////////////////////////////////

    // 権限アドレス追加(Admin)。Adminの権限が必要。
    function addAdminRole(
        address eoaNew,
        uint256,
        bytes memory
    ) external override {
        _setupRole(_ROLE_ADMIN, eoaNew);
    }

    // 権限アドレス追加(Admin以外)。
    function addRole(
        bytes32,
        address,
        uint256,
        bytes memory
    ) external pure override {
        revert("unused override only");
    }

    // 権限アドレス追加(Admin以外)(Providerコントラクト専用)。
    function addRoleByProv(
        bytes32,
        bytes32,
        address
    ) external pure override {
        revert("unused override only");
    }

    // 権限アドレス追加(Admin以外)(Issuerコントラクト専用)。
    function addRoleByIssuer(
        bytes32,
        bytes32,
        address
    ) external pure override {
        revert("unused override only");
    }

    // 権限アドレス追加(Admin以外)(Validatorコントラクト専用)。
    function addRoleByValidator(
        bytes32,
        bytes32,
        address
    ) external pure override {
        revert("unused override only");
    }

    // AccountのEoaを追加。
    function addAccountEoa(bytes32, address) external pure override {
        revert("unused override only");
    }

    // Admin権限アドレス削除。
    function delAdminRole(address) external pure override {
        revert("unused override only");
    }

    // Provider権限アドレス削除。
    function delProviderRole(bytes32, address) external pure override {
        revert("unused override only");
    }

    // Issuer権限アドレス削除。
    function delIssuerRole(bytes32, address) external pure override {
        revert("unused override only");
    }

    // Validator権限アドレス削除。
    function delValidRole(bytes32, address) external pure override {
        revert("unused override only");
    }

    ///////////////////////////////////
    // Mock call functions
    ///////////////////////////////////

    // 権限値計算。
    function calcRole(bytes32, bytes32) public pure returns (bytes32) {
        revert("unused override only");
    }

    /**
     * @dev 権限チェック(Admin)。署名からEOAを復元し、Admin権限を持つかチェックする
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true:Admin権限あり
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function checkAdminRole(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool has, string memory err) {
        return _checkRole(_ROLE_ADMIN, hash, deadline, signature);
    }

    /**
     * @dev 権限チェック(Admin以外)。署名からEOAを復元し、対象の権限を持つかチェックする。
     * @param role チェック対象の権限値
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true:該当の権限あり
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function checkRole(
        bytes32 role,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool has, string memory err) {
        return _checkRole(role, hash, deadline, signature);
    }

    /**
     * @dev 権限チェック(内部用)。
     * @param role チェック対象の権限値
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true:該当の権限あり
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function _checkRole(
        bytes32 role,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view returns (bool has, string memory err) {
        if (role == 0) {
            // 主に未登録の場合
            return (false, Error.ACTRL_NOT_ROLE);
        }

        // solhint-disable not-rely-on-time
        if (deadline < block.timestamp) {
            return (false, Error.ACTRL_SIG_TIMEOUT);
        }
        // solhint-enable not-rely-on-time

        address addr = _recoverAddr(hash, signature);
        if (addr == address(0)) {
            return (false, Error.ACTRL_BAD_SIG);
        }
        has = hasRole(role, addr);
    }

    // 署名からのアドレス復元(内部用)
    function _recoverAddr(bytes32 hash, bytes memory signature) internal pure returns (address) {
        if (signature.length != 65) {
            return address(0);
        }
        bytes32 msgHash = ECDSAUpgradeable.toEthSignedMessageHash(hash);
        return ECDSAUpgradeable.recover(msgHash, signature);
    }

    function _pubRecover(uint256, uint256) internal pure returns (address) {
        revert("unused override only");
    }

    // Validatorの証明書検証
    function _checkValidatorSig_deep(
        bytes32,
        uint256,
        bytes memory,
        bytes memory
    ) internal pure returns (bool, string memory) {
        revert("unused override only");
    }

    // アカウントの署名検証
    function checkSigAccount(
        bytes32,
        bytes32,
        bytes memory,
        bytes memory
    ) external pure override returns (bool, string memory) {
        revert("unused override only");
    }

    // ecAddRecover recover an address
    function eccAddRecover(
        uint256,
        uint256,
        uint256,
        uint256
    ) external pure override returns (address) {
        revert("unused override only");
    }

    function addPoint(uint256[2] memory, uint256[2] memory)
        internal
        pure
        returns (uint256, uint256)
    {
        revert("unused override only");
    }

    // Uint256に変換を行う
    function toUint256(bytes memory, uint256) internal pure returns (uint256) {
        revert("unused override only");
    }

    // AccountのOneTime検証用関数
    function _checkAccountOneTime_deep(
        bytes32,
        bytes memory,
        bytes memory
    ) internal pure returns (bool, string memory) {
        revert("unused override only");
    }

    // s, v検証用関数
    function _sigVerify(bytes memory) internal pure returns (string memory) {
        revert("unused override only");
    }

    // AccountのSignatureを検証する
    function _checkAccountSig_deep(
        bytes memory,
        bytes32,
        bytes memory
    ) internal pure returns (bool, string memory) {
        revert("unused override only");
    }

    // ProviderIDからEOAを逆引きする。Providerコントラクトのみ実行を許す。
    function getProviderEoa(bytes32) public pure returns (address) {
        revert("unused override only");
    }

    // IssuerIDからEOAを逆引きする。Issuerコントラクトのみ実行を許す。
    function getIssuerEoa(bytes32) public pure returns (address) {
        revert("unused override only");
    }

    // ValidatorIDからEOAを逆引きする。Validatorコントラクトのみ実行を許す。
    function getValidatorEoa(bytes32) public pure returns (address) {
        revert("unused override only");
    }

    // AccountIDからEOAを逆引きする。Accountコントラクトのみ実行を許す。
    function getAccountEoa(bytes32) public pure returns (address) {
        revert("unused override only");
    }

    ///////////////////////////////////
    // for upgrade contracts
    // deprecated
    ///////////////////////////////////

    // uint256[50] private __gap;
}
