/**
 * @title Curve
 *
 * Interface for elliptic curves and related crypto primitives.
 *
 * <AUTHOR> (<EMAIL>)
 * @see <a href=https://github.com/androlo/standard-contracts/blob/master/contracts/src/crypto/Curve.sol>Curve</a>
 * @changed pragma version
 */
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

interface Curve {
    /// @dev Check whether the input point is on the curve.
    /// SEC 1: *******
    /// @param P The point.
    /// @return True if the point is on the curve.
    function onCurve(uint256[2] memory P) external view returns (bool);

    /// @dev Check if the given point is a valid public key.
    /// SEC 1: *******
    /// @param P The point.
    /// @return True if the point is valid public key
    function isPubKey(uint256[2] memory P) external view returns (bool);

    /// @dev Validate the signature 'rs' of 'h = H(message)' against the public key Q.
    /// SEC 1: 4.1.4
    /// @param h The hash of the message.
    /// @param rs The signature (r, s)
    /// @param Q The public key to validate against.
    /// @return True if the given signature is a valid signature.
    function validateSignature(
        bytes32 h,
        uint256[2] memory rs,
        uint256[2] memory Q
    ) external view returns (bool);

    /// @dev compress a point 'P = (Px, Py)' on the curve, giving 'C(P) = (yBit, Px)'
    /// SEC 1: 2.3.3 - but only the curve-dependent code.
    /// @param P The point.
    function compress(uint256[2] memory P) external view returns (uint8 yBit, uint256 x);

    /// @dev decompress a point 'Px', giving 'Py' for 'P = (Px, Py)'
    /// 'yBit' is 1 if 'Qy' is odd, otherwise 0.
    /// SEC 1: 2.3.4 - but only the curve-dependent code.
    /// @param yBit The compressed y-coordinate (One bit)
    /// @param Px The x-coordinate.
    function decompress(uint8 yBit, uint256 Px) external view returns (uint256[2] memory Q);

    // /// @dev ecAddRecover recover an address
    // /// @param pox Px of public key O
    // /// @param pcx Px of public key C
    // /// @param addr address of PKa.
    // function ecAddRecover(uint  pox, uint pcx) external view returns(address addr);
}
