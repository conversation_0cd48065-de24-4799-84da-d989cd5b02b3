/**
 * @title ECCMath
 *
 * Functions for working with integers, curve-points, etc.
 *
 * <AUTHOR> (<EMAIL>)
 * @see <a href=https://github.com/androlo/standard-contracts/blob/master/contracts/src/crypto/ECCMath.sol>ECCMath</a>
 * @see <a href=https://github.com/witnet/elliptic-curve-solidity/blob/master/contracts/EllipticCurve.sol>invmod</a>
 * @changed pragma version
 * @changed invmod function
 */
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

library ECCMath {
    /* solhint-disable no-inline-assembly */
    /// @dev Modular euclidean inverse of a number (mod p).
    /// @param _x The number
    /// @param _pp The modulus
    /// @return q such that x*q = 1 (mod _pp)
    function invmod(uint256 _x, uint256 _pp) internal pure returns (uint256) {
        require(_x != 0 && _x != _pp && _pp != 0, "Invalid number");
        uint256 q = 0;
        uint256 newT = 1;
        uint256 r = _pp;
        uint256 t;
        while (_x != 0) {
            t = r / _x;
            (q, newT) = (newT, addmod(q, (_pp - mulmod(t, newT, _pp)), _pp));
            (r, _x) = (_x, r - t * _x);
        }
        return q;
    }

    /// @dev Modular exponentiation, b^e % m
    /// Basically the same as can be found here:
    /// https://github.com/ethereum/serpent/blob/develop/examples/ecc/modexp.se
    /// @param b The base.
    /// @param e The exponent.
    /// @param m The modulus.
    /// @return r such that r = b**e (mod m)
    function expmod(
        uint256 b,
        uint256 e,
        uint256 m
    ) internal pure returns (uint256 r) {
        if (b == 0) return 0;
        if (e == 0) return 1;
        // solhint-disable-next-line
        if (m == 0) revert();
        r = 1;
        uint256 bit = 2**255;
        assembly {
            // prettier-ignore
            // solhint-disable-next-line no-empty-blocks
            for {} eq(iszero(bit), 0) {} {
                r := mulmod(mulmod(r, r, m), exp(b, iszero(iszero(and(e, bit)))), m)
                r := mulmod(mulmod(r, r, m), exp(b, iszero(iszero(and(e, div(bit, 2))))), m)
                r := mulmod(mulmod(r, r, m), exp(b, iszero(iszero(and(e, div(bit, 4))))), m)
                r := mulmod(mulmod(r, r, m), exp(b, iszero(iszero(and(e, div(bit, 8))))), m)
                bit := div(bit, 16)
            }
        }
    }

    /// @dev Converts a point (Px, Py, Pz) expressed in Jacobian coordinates to (Px', Py', 1).
    /// Mutates P.
    /// @param P The point.
    /// @param zInv The modular inverse of 'Pz'.
    /// @param z2Inv The square of zInv
    /// @param prime The prime modulus.
    // / @return (Px', Py', 1)
    function toZ1(
        uint256[3] memory P,
        uint256 zInv,
        uint256 z2Inv,
        uint256 prime
    ) internal pure {
        P[0] = mulmod(P[0], z2Inv, prime);
        P[1] = mulmod(P[1], mulmod(zInv, z2Inv, prime), prime);
        P[2] = 1;
    }

    /// @dev See _toZ1(uint[3], uint, uint).
    /// Warning: Computes a modular inverse.
    /// @param PJ The point.
    /// @param prime The prime modulus.
    // / @return (Px', Py', 1)
    function toZ1(uint256[3] memory PJ, uint256 prime) internal pure {
        uint256 zInv = invmod(PJ[2], prime);
        uint256 zInv2 = mulmod(zInv, zInv, prime);
        PJ[0] = mulmod(PJ[0], zInv2, prime);
        PJ[1] = mulmod(PJ[1], mulmod(zInv, zInv2, prime), prime);
        PJ[2] = 1;
    }
    /* solhint-disable no-inline-assembly */
}
