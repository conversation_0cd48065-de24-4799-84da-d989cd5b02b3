import "./Secp256k1.sol";
import "./Curve.sol";

/**
 * @title Secp256k1Curve
 *
 * Secp256k1 contract that implements the Curve interface.
 * See 'Curve' for details.
 *
 * <AUTHOR> (<EMAIL>)
 * @see <a href=https://github.com/androlo/standard-contracts/blob/master/contracts/src/crypto/Secp256k1Curve.sol>Secp256k1Curve</a>
 * @changed pragma version
 */
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

contract Secp256k1Curve is Curve {
    /* solhint-disable no-inline-assembly */
    function onCurve(uint256[2] memory P) external pure override returns (bool) {
        return Secp256k1.onCurve(P);
    }

    function isPubKey(uint256[2] memory P) external pure override returns (bool) {
        return Secp256k1.isPubKey(P);
    }

    function validateSignature(
        bytes32 h,
        uint256[2] memory rs,
        uint256[2] memory Q
    ) external pure override returns (bool) {
        return Secp256k1.validateSignature(h, rs, Q);
    }

    function compress(uint256[2] memory P) external pure override returns (uint8 yBit, uint256 x) {
        return Secp256k1.compress(P);
    }

    function decompress(uint8 yBit, uint256 Px) external pure override returns (uint256[2] memory) {
        return Secp256k1.decompress(yBit, Px);
    }
    /* solhint-disable no-inline-assembly */
}
