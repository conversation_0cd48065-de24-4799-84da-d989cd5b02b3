// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "../interfaces/IContractManager.sol";
import "../ContractManager.sol";

import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

contract RemigrationRestore is Initializable {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス **/
    IContractManager private _contractManager;

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager ContractManagerアドレス
     */
    function initialize(IContractManager contractManager) public initializer {
        _contractManager = contractManager;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @dev 指定されたProviderに紐づくProvider情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     */
    function restoreProviders(
        ProviderAll[] memory providers,
        uint256 deadline,
        bytes memory signature
    ) external {
        for (uint256 i = 0; i < providers.length; i++) {
            _contractManager.provider().setProviderAll(providers[i], deadline, signature);
        }
    }

    /**
     * @dev 指定されたValidatorIdに紐づくValidator情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param validators validators
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function restoreValidators(
        ValidatorAll[] memory validators,
        uint256 deadline,
        bytes memory signature
    ) external {
        for (uint256 i = 0; i < validators.length; i++) {
            _contractManager.validator().setValidatorAll(validators[i], deadline, signature);
        }
    }

    /**
     * @dev 指定されたIssuerIdに紐づくIssuer情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param issuers issuers
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function restoreIssuers(
        IssuerAll[] memory issuers,
        uint256 deadline,
        bytes memory signature
    ) external {
        for (uint256 i = 0; i < issuers.length; i++) {
            _contractManager.issuer().setIssuerAll(issuers[i], deadline, signature);
        }
    }

    /**
     * @dev Account情報を再登録する
     *      バックアップリスト作業時のみ実行
     */
    function restoreAccounts(
        AccountsAll[] memory accounts,
        uint256 deadline,
        bytes memory signature
    ) external {
        for (uint256 i = 0; i < accounts.length; i++) {
            _contractManager.account().setAccountAll(accounts[i], deadline, signature);
        }
    }

    /**
     * @dev Token情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param token token
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function restoreToken(
        TokenAll memory token,
        uint256 deadline,
        bytes memory signature
    ) external {
        _contractManager.token().setTokenAll(token, deadline, signature);
    }

    /**
     * @dev FinancialZoneAccount情報を再登録する
     *     バックアップリスト作業時のみ実行
     */
    function restoreFinancialZoneAccounts(
        FinancialZoneAccountsAll[] memory financialZoneAccounts,
        uint256 deadline,
        bytes memory signature
    ) external {
        for (uint256 i = 0; i < financialZoneAccounts.length; i++) {
            _contractManager.financialZoneAccount().setFinAccountAll(
                financialZoneAccounts[i],
                deadline,
                signature
            );
        }
    }

    /**
     * @dev 指定されたAccountIdに紐づくBusinessZoneAccount情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param bizAccounts businessZoneAccounts
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function restoreBusinessZoneAccounts(
        BizAccountsAll[] memory bizAccounts,
        uint256 deadline,
        bytes memory signature
    ) external {
        for (uint256 i = 0; i < bizAccounts.length; i++) {
            _contractManager.businessZoneAccount().setBizAccountsAll(
                bizAccounts[i],
                deadline,
                signature
            );
        }
    }
}
