// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

library RemigrationLib {
    function setValidatorAll(
        ValidatorData storage validatorMapping,
        mapping(bytes32 => bool) storage accountIdExistenceByValidatorIdMapping,
        mapping(bytes32 => bool) storage validatorIdExistence,
        mapping(bytes32 => bool) storage issuerIdLinkedFlag,
        IContractManager contractManager,
        ValidatorAll memory validator
    ) external {
        validatorMapping.name = validator.name;
        validatorMapping.issuerId = validator.issuerId;
        validatorMapping.role = validator.role;
        validatorMapping.enabled = validator.enabled;
        validatorMapping.validatorAccountId = validator.validatorAccountId;

        validatorIdExistence[validator.validatorId] = validator.validatorIdExistence;
        issuerIdLinkedFlag[validator.issuerId] = validator.issuerIdLinkedFlag;

        contractManager.accessCtrl().addRoleByValidator(
            validator.validatorId,
            validator.role,
            validator.validatorEoa
        );

        for (uint256 i = 0; i < validator.validAccountExistence.length; i++) {
            validatorMapping.accountIds.push(validator.validAccountExistence[i].accountId);
            accountIdExistenceByValidatorIdMapping[
                validator.validAccountExistence[i].accountId
            ] = validator.validAccountExistence[i].accountIdExistenceByValidatorId;
        }
    }

    /**
     * @dev 全てのValidatorsを取得
     *      バックアップリスト作業時のみ実行
     *
     * @param validatorMapping 検証者データを保存するマッピング
     * @param accountIdExistenceByValidatorIdMapping 検証者に紐ついているアカウントの存在情報を保存するマッピング
     * @param validatorIdExistenceMapping 検証者存在情報を保存するマッピング
     * @param issuerIdLinkedFlagMapping 検証者に紐づく発行者の存在確認
     * @param contractManager コントラクトマネージャー
     * @param validatorId validator Id
     */
    function getValidatorAll(
        ValidatorData storage validatorMapping,
        mapping(bytes32 => bool) storage accountIdExistenceByValidatorIdMapping,
        mapping(bytes32 => bool) storage validatorIdExistenceMapping,
        mapping(bytes32 => bool) storage issuerIdLinkedFlagMapping,
        IContractManager contractManager,
        bytes32 validatorId
    ) external view returns (ValidatorAll memory validator) {
        validator.validatorId = validatorId;
        validator.name = validatorMapping.name;
        validator.issuerId = validatorMapping.issuerId;
        validator.role = validatorMapping.role;
        validator.validatorAccountId = validatorMapping.validatorAccountId;
        validator.enabled = validatorMapping.enabled;

        validator.validatorIdExistence = validatorIdExistenceMapping[validatorId];
        validator.issuerIdLinkedFlag = issuerIdLinkedFlagMapping[validatorMapping.issuerId];
        validator.validatorEoa = contractManager.accessCtrl().getValidatorEoa(validatorId);

        ValidatorAccountsExistence[]
            memory validAccountExistence = new ValidatorAccountsExistence[](
                validatorMapping.accountIds.length
            );
        for (uint256 i = 0; i < validatorMapping.accountIds.length; i++) {
            validAccountExistence[i].accountId = validatorMapping.accountIds[i];
            validAccountExistence[i]
                .accountIdExistenceByValidatorId = accountIdExistenceByValidatorIdMapping[
                validatorMapping.accountIds[i]
            ];
        }
        validator.validAccountExistence = validAccountExistence;

        return validator;
    }

    /**
     * @dev 全てのProviderを登録
     *      バックアップリスト作業時のみ実行
     *
     */
    function setProviderAll(
        ProviderData storage providerData,
        mapping(uint16 => ZoneData) storage zoneData,
        IContractManager contractManager,
        ProviderAll memory provider
    ) external {
        providerData.role = provider.providerData.role;
        providerData.name = provider.providerData.name;
        providerData.zoneId = provider.providerData.zoneId;
        providerData.enabled = provider.providerData.enabled;

        contractManager.accessCtrl().addRoleByProv(
            provider.providerId,
            provider.providerData.role,
            provider.providerEoa
        );
        // zoneDataのセット
        for (uint256 i = 0; i < provider.zoneData.length; i++) {
            zoneData[provider.zoneData[i].zoneId] = provider.zoneData[i];
        }
    }

    /**
     * @dev Accountを登録
     *      バックアップリスト作業時のみ実行
     *
     */
    function setAccountAll(
        bytes32[] storage accountIds,
        AccountData storage accountData,
        mapping(bytes32 => bool) storage accountIdExistingMapping,
        mapping(bytes32 => AllowanceList) storage accountApprovalMapping,
        IContractManager contractManager,
        AccountsAll memory account
    ) external {
        accountIds.push(account.accountId);
        accountData.accountName = account.accountName;
        accountData.accountStatus = account.accountStatus;
        accountData.zoneIds = account.zoneIds;
        accountData.balance = account.balance;
        accountData.reasonCode = account.reasonCode;
        accountData.appliedAt = account.appliedAt;
        accountData.registeredAt = account.registeredAt;
        accountData.terminatingAt = account.terminatingAt;
        accountData.terminatedAt = account.terminatedAt;
        accountData.validatorId = account.validatorId;

        contractManager.accessCtrl().addAccountEoa(account.accountId, account.accountEoa);

        accountIdExistingMapping[account.accountId] = account.accountIdExistence;

        for (uint256 lp = 0; lp < account.accountApprovalAll.length; lp++) {
            accountApprovalMapping[account.accountId].spender.push(
                account.accountApprovalAll[lp].spanderId
            );
            accountApprovalMapping[account.accountId].accountApprovalData[
                account.accountApprovalAll[lp].spanderId
            ] = AccountApproval(
                account.accountApprovalAll[lp].spenderAccountName,
                account.accountApprovalAll[lp].allowanceAmount,
                account.accountApprovalAll[lp].approvedAt
            );
        }
    }

    /**
     * @dev 全てのFinancialZoneAccount情報を取得
     *      バックアップリスト作業時のみ実行
     *
     */
    function setFinAccountAll(
        mapping(bytes32 => FinancialZoneAccountData) storage finAccountDataMapping,
        FinancialZoneAccountsAll memory finAccount
    ) external {
        bytes32 finAccountId = finAccount.accountId;
        finAccountDataMapping[finAccountId].mintLimit = finAccount
            .financialZoneAccountData
            .mintLimit;
        finAccountDataMapping[finAccountId].burnLimit = finAccount
            .financialZoneAccountData
            .burnLimit;
        finAccountDataMapping[finAccountId].chargeLimit = finAccount
            .financialZoneAccountData
            .chargeLimit;
        finAccountDataMapping[finAccountId].transferLimit = finAccount
            .financialZoneAccountData
            .transferLimit;
        finAccountDataMapping[finAccountId].cumulativeLimit = finAccount
            .financialZoneAccountData
            .cumulativeLimit;
        finAccountDataMapping[finAccountId].cumulativeAmount = finAccount
            .financialZoneAccountData
            .cumulativeAmount;
        finAccountDataMapping[finAccountId].cumulativeDate = finAccount
            .financialZoneAccountData
            .cumulativeDate;
    }

    /**
     * @dev 全てのProviderを取得
     *      バックアップリスト作業時のみ実行
     *
     */
    function getProviderAll(
        ProviderData storage providerData,
        mapping(uint16 => ZoneData) storage zoneData,
        IContractManager contractManager,
        bytes32 providerId
    ) external view returns (ProviderAll memory provider) {
        provider.providerId = providerId;
        provider.providerData.role = providerData.role;
        provider.providerData.name = providerData.name;
        provider.providerData.zoneId = providerData.zoneId;
        provider.providerData.enabled = providerData.enabled;
        provider.providerEoa = contractManager.accessCtrl().getProviderEoa(providerId);

        // ZoneDataの取得とバックアップ
        uint16 currentZoneId = providerData.zoneId;
        uint16 zoneCount = 0;
        while (bytes(zoneData[currentZoneId + zoneCount].zoneName).length != 0) {
            zoneCount += 1;
        }

        provider.zoneData = new ZoneData[](zoneCount);
        // ZoneDataをセット
        for (uint16 i = 0; i < zoneCount; i++) {
            provider.zoneData[i] = zoneData[currentZoneId + i];
        }

        return provider;
    }

    /**
     * @dev Accountを取得
     *      バックアップリスト作業時のみ実行
     *
     */
    function getAccountAll(
        AccountData storage accountData,
        mapping(bytes32 => bool) storage accountIdExistingMapping,
        mapping(bytes32 => AllowanceList) storage accountApprovalMapping,
        IContractManager contractManager,
        bytes32 accountId
    ) external view returns (AccountsAll memory account) {
        account.accountId = accountId;
        account.accountName = accountData.accountName;
        account.accountStatus = accountData.accountStatus;
        account.zoneIds = accountData.zoneIds;
        account.balance = accountData.balance;
        account.reasonCode = accountData.reasonCode;
        account.appliedAt = accountData.appliedAt;
        account.registeredAt = accountData.registeredAt;
        account.terminatingAt = accountData.terminatingAt;
        account.terminatedAt = accountData.terminatedAt;
        account.validatorId = accountData.validatorId;

        account.accountEoa = contractManager.accessCtrl().getAccountEoa(accountId);
        account.accountIdExistence = accountIdExistingMapping[accountId];

        bytes32[] memory _spenders = accountApprovalMapping[accountId].spender;
        AccountApprovalAll[] memory _accountApprovalAll = new AccountApprovalAll[](
            _spenders.length
        );
        for (uint256 lp2 = 0; lp2 < _spenders.length; lp2++) {
            _accountApprovalAll[lp2].spanderId = _spenders[lp2];
            _accountApprovalAll[lp2].spenderAccountName = accountApprovalMapping[accountId]
                .accountApprovalData[_spenders[lp2]]
                .spenderAccountName;
            _accountApprovalAll[lp2].allowanceAmount = accountApprovalMapping[accountId]
                .accountApprovalData[_spenders[lp2]]
                .approvedAmount;
            _accountApprovalAll[lp2].approvedAt = accountApprovalMapping[accountId]
                .accountApprovalData[_spenders[lp2]]
                .approvedAt;
        }
        account.accountApprovalAll = _accountApprovalAll;

        return account;
    }

    /**
     * @dev 全てのFinancialZoneAccount情報を取得
     *      バックアップリスト作業時のみ実行
     *
     */
    function getFinAccountAll(
        mapping(bytes32 => FinancialZoneAccountData) storage finAccountDataMapping,
        IContractManager contractManager,
        uint256 index
    ) external view returns (FinancialZoneAccountsAll memory finAccount) {
        (bytes32 accountId, ) = contractManager.account().getAccountId(index);
        finAccount.accountId = accountId;
        finAccount.financialZoneAccountData.mintLimit = finAccountDataMapping[accountId].mintLimit;
        finAccount.financialZoneAccountData.burnLimit = finAccountDataMapping[accountId].burnLimit;
        finAccount.financialZoneAccountData.chargeLimit = finAccountDataMapping[accountId]
            .chargeLimit;
        finAccount.financialZoneAccountData.transferLimit = finAccountDataMapping[accountId]
            .transferLimit;
        finAccount.financialZoneAccountData.cumulativeLimit = finAccountDataMapping[accountId]
            .cumulativeLimit;
        finAccount.financialZoneAccountData.cumulativeAmount = finAccountDataMapping[accountId]
            .cumulativeAmount;
        finAccount.financialZoneAccountData.cumulativeDate = finAccountDataMapping[accountId]
            .cumulativeDate;

        return finAccount;
    }

    function setIssuerAll(
        IssuerData storage issuerMapping,
        bytes32[] storage issuserIdsArray,
        mapping(bytes32 => bool) storage issuerIdExistenceMapping,
        mapping(bytes32 => mapping(bytes32 => bool)) storage accountIdExistenceByIssuerIdMapping,
        IContractManager contractManager,
        IssuerAll memory issuer
    ) external {
        bytes32 issuerId = issuer.issuerId;
        issuserIdsArray.push(issuerId);
        issuerMapping.role = issuer.role;
        issuerMapping.name = issuer.name;
        issuerMapping.bankCode = issuer.bankCode;
        issuerIdExistenceMapping[issuerId] = issuer.issuerIdExistence;

        for (uint256 lp = 0; lp < issuer.issuerAccountExistence.length; lp++) {
            issuerMapping.accountIds.push(issuer.issuerAccountExistence[lp].accountId);
            accountIdExistenceByIssuerIdMapping[issuerId][
                issuer.issuerAccountExistence[lp].accountId
            ] = issuer.issuerAccountExistence[lp].accountIdExistenceByIssuerId;
        }

        contractManager.accessCtrl().addRoleByIssuer(
            issuer.issuerId,
            issuer.role,
            issuer.issuerEoa
        );
    }

    function getIssuerAll(
        IssuerData storage issuerMapping,
        bytes32[] storage issuerIdsArray,
        mapping(bytes32 => bool) storage issuerIdExistenceMapping,
        mapping(bytes32 => mapping(bytes32 => bool)) storage accountIdExistenceByIssuerIdMapping,
        IContractManager contractManager,
        uint256 index
    ) external view returns (IssuerAll memory issuer) {
        bytes32 issuerId = issuerIdsArray[index];
        issuer.issuerId = issuerId;
        issuer.role = issuerMapping.role;
        issuer.name = issuerMapping.name;
        issuer.bankCode = issuerMapping.bankCode;
        issuer.issuerIdExistence = issuerIdExistenceMapping[issuerId];

        issuer.issuerEoa = contractManager.accessCtrl().getIssuerEoa(issuerId);

        bytes32[] memory _accountIds = issuerMapping.accountIds;
        IssuerAccountsExistence[]
            memory _accountIdExistenceByIssuerId = new IssuerAccountsExistence[](
                _accountIds.length
            );

        for (uint256 lp = 0; lp < _accountIds.length; lp++) {
            _accountIdExistenceByIssuerId[lp].accountId = _accountIds[lp];
            _accountIdExistenceByIssuerId[lp]
                .accountIdExistenceByIssuerId = accountIdExistenceByIssuerIdMapping[issuerId][
                _accountIds[lp]
            ];
        }
        issuer.issuerAccountExistence = _accountIdExistenceByIssuerId;

        return (issuer);
    }

    /**
     * @dev Token全情報登録
     * @param tokenDataMapping Tokenの情報
     * @param token signatureのタイムスタンプ(秒)

     */
    function setTokenAll(
        mapping(bytes32 => TokenData) storage tokenDataMapping,
        TokenAll memory token
    ) external {
        tokenDataMapping[token.tokenId].name = token.name;
        tokenDataMapping[token.tokenId].symbol = token.symbol;
        tokenDataMapping[token.tokenId].totalSupply = token.totalSupply;
        tokenDataMapping[token.tokenId].enabled = token.enabled;
    }

    /**
     * @dev Token全情報取得
     * @param tokenDataMapping Tokenデータ
     * @param tokenId TokenID

     */
    function getTokenAll(mapping(bytes32 => TokenData) storage tokenDataMapping, bytes32 tokenId)
        external
        view
        returns (TokenAll memory token)
    {
        token.tokenId = tokenId;
        token.name = tokenDataMapping[tokenId].name;
        token.symbol = tokenDataMapping[tokenId].symbol;
        token.totalSupply = tokenDataMapping[tokenId].totalSupply;
        token.enabled = tokenDataMapping[tokenId].enabled;
        return token;
    }

    /**
     * @dev BusinessZoneAccountsを一括登録する
     *      バックアップリスト作業時のみ実行
     *
     * @param bizAccountDataMapping BusinessZoneAccountsを保存するマッピング
     * @param accountIdExistenceByZoneIdMapping アカウント存在確認マッピング
     * @param bizAccounts 登録するBusinessZoneAccounts
     */
    function setBizAccountsAll(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage bizAccountDataMapping,
        mapping(uint16 => mapping(bytes32 => bool)) storage accountIdExistenceByZoneIdMapping,
        BizAccountsAll memory bizAccounts
    ) external {
        bytes32 _accountId = bizAccounts.accountId;
        BizAccountsByZoneId[] memory _bizAccountsByZoneId = bizAccounts.bizAccountsByZoneId;
        for (uint256 lp = 0; lp < _bizAccountsByZoneId.length; lp++) {
            if (_bizAccountsByZoneId[lp].accountIdExistenceByZoneId) {
                accountIdExistenceByZoneIdMapping[_bizAccountsByZoneId[lp].zoneId][
                    _accountId
                ] = _bizAccountsByZoneId[lp].accountIdExistenceByZoneId;
                bizAccountDataMapping[_bizAccountsByZoneId[lp].zoneId][_accountId]
                    .accountName = _bizAccountsByZoneId[lp].businessZoneAccountData.accountName;
                bizAccountDataMapping[_bizAccountsByZoneId[lp].zoneId][_accountId]
                    .balance = _bizAccountsByZoneId[lp].businessZoneAccountData.balance;
                bizAccountDataMapping[_bizAccountsByZoneId[lp].zoneId][_accountId]
                    .accountStatus = _bizAccountsByZoneId[lp].businessZoneAccountData.accountStatus;
                bizAccountDataMapping[_bizAccountsByZoneId[lp].zoneId][_accountId]
                    .appliedAt = _bizAccountsByZoneId[lp].businessZoneAccountData.appliedAt;
                bizAccountDataMapping[_bizAccountsByZoneId[lp].zoneId][_accountId]
                    .registeredAt = _bizAccountsByZoneId[lp].businessZoneAccountData.registeredAt;
                bizAccountDataMapping[_bizAccountsByZoneId[lp].zoneId][_accountId]
                    .terminatingAt = _bizAccountsByZoneId[lp].businessZoneAccountData.terminatingAt;
                bizAccountDataMapping[_bizAccountsByZoneId[lp].zoneId][_accountId]
                    .terminatedAt = _bizAccountsByZoneId[lp].businessZoneAccountData.terminatedAt;
            }
        }
    }

    /**
     * @dev limitとoffsetで指定したBuisinessZoneAccountsを一括取得する
     *      バックアップリスト作業時のみ実行
     *
     * @param bizAccountDataMapping BusinessZoneAccountsデータを保存するマッピング
     * @param accountIdExistenceByZoneIdMapping アカウント存在マッピング
     * @param contractManager コントラクトマネージャー
     * @param index 取得accountsのindex
     */
    function getBizAccountsAll(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage bizAccountDataMapping,
        mapping(uint16 => mapping(bytes32 => bool)) storage accountIdExistenceByZoneIdMapping,
        IContractManager contractManager,
        uint256 index
    ) external view returns (BizAccountsAll memory bizAccount) {
        (bytes32 accountId, ) = contractManager.account().getAccountId(index);
        bizAccount.accountId = accountId;

        ZoneData[] memory zoneIdList = contractManager.account().getZoneByAccountId(accountId);

        BizAccountsByZoneId[] memory bizAccountsByZoneId = new BizAccountsByZoneId[](
            zoneIdList.length
        );

        for (uint256 lp = 0; lp < zoneIdList.length; lp++) {
            bizAccountsByZoneId[lp].zoneId = zoneIdList[lp].zoneId;
            bizAccountsByZoneId[lp].accountIdExistenceByZoneId = accountIdExistenceByZoneIdMapping[
                zoneIdList[lp].zoneId
            ][accountId];
            bizAccountsByZoneId[lp].businessZoneAccountData.accountName = bizAccountDataMapping[
                zoneIdList[lp].zoneId
            ][accountId].accountName;
            bizAccountsByZoneId[lp].businessZoneAccountData.balance = bizAccountDataMapping[
                zoneIdList[lp].zoneId
            ][accountId].balance;
            bizAccountsByZoneId[lp].businessZoneAccountData.accountStatus = bizAccountDataMapping[
                zoneIdList[lp].zoneId
            ][accountId].accountStatus;
            bizAccountsByZoneId[lp].businessZoneAccountData.appliedAt = bizAccountDataMapping[
                zoneIdList[lp].zoneId
            ][accountId].appliedAt;
            bizAccountsByZoneId[lp].businessZoneAccountData.registeredAt = bizAccountDataMapping[
                zoneIdList[lp].zoneId
            ][accountId].registeredAt;
            bizAccountsByZoneId[lp].businessZoneAccountData.terminatingAt = bizAccountDataMapping[
                zoneIdList[lp].zoneId
            ][accountId].terminatingAt;
            bizAccountsByZoneId[lp].businessZoneAccountData.terminatedAt = bizAccountDataMapping[
                zoneIdList[lp].zoneId
            ][accountId].terminatedAt;
        }
        bizAccount.bizAccountsByZoneId = bizAccountsByZoneId;
        return (bizAccount);
    }
}
