// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

library FinancialZoneAccountLib {
    /**
     * @dev アカウントの限度額を取得する
     *
     * @param accountLimitMapping 取得対象となるアカウント限度額設定のマッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @return accountLimitData
     */
    function getAccountLimitData(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitMapping,
        bytes32 key
    ) external view returns (FinancialZoneAccountData memory accountLimitData) {
        return accountLimitMapping[key];
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev アカウント限度額登録
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額のマッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param limitAmounts 登録対象の限度額データ
     */
    function addAccountLimitData(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        uint256[] memory limitAmounts
    ) external {
        {
            require(limitAmounts.length == 5, Error.LIMIT_AMOUNT_INVALID_COUNT);
            require(limitAmounts[0] <= Constant._MAX_LIMIT_VALUE, Error.EXCEEDED_TRANSFER_LIMIT);
            require(limitAmounts[1] <= Constant._MAX_LIMIT_VALUE, Error.EXCEEDED_CHARGE_LIMIT);
            require(limitAmounts[2] <= Constant._MAX_LIMIT_VALUE, Error.EXCEEDED_MINT_LIMIT);
            require(limitAmounts[3] <= Constant._MAX_LIMIT_VALUE, Error.EXCEEDED_BURN_LIMIT);
            require(limitAmounts[4] <= Constant._MAX_DAILY_LIMIT_VALUE, Error.EXCEEDED_DAILY_LIMIT);
        }
        accountLimitDataMapping[key].transferLimit = limitAmounts[0];
        accountLimitDataMapping[key].chargeLimit = limitAmounts[1];
        accountLimitDataMapping[key].mintLimit = limitAmounts[2];
        accountLimitDataMapping[key].burnLimit = limitAmounts[3];
        accountLimitDataMapping[key].cumulativeLimit = limitAmounts[4];
    }

    /**
     * @dev アカウント限度額登録
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額のマッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param limitAmounts 登録対象の限度額データ
     */
    function modAccountLimitData(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        bool[] memory itemFlgs,
        uint256[] memory limitAmounts
    ) external returns (uint256[] memory) {
        // 各フラグに応じて限度額を設定
        // 送金限度額を設定する
        if (itemFlgs[0] == true) {
            require(limitAmounts[0] <= Constant._MAX_LIMIT_VALUE, Error.EXCEEDED_TRANSFER_LIMIT);
            accountLimitDataMapping[key].transferLimit = limitAmounts[0];
        }
        // 割当限度額を設定する
        if (itemFlgs[1] == true) {
            require(limitAmounts[1] <= Constant._MAX_LIMIT_VALUE, Error.EXCEEDED_CHARGE_LIMIT);
            accountLimitDataMapping[key].chargeLimit = limitAmounts[1];
        }
        // 交換限度額を設定する
        if (itemFlgs[2] == true) {
            require(limitAmounts[2] <= Constant._MAX_LIMIT_VALUE, Error.EXCEEDED_MINT_LIMIT);
            accountLimitDataMapping[key].mintLimit = limitAmounts[2];
        }
        // 償却限度額を設定する
        if (itemFlgs[3] == true) {
            require(limitAmounts[3] <= Constant._MAX_LIMIT_VALUE, Error.EXCEEDED_BURN_LIMIT);
            accountLimitDataMapping[key].burnLimit = limitAmounts[3];
        }
        // 累積限度額を設定する
        if (itemFlgs[4] == true) {
            require(limitAmounts[4] <= Constant._MAX_DAILY_LIMIT_VALUE, Error.EXCEEDED_DAILY_LIMIT);
            accountLimitDataMapping[key].cumulativeLimit = limitAmounts[4];
        }

        // Event用に設定後のAccountの限度額Listを作成
        limitAmounts[0] = accountLimitDataMapping[key].transferLimit;
        limitAmounts[1] = accountLimitDataMapping[key].chargeLimit;
        limitAmounts[2] = accountLimitDataMapping[key].mintLimit;
        limitAmounts[3] = accountLimitDataMapping[key].burnLimit;
        limitAmounts[4] = accountLimitDataMapping[key].cumulativeLimit;

        return limitAmounts;
    }

    /**
     * @dev アカウント限度額初期化
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額のマッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param jstDay 現在の日付
     */
    function resetCumulative(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        uint256 jstDay
    ) external {
        accountLimitDataMapping[key].cumulativeAmount = 0;
        accountLimitDataMapping[key].cumulativeDate = jstDay;
    }

    /**
     * @dev 累積限度額の加算を行う TODO:減算処理と統合する？
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額マッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param amount 金額
     * @param currentDay 現在の日付
     * @return cumulativeDate
     * @return cumulativeAmount
     */
    function addAmount(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        uint256 amount,
        uint256 currentDay
    ) external returns (uint256 cumulativeDate, uint256 cumulativeAmount) {
        if (accountLimitDataMapping[key].cumulativeDate == currentDay) {
            accountLimitDataMapping[key].cumulativeAmount += amount;
        } else {
            accountLimitDataMapping[key].cumulativeAmount = amount;
            accountLimitDataMapping[key].cumulativeDate = currentDay;
        }

        return (
            accountLimitDataMapping[key].cumulativeDate,
            accountLimitDataMapping[key].cumulativeAmount
        );
    }

    /**
     * @dev 累積限度額の減算を行う
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額マッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param amount 金額
     * @return cumulativeDate
     * @return cumulativeAmount
     */

    function subtractAmount(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        uint256 amount
    ) external returns (uint256 cumulativeDate, uint256 cumulativeAmount) {
        accountLimitDataMapping[key].cumulativeAmount -= amount;
        return (
            accountLimitDataMapping[key].cumulativeDate,
            accountLimitDataMapping[key].cumulativeAmount
        );
    }

    /**
     * @dev 操作額のamountを累積限度額に反映させる TODO:mintLimitの更新は本当に不要かどうか精査する
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額マッピングデータ
     * @param key アカウントID
     * @param amount 金額
     * @param currentDay 現在の日付
     */
    function syncLimitAmount(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        uint256 amount,
        uint256 currentDay
    ) external {
        if (accountLimitDataMapping[key].cumulativeDate == currentDay) {
            accountLimitDataMapping[key].cumulativeAmount += amount;
        } else {
            accountLimitDataMapping[key].cumulativeAmount = amount;
            accountLimitDataMapping[key].cumulativeDate = currentDay;
        }
    }
}
