// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

library BusinessZoneAccountLib {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /* @dev AccountStatus判定用の定数(口座開設済) */
    bytes32 private constant _STATUS_ACTIVE = "active";
    /* @dev AccountStatus判定用の定数(口座申し込み) */
    bytes32 private constant _STATUS_APPLIYNG = "applying";
    /* @dev AccountStatus判定用の定数(口座解約申し込み) */
    bytes32 private constant _STATUS_TERMINATING = "terminating";
    /* @dev AccountStatus判定用の定数(口座解約申し込み) */
    bytes32 private constant _STATUS_TERMINATED = "terminated";

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev BizZoneのアカウント情報を取得する
     * @param businessZoneaccountDataMapping 取得対象のBizZoneのアカウントデータが格納されているマッピング
     * @param key マッピングのキーとなゾーンID
     * @param accountId マッピングのキーとなるアカウントID
     * @return businessZoneAccountData
     */
    function getBusinessZoneAccountData(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage businessZoneaccountDataMapping,
        uint16 key,
        bytes32 accountId
    ) external view returns (BusinessZoneAccountData memory) {
        return businessZoneaccountDataMapping[key][accountId];
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev ビジネスゾーンアカウントの登録
     *
     * @param bizZoneaccountDataMapping 登録先のアカウントのマッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param accountId アカウントID
     */
    function setActivateAccount(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage bizZoneaccountDataMapping,
        uint16 key,
        bytes32 accountId
    ) external {
        bizZoneaccountDataMapping[key][accountId].accountStatus = _STATUS_ACTIVE;
        bizZoneaccountDataMapping[key][accountId].registeredAt = block.timestamp;
    }

    /**
     * @dev ビジネスゾーンアカウントの解約
     *
     * @param bizZoneaccountDataMapping 登録先のアカウントのマッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param accountId アカウントID
     */
    function setBizZoneTerminated(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage bizZoneaccountDataMapping,
        uint16 key,
        bytes32 accountId
    ) external {
        bizZoneaccountDataMapping[key][accountId].accountStatus = _STATUS_TERMINATED;
        bizZoneaccountDataMapping[key][accountId].terminatedAt = block.timestamp;
    }

    /**
     * @dev ビジネスゾーンアカウント情報の更新(口座申し込み, 解約申し込み実行)
     *
     * @param businessZoneAccountDataMapping 更新先のビジネスゾーンアカウントデータのマッピング
     * @param zoneId zoneId
     * @param accountId アカウントID
     * @param accountName アカウント名
     * @param accountStatus アカウントステータス
     * @param traceId トレースID
     */
    function syncBusinessZoneStatus(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage businessZoneAccountDataMapping,
        mapping(uint16 => mapping(bytes32 => bool)) storage businessZoneAccountExistenceMapping,
        IContractManager contractMananger,
        uint16 zoneId,
        bytes32 accountId,
        string memory accountName,
        bytes32 accountStatus,
        bytes32 traceId
    ) external {
        if (accountStatus == _STATUS_APPLIYNG) {
            businessZoneAccountDataMapping[zoneId][accountId].accountName = accountName;
            businessZoneAccountDataMapping[zoneId][accountId].balance = 0;
            businessZoneAccountDataMapping[zoneId][accountId].accountStatus = accountStatus;
            businessZoneAccountDataMapping[zoneId][accountId].appliedAt = block.timestamp;
            businessZoneAccountDataMapping[zoneId][accountId].registeredAt = 0;
            businessZoneAccountDataMapping[zoneId][accountId].terminatingAt = 0;
            businessZoneAccountDataMapping[zoneId][accountId].terminatedAt = 0;
            contractMananger.account().addZone(accountId, zoneId, traceId);
        }
        if (accountStatus == _STATUS_TERMINATING) {
            businessZoneAccountDataMapping[zoneId][accountId].accountStatus = accountStatus;
            businessZoneAccountDataMapping[zoneId][accountId].terminatingAt = block.timestamp;
        }

        businessZoneAccountExistenceMapping[zoneId][accountId] = true;
        businessZoneAccountDataMapping[zoneId][accountId].accountStatus = accountStatus;
    }

    /**
     * @dev ビジネスゾーンアカウントステータス更新
     *
     * @param businessZoneAccountDataMapping 更新先のビジネスゾーンアカウントデータのマッピング
     * @param zoneId zoneId
     * @param toAccountId 送り先アカウントId
     * @param fromAccountId 送り元アカウントID
     * @param balance アカウント残高
     */
    function syncBusinessZoneBalance(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage businessZoneAccountDataMapping,
        uint16 zoneId,
        bytes32 toAccountId,
        bytes32 fromAccountId,
        uint256 balance
    ) external {
        businessZoneAccountDataMapping[zoneId][toAccountId].balance += balance;
        businessZoneAccountDataMapping[zoneId][fromAccountId].balance -= balance;
    }

    /**
     * @dev ビジネスゾーンアカウント残高チャージ
     *
     * @param businessZoneAccountDataMapping 更新先のビジネスゾーンアカウントデータのマッピング
     * @param zoneId zoneId
     * @param accountId アカウントId
     * @param amount チャージ額
     */
    function addBusinessZoneBalance(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage businessZoneAccountDataMapping,
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external {
        businessZoneAccountDataMapping[zoneId][accountId].balance += amount;
    }

    /**
     * @dev ビジネスゾーンアカウント残高ディスチャージ
     *
     * @param businessZoneAccountDataMapping 更新先のビジネスゾーンアカウントデータのマッピング
     * @param zoneId zoneId
     * @param accountId アカウントId
     * @param amount ディスチャージ額
     */
    function subtractBusinessZoneBalance(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage businessZoneAccountDataMapping,
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external {
        businessZoneAccountDataMapping[zoneId][accountId].balance -= amount;
    }

    /**
     * @dev 残高を更新(償却)
     *
     * @param key zoneId
     * @param accountId accountId
     * @param amount Burnする数量
     */
    function balanceUpdateByRedeemVoucher(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage businessZoneAccountDataMapping,
        uint16 key,
        bytes32 accountId,
        uint256 amount
    ) external {
        // 付加領域からの結果なので失敗しない前提だが、Balanceのチェックを行う
        require(
            businessZoneAccountDataMapping[key][accountId].balance >= amount,
            Error.BALANCE_NOT_ENOUGH
        );

        // 残高を更新
        businessZoneAccountDataMapping[key][accountId].balance -= amount;
    }

    /**
     * @dev 残高を更新(発行)
     * @param key zoneId
     * @param accountId accountId
     * @param amount Burnする数量
     */
    function balanceUpdateByIssueVoucher(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage businessZoneAccountDataMapping,
        uint16 key,
        bytes32 accountId,
        uint256 amount
    ) external {
        // 残高を更新
        businessZoneAccountDataMapping[key][accountId].balance += amount;
    }

    /**
     * @dev 対象のビジネスゾーンの残高を0にし、ステータスを"terminated"に更新する(強制償却)
     *
     * @param businessZoneAccountDataMapping 更新先のビジネスゾーンアカウントデータのマッピング
     * @param key zoneId
     * @param accountId accountId
     * @return burnedAmount Burnした数量
     */
    function balanceUpdateByForceBurn(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage businessZoneAccountDataMapping,
        uint16 key,
        bytes32 accountId
    ) external returns (uint256 burnedAmount) {
        // 残高を更新
        burnedAmount = businessZoneAccountDataMapping[key][accountId].balance;
        businessZoneAccountDataMapping[key][accountId].balance = 0;
        businessZoneAccountDataMapping[key][accountId].accountStatus = Constant
            ._STATUS_FORCE_BURNED;

        return burnedAmount;
    }

    /**
     * @dev ビジネスゾーンアカウントのステータス更新(口座申し込み)
     *
     * @param businessZoneAccountDataMapping 更新先のビジネスゾーンアカウントデータのマッピング
     * @param zoneId zoneId
     * @param accountId アカウントID
     * @param accountStatus アカウントステータス
     */
    function setBusinessAccountStatus(
        mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData))
            storage businessZoneAccountDataMapping,
        uint16 zoneId,
        bytes32 accountId,
        bytes32 accountStatus
    ) external {
        businessZoneAccountDataMapping[zoneId][accountId].accountStatus = accountStatus;
    }
}
