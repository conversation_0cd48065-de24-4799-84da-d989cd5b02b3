// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

library ValidatorLib {
    uint256 private constant _MAX_LIMIT = 100;
    uint256 private constant _MAX_LIMIT_1000 = 1000;
    /** @dev 未登録の場合にて返す空の値 **/
    bytes32 private constant _EMPTY_VALUE = 0x00;
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant _EMPTY_LENGTH = 0;
    /** @dev FinZoneのID */
    uint16 private constant _FINANCIAL_ZONE = 3000;
    /** @dev ソート制御用の固定値(降順) */
    bytes32 private constant _DESC_SORT = keccak256(bytes("desc"));

    /** @dev バリデーション用のステータス値(申し込み) */
    bytes32 private constant _STATUS_APPLYING = "applying";
    /** @dev バリデーション用のステータス値(解約申し込み) */
    bytes32 private constant _STATUS_TERMINATING = "terminating";

    /**
     * @dev 検証者データを取得する
     *
     * @param validatorMapping 検証者データを保存するマッピング
     * @param contractManagerAddr コントラクトマネージャアドレスアドレス
     * @param validatorId マッピングのキーとなる検証者ID
     * @return name 検証者IDに紐づく検証者名
     * @return issuerId 検証者IDに紐づく発行者ID
     */
    function getValidator(
        mapping(bytes32 => ValidatorData) storage validatorMapping,
        address contractManagerAddr,
        bytes32 validatorId
    )
        external
        view
        returns (
            bytes32 name,
            bytes32 issuerId,
            string memory err
        )
    {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        bool success;
        (success, err) = contractManager.validator().hasValidator(validatorId);
        if (!success) {
            return (_EMPTY_VALUE, _EMPTY_VALUE, err);
        }

        return (validatorMapping[validatorId].name, validatorMapping[validatorId].issuerId, "");
    }

    /**
     * @dev 検証者データを取得する
     *
     * @param validatorMapping 検証者データを保存するマッピング
     * @param validatorIds マッピングのキーとなる検証者ID
     * @param limit マッピングのキーとなる検証者ID
     * @param offset マッピングのキーとなる検証者ID
     * @return validators 検証者IDに紐づく検証者名
     * @return totalCount 検証者IDに紐づく発行者ID
     * @return err 検証者IDに紐づく発行者ID
     */
    function getValidatorList(
        mapping(bytes32 => ValidatorData) storage validatorMapping,
        bytes32[] storage validatorIds,
        uint256 limit,
        uint256 offset
    )
        external
        view
        returns (
            ValidatorListData[] memory validators,
            uint256 totalCount,
            string memory err
        )
    {
        if (limit == 0 || validatorIds.length == 0) {
            return (validators, _EMPTY_LENGTH, "");
        }
        if (limit > _MAX_LIMIT) {
            return (validators, _EMPTY_LENGTH, Error.VALIDATOR_TOO_LARGE_LIMIT);
        }
        if (offset >= validatorIds.length) {
            return (validators, _EMPTY_LENGTH, Error.VALIDATOR_OFFSET_OUT_OF_INDEX);
        }

        // 配列のサイズを作成する。
        // 要素数がoffsetとlimitの指定範囲以上の配列である場合はlimit分の配列を作成する
        // 要素数がoffsetとlimitの指定範囲未満の配列である場合は範囲分の項目数のみの配列を作成する
        uint256 size = (validatorIds.length >= offset + limit)
            ? limit
            : validatorIds.length - offset;

        validators = new ValidatorListData[](size);
        for (uint256 i = 0; i < size; i++) {
            bytes32 validatorId = validatorIds[offset + i];
            validators[i].validatorId = validatorId;
            validators[i].name = validatorMapping[validatorId].name;
            validators[i].issuerId = validatorMapping[validatorId].issuerId;
        }

        return (validators, validatorIds.length, "");
    }

    function getAccountData(
        address contractManagerAddr,
        bytes32 validatorId,
        bytes32 accountId
    ) external view returns (AccountDataWithLimitData memory accountData, string memory err) {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        (bool success, string memory errHasAccount) = contractManager.validator().hasAccount(
            validatorId,
            accountId
        );
        if (!success) {
            return (accountData, errHasAccount);
        }

        AccountDataWithoutZoneId memory accountBaseData;
        (accountBaseData, err) = contractManager.account().getAccount(accountId);
        {
            accountData.accountName = accountBaseData.accountName;
            accountData.accountStatus = accountBaseData.accountStatus;
            accountData.balance = accountBaseData.balance;
            accountData.reasonCode = accountBaseData.reasonCode;
            accountData.appliedAt = accountBaseData.appliedAt;
            accountData.registeredAt = accountBaseData.registeredAt;
            accountData.terminatingAt = accountBaseData.terminatingAt;
            accountData.terminatedAt = accountBaseData.terminatedAt;
        }

        // FinancialZoneで実行する場合のみ、返却するaccountDataにLimit情報を加える
        (uint16 zoneId, , ) = contractManager.provider().getZone();
        if (zoneId == _FINANCIAL_ZONE) {
            FinancialZoneAccountData memory accountLimitData;
            (accountLimitData, err) = contractManager.account().getAccountLimit(
                validatorId,
                accountId
            );
            {
                accountData.mintLimit = accountLimitData.mintLimit;
                accountData.burnLimit = accountLimitData.burnLimit;
                accountData.transferLimit = accountLimitData.transferLimit;
                accountData.chargeLimit = accountLimitData.chargeLimit;
                accountData.dischargeLimit = accountLimitData.dischargeLimit;
                accountData.cumulativeLimit = accountLimitData.cumulativeLimit;
                accountData.cumulativeAmount = accountLimitData.cumulativeAmount;
                accountData.cumulativeDate = accountLimitData.cumulativeDate;
                accountData.cumulativeTransactionLimits = accountLimitData
                    .cumulativeTransactionLimits;
            }
        }
        return (accountData, "");
    }

    /**
     * @dev バリデータが直接管理するアカウントIDを取得する
     *
     * @param validatorMapping 検証者データを保存するマッピング
     * @param contractManagerAddr コントラクトマネージャーアドレス
     * @param validatorId バリデータID
     * @return accountId アカウントID
     * @return err エラー
     */
    function getValidatorAccountId(
        mapping(bytes32 => ValidatorData) storage validatorMapping,
        address contractManagerAddr,
        bytes32 validatorId
    ) external view returns (bytes32 accountId, string memory err) {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        (bool _success, string memory _err) = contractManager.validator().hasValidator(validatorId);
        if (!_success) {
            return (_EMPTY_VALUE, _err);
        }

        if (validatorMapping[validatorId].validatorAccountId == _EMPTY_VALUE) {
            return (_EMPTY_VALUE, Error.VALIDATOR_ACCOUNT_NOT_EXIST);
        }

        return (validatorMapping[validatorId].validatorAccountId, "");
    }

    /**
     * @dev 該当ValidatorIDに紐づくAccountの情報を取得する。
     *
     * @param inAccountIds 情報を取得したいaccountのIdの配列
     * @return accounts
     * @return totalCount
     * @return err エラーメッセージ
     */
    function getAccountList(
        address contractManagerAddr,
        bytes32[] memory inAccountIds,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            ValidatorAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        if (limit == 0 || inAccountIds.length == 0) {
            return (accounts, _EMPTY_LENGTH, "");
        }
        if (limit > _MAX_LIMIT) {
            return (accounts, _EMPTY_LENGTH, Error.ACCOUNT_TOO_LARGE_LIMIT);
        }
        if (offset >= inAccountIds.length) {
            return (accounts, _EMPTY_LENGTH, Error.ACCOUNT_OFFSET_OUT_OF_INDEX);
        }

        // 配列のサイズを作成する。
        // 要素数がoffsetとlimitの指定範囲以上の配列である場合はlimit分の配列を作成する
        // 要素数がoffsetとlimitの指定範囲未満の配列である場合は範囲分の項目数のみの配列を作成する
        uint256 size = (inAccountIds.length >= offset + limit)
            ? limit
            : inAccountIds.length - offset;

        accounts = new ValidatorAccountsData[](size);

        for (uint256 i = 0; i < size; i++) {
            bytes32 accountId = inAccountIds[offset + i];
            AccountDataWithoutZoneId memory accountData;

            accounts[i].accountId = accountId;
            (accountData, ) = contractManager.account().getAccount(accountId);
            accounts[i].accountName = accountData.accountName;
            accounts[i].balance = accountData.balance;
            accounts[i].accountStatus = accountData.accountStatus;
            accounts[i].reasonCode = accountData.reasonCode;
            accounts[i].appliedAt = accountData.appliedAt;
            accounts[i].registeredAt = accountData.registeredAt;
            accounts[i].terminatingAt = accountData.terminatingAt;
            accounts[i].terminatedAt = accountData.terminatedAt;
        }
        return (accounts, inAccountIds.length, "");
    }

    /**
     * @dev 該当ValidatorIDに紐づくAccountの情報を取得する。
     *
     * @param inAccountIds 情報を取得したいaccountのIdの配列
     * @return accounts
     * @return totalCount
     * @return err エラーメッセージ
     */
    function getAccountAllList(
        address contractManagerAddr,
        bytes32[] memory inAccountIds,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            ValidatorAccountsDataALL[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        if (limit == 0 || inAccountIds.length == 0) {
            return (new ValidatorAccountsDataALL[](0), _EMPTY_LENGTH, "");
        }
        if (limit > _MAX_LIMIT_1000) {
            return (
                new ValidatorAccountsDataALL[](0),
                _EMPTY_LENGTH,
                Error.ACCOUNT_TOO_LARGE_LIMIT
            );
        }
        if (offset >= inAccountIds.length) {
            return (
                new ValidatorAccountsDataALL[](0),
                _EMPTY_LENGTH,
                Error.ACCOUNT_OFFSET_OUT_OF_INDEX
            );
        }

        // 配列のサイズを作成する。
        // 要素数がoffsetとlimitの指定範囲以上の配列である場合はlimit分の配列を作成する
        // 要素数がoffsetとlimitの指定範囲未満の配列である場合は範囲分の項目数のみの配列を作成する
        uint256 size = (inAccountIds.length >= offset + limit)
            ? limit
            : inAccountIds.length - offset;

        accounts = new ValidatorAccountsDataALL[](size);

        for (uint256 i = 0; i < size; i++) {
            bytes32 accountId = inAccountIds[offset + i];
            AccountDataAll memory accountData;

            accounts[i].accountId = accountId;
            (accountData, ) = contractManager.account().getAccountAll(accountId);
            accounts[i].accountDataAll = accountData;
        }
        return (accounts, inAccountIds.length, "");
    }

    /**
     * @dev バリデータに紐づくAccountIdを返却する
     *
     * @param validatorMapping 検証者情報を保存するマッピング
     * @param validatorId マッピングのキーとなる検証者ID
     * @param sortOrder ソート順(desc: 降順, asc: 昇順)
     * @return accountIds アカウント情報の配列
     */
    function getAccountIdList(
        mapping(bytes32 => ValidatorData) storage validatorMapping,
        bytes32 validatorId,
        string memory sortOrder
    ) external view returns (bytes32[] memory accountIds) {
        bytes32[] memory ids = validatorMapping[validatorId].accountIds;
        uint256 n = ids.length;

        // sortOrderが"desc"の場合、配列を先頭から逆順に並び替える
        if (keccak256(bytes(sortOrder)) == _DESC_SORT) {
            uint256 idsLength = ids.length;
            for (uint256 i = 0; i < idsLength / 2; i++) {
                bytes32 temp = ids[i];
                ids[i] = ids[n - i - 1];
                ids[n - i - 1] = temp;
            }
        }

        return ids;
    }

    /**
     * @dev 検証者の権限を取得する
     *
     * @param validatorMapping 検証者データを保存するマッピング
     * @param validatorId マッピングのキーとなる検証者ID
     * @return role 検証者IDに紐づく検証者の権限
     */
    function getValidatorRole(
        mapping(bytes32 => ValidatorData) storage validatorMapping,
        bytes32 validatorId
    ) external view returns (bytes32 role) {
        return validatorMapping[validatorId].role;
    }

    /**
     * @dev 検証者IDを取得する
     *
     * @param validatorIdMapping 検証者IDを保存するマッピング
     * @param key 配列のindex
     * @return validatorId 検証者ID
     */
    function getValidatorIds(bytes32[] storage validatorIdMapping, uint256 key)
        external
        view
        returns (bytes32 validatorId)
    {
        return validatorIdMapping[key];
    }

    /**
     * @dev 検証者データを追加する
     *
     * @param validatorMapping validatorデータを保存するマッピング
     * @param validatorIdExistenceMapping マッピングのキーとなるvalidatorId
     * @param issuerIdLinkedFlagMapping issuerIdの存在マッピング
     * @param contractManagerAddr コントラクトマネジャーアドレス
     * @param validatorId validatorId
     * @param name validator 名
     * @param issuerId issuerId
     */
    function addValidator(
        mapping(bytes32 => ValidatorData) storage validatorMapping,
        mapping(bytes32 => bool) storage validatorIdExistenceMapping,
        mapping(bytes32 => bool) storage issuerIdLinkedFlagMapping,
        address contractManagerAddr,
        bytes32 validatorId,
        bytes32 name,
        bytes32 issuerId
    ) external {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        {
            //ValidatorIdの存在チェック
            require(validatorId != _EMPTY_VALUE, Error.VALIDATOR_INVALID_VAL);

            //Validatorが既に登録されているか確認を行う
            require(!validatorIdExistenceMapping[validatorId], Error.VALIDATOR_ID_EXIST);
        }
        {
            // zoneIdが登録されているか判別を行う。
            (uint16 zoneId, , string memory errZone) = contractManager.provider().getZone();
            require(bytes(errZone).length == 0, errZone);

            //共通領域の場合はIssuerIdのチェックを行う。
            if (zoneId == _FINANCIAL_ZONE) {
                //IssuerIdの存在チェック
                require(issuerId != _EMPTY_VALUE, Error.VALIDATOR_INVALID_VAL);
                //IssuerIdが未登録か確認を行う
                (bool success, string memory errIssuer) = contractManager.issuer().hasIssuer(
                    issuerId
                );
                require(success, errIssuer);

                //既にIssuerIDが紐付けられているか確認を行う
                require(!issuerIdLinkedFlagMapping[issuerId], Error.ISSUER_ID_EXIST);
            }
        }
        validatorMapping[validatorId].name = name;
        validatorMapping[validatorId].issuerId = issuerId;
        validatorIdExistenceMapping[validatorId] = true;
        issuerIdLinkedFlagMapping[issuerId] = true;
    }

    /**
     * @dev 検証者の権限を追加する
     *
     * @param validatorMapping 検証者データを保存するマッピング
     * @param validatorId マッピングのキーとなる検証者ID
     * @param role 検証者の権限
     * @param enabled 検証者の有効性.true:有効,false:無効
     */
    function addValidatorRole(
        mapping(bytes32 => ValidatorData) storage validatorMapping,
        bytes32 validatorId,
        bytes32 role,
        bool enabled
    ) external {
        validatorMapping[validatorId].role = role;
        validatorMapping[validatorId].enabled = enabled;
    }

    /**
     * @dev Accountを登録する(共通領域)。
     *
     * ```
     * emit event: AddAccount()
     * ```
     * @param validatorDataMapping validatorデータを保存するマッピング
     * @param accountIdExistenceByValidatorId accountIdとvalidatorIdの紐付きマッピング
     * @param contractManagerAddr コントラクトマネジャーアドレス
     * @param validatorId validatorId
     * @param accountId accountId
     * @param accountName account名
     * @param limitValues アカウントの限度額値
     * @param traceId トレースID
     */
    function addAccount(
        mapping(bytes32 => ValidatorData) storage validatorDataMapping,
        mapping(bytes32 => mapping(bytes32 => bool)) storage accountIdExistenceByValidatorId,
        address contractManagerAddr,
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        AccountLimitValues memory limitValues,
        bytes32 traceId
    ) external {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        // Validator未入力チェック
        (bool validatorSucess, string memory err) = contractManager.validator().hasValidator(
            validatorId
        );
        require(validatorSucess, err);

        // AccountIdが既に紐付けられているか確認を行う
        require(!accountIdExistenceByValidatorId[validatorId][accountId], Error.ACCOUNT_ID_EXIST);

        // Accountの登録を行う
        contractManager.account().addAccount(accountId, accountName, validatorId);
        contractManager.financialZoneAccount().addAccountLimit(accountId, limitValues, traceId);

        // Issuerとの紐付け処理
        {
            // Validatorに紐付いているIssuerを取得する。
            bytes32 issuerId = validatorDataMapping[validatorId].issuerId;

            // IssuerとAccountを紐付ける。
            contractManager.issuer().addAccountId(issuerId, accountId, traceId);
        }
        // ValidatorとAccountを紐付ける
        {
            validatorDataMapping[validatorId].accountIds.push(accountId);
            accountIdExistenceByValidatorId[validatorId][accountId] = true;
        }
    }

    /**
     * @dev バリデータが直接管理するアカウントIDを追加する
     *
     * @param validatorMapping 検証者データを保存するマッピング
     * @param contractManagerAddr コントラクトマネージャーアドレス
     * @param validatorId バリデータID
     * @param accountId アカウントID
     */
    function addValidatorAccountId(
        mapping(bytes32 => ValidatorData) storage validatorMapping,
        mapping(bytes32 => mapping(bytes32 => bool)) storage accountIdExistenceByValidatorId,
        address contractManagerAddr,
        bytes32 validatorId,
        bytes32 accountId
    ) external {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        bool success;
        string memory err;

        (success, err) = contractManager.validator().hasValidator(validatorId);
        require(success, err);

        (success, err) = contractManager.account().hasAccount(accountId);
        require(success, err);

        // accountIdが該当validatorId配下か確認を行う
        require(
            accountIdExistenceByValidatorId[validatorId][accountId],
            Error.ACCOUNT_ID_NOT_EXIST
        );

        validatorMapping[validatorId].validatorAccountId = accountId;
    }

    /**
     * @dev Account登録（付加領域用)。
     *
     * ```
     * emit event: SyncAccount()
     * ```
     * @param validatorDataMapping 検証者データを保存するマッピング
     * @param accountIdExistenceByValidatorId accountIdとvalidatorIdの紐付きマッピング
     * @param contractManagerAddr コントラクトマネジャーアドレス
     * @param validatorId validatorId
     * @param accountId accountId
     * @param accountStatus 口座のステータス
     */
    function syncAccount(
        mapping(bytes32 => ValidatorData) storage validatorDataMapping,
        mapping(bytes32 => mapping(bytes32 => bool)) storage accountIdExistenceByValidatorId,
        address contractManagerAddr,
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        bytes32 accountStatus,
        bytes32 reasonCode,
        bytes32 traceId
    ) external {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        if (accountStatus == _STATUS_APPLYING) {
            // Accountが既に紐付けられているか確認を行う
            require(
                !accountIdExistenceByValidatorId[validatorId][accountId],
                Error.ACCOUNT_ID_EXIST
            );
            contractManager.account().addAccount(accountId, accountName, validatorId);
            // ValidatorのAccountを登録する
            validatorDataMapping[validatorId].accountIds.push(accountId);
            accountIdExistenceByValidatorId[validatorId][accountId] = true;
        } else if (accountStatus == _STATUS_TERMINATING) {
            // Accountが既に紐付けられているか確認を行う
            require(
                accountIdExistenceByValidatorId[validatorId][accountId],
                Error.ACCOUNT_ID_NOT_EXIST
            );

            contractManager.validator().setTerminated(validatorId, accountId, reasonCode, traceId);
        }
    }

    /**
     * @dev 検証者データを変更する
     *
     * @param validatorMapping 検証者データを保存するマッピング
     * @param validatorId マッピングのキーとなる検証者ID
     * @param name 検証者の名前
     */
    function modValidator(
        mapping(bytes32 => ValidatorData) storage validatorMapping,
        bytes32 validatorId,
        bytes32 name
    ) external {
        validatorMapping[validatorId].name = name;
    }

    /**
     * @dev 検証者の存在確認。
     *
     * @param validatorIdExistenceMapping 検証者データを保存するマッピング
     * @param validatorId チェック対象となる検証者ID
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function hasValidator(
        mapping(bytes32 => bool) storage validatorIdExistenceMapping,
        bytes32 validatorId
    ) internal view returns (bool success, string memory err) {
        // ValidatorId入力確認
        if (validatorId == _EMPTY_VALUE) {
            return (false, Error.VALIDATOR_INVALID_VAL);
        }
        // Validator存在確認
        success = validatorIdExistenceMapping[validatorId];
        err = success ? "" : Error.VALIDATOR_ID_NOT_EXIST;

        return (success, err);
    }

    /**
     * @dev 指定されたValidatorIDにAccountが紐付いているか確認する
     *
     * @param accountIdExistenceByValidatorId 検証者データを保存するマッピング
     * @param validatorId チェック対象となるアカウントID
     * @param accountId  アカウントIDが検証者IDに紐付き済フラグ
     * @return success true:紐づいている,false:紐づいていない
     * @return err エラーメッセージ
     */
    function hasAccount(
        mapping(bytes32 => mapping(bytes32 => bool)) storage accountIdExistenceByValidatorId,
        bytes32 validatorId,
        bytes32 accountId
    ) internal view returns (bool success, string memory err) {
        // AccountID入力確認
        if (accountId == _EMPTY_VALUE) {
            return (false, Error.VALIDATOR_INVALID_VAL);
        }
        // Account存在確認
        if (!accountIdExistenceByValidatorId[validatorId][accountId]) {
            return (false, Error.ACCOUNT_ID_NOT_EXIST);
        }
        return (true, "");
    }
}
